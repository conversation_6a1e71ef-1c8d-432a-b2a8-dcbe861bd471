{"api/HotPreview.AutoGeneratePreviewAttribute.html": {"href": "api/HotPreview.AutoGeneratePreviewAttribute.html", "title": "Class AutoGeneratePreviewAttribute | Hot Preview Documentation", "summary": "Class AutoGeneratePreviewAttribute Namespace HotPreview Assembly HotPreview.dll Controls whether auto-generated previews should be created for a UI component. [AttributeUsage(AttributeTargets.Class)] public sealed class AutoGeneratePreviewAttribute : Attribute Inheritance object Attribute AutoGeneratePreviewAttribute Inherited Members Attribute.Equals(object) Attribute.GetCustomAttribute(Assembly, Type) Attribute.GetCustomAttribute(Assembly, Type, bool) Attribute.GetCustomAttribute(MemberInfo, Type) Attribute.GetCustomAttribute(MemberInfo, Type, bool) Attribute.GetCustomAttribute(Module, Type) Attribute.GetCustomAttribute(Module, Type, bool) Attribute.GetCustomAttribute(ParameterInfo, Type) Attribute.GetCustomAttribute(ParameterInfo, Type, bool) Attribute.GetCustomAttributes(Assembly) Attribute.GetCustomAttributes(Assembly, bool) Attribute.GetCustomAttributes(Assembly, Type) Attribute.GetCustomAttributes(Assembly, Type, bool) Attribute.GetCustomAttributes(MemberInfo) Attribute.GetCustomAttributes(MemberInfo, bool) Attribute.GetCustomAttributes(MemberInfo, Type) Attribute.GetCustomAttributes(MemberInfo, Type, bool) Attribute.GetCustomAttributes(Module) Attribute.GetCustomAttributes(Module, bool) Attribute.GetCustomAttributes(Module, Type) Attribute.GetCustomAttributes(Module, Type, bool) Attribute.GetCustomAttributes(ParameterInfo) Attribute.GetCustomAttributes(ParameterInfo, bool) Attribute.GetCustomAttributes(ParameterInfo, Type) Attribute.GetCustomAttributes(ParameterInfo, Type, bool) Attribute.GetHashCode() Attribute.IsDefaultAttribute() Attribute.IsDefined(Assembly, Type) Attribute.IsDefined(Assembly, Type, bool) Attribute.IsDefined(MemberInfo, Type) Attribute.IsDefined(MemberInfo, Type, bool) Attribute.IsDefined(Module, Type) Attribute.IsDefined(Module, Type, bool) Attribute.IsDefined(ParameterInfo, Type) Attribute.IsDefined(ParameterInfo, Type, bool) Attribute.Match(object) Attribute.TypeId object.Equals(object, object) object.GetType() object.ReferenceEquals(object, object) object.ToString() Remarks When present on a class and the autoGenerate property is false, auto-generation is disabled. Constructors AutoGeneratePreviewAttribute(bool) Controls whether auto-generated previews should be created for a UI component. public AutoGeneratePreviewAttribute(bool autoGenerate) Parameters autoGenerate bool Controls whether auto-generated previews should be created for this component. When set to false, auto-generation is disabled. Remarks When present on a class and the autoGenerate property is false, auto-generation is disabled. Properties AutoGenerate Controls whether auto-generated previews should be created for this component. When set to false, auto-generation is disabled. public bool AutoGenerate { get; } Property Value bool"}, "api/HotPreview.ControlUIComponentBaseTypeAttribute.html": {"href": "api/HotPreview.ControlUIComponentBaseTypeAttribute.html", "title": "Class ControlUIComponentBaseTypeAttribute | Hot Preview Documentation", "summary": "Class ControlUIComponentBaseTypeAttribute Namespace HotPreview Assembly HotPreview.dll Specifies the base type for control UI components on a specific platform. [AttributeUsage(AttributeTargets.Assembly, AllowMultiple = true)] public sealed class ControlUIComponentBaseTypeAttribute : Attribute Inheritance object Attribute ControlUIComponentBaseTypeAttribute Inherited Members Attribute.Equals(object) Attribute.GetCustomAttribute(Assembly, Type) Attribute.GetCustomAttribute(Assembly, Type, bool) Attribute.GetCustomAttribute(MemberInfo, Type) Attribute.GetCustomAttribute(MemberInfo, Type, bool) Attribute.GetCustomAttribute(Module, Type) Attribute.GetCustomAttribute(Module, Type, bool) Attribute.GetCustomAttribute(ParameterInfo, Type) Attribute.GetCustomAttribute(ParameterInfo, Type, bool) Attribute.GetCustomAttributes(Assembly) Attribute.GetCustomAttributes(Assembly, bool) Attribute.GetCustomAttributes(Assembly, Type) Attribute.GetCustomAttributes(Assembly, Type, bool) Attribute.GetCustomAttributes(MemberInfo) Attribute.GetCustomAttributes(MemberInfo, bool) Attribute.GetCustomAttributes(MemberInfo, Type) Attribute.GetCustomAttributes(MemberInfo, Type, bool) Attribute.GetCustomAttributes(Module) Attribute.GetCustomAttributes(Module, bool) Attribute.GetCustomAttributes(Module, Type) Attribute.GetCustomAttributes(Module, Type, bool) Attribute.GetCustomAttributes(ParameterInfo) Attribute.GetCustomAttributes(ParameterInfo, bool) Attribute.GetCustomAttributes(ParameterInfo, Type) Attribute.GetCustomAttributes(ParameterInfo, Type, bool) Attribute.GetHashCode() Attribute.IsDefaultAttribute() Attribute.IsDefined(Assembly, Type) Attribute.IsDefined(Assembly, Type, bool) Attribute.IsDefined(MemberInfo, Type) Attribute.IsDefined(MemberInfo, Type, bool) Attribute.IsDefined(Module, Type) Attribute.IsDefined(Module, Type, bool) Attribute.IsDefined(ParameterInfo, Type) Attribute.IsDefined(ParameterInfo, Type, bool) Attribute.Match(object) Attribute.TypeId object.Equals(object, object) object.GetType() object.ReferenceEquals(object, object) object.ToString() Constructors ControlUIComponentBaseTypeAttribute(string, string) Specifies the base type for control UI components on a specific platform. public ControlUIComponentBaseTypeAttribute(string platform, string baseType) Parameters platform string baseType string Properties BaseType public string BaseType { get; } Property Value string Platform public string Platform { get; } Property Value string"}, "api/HotPreview.NameUtilities.html": {"href": "api/HotPreview.NameUtilities.html", "title": "Class NameUtilities | Hot Preview Documentation", "summary": "Class NameUtilities Namespace HotPreview Assembly HotPreview.dll public class NameUtilities Inheritance object NameUtilities Inherited Members object.Equals(object) object.Equals(object, object) object.GetHashCode() object.GetType() object.MemberwiseClone() object.ReferenceEquals(object, object) object.ToString() Methods GetUnqualifiedName(string) public static string GetUnqualifiedName(string name) Parameters name string Returns string NormalizeTypeFullName(Type) public static string NormalizeTypeFullName(Type type) Parameters type Type Returns string"}, "api/HotPreview.PageUIComponentBaseTypeAttribute.html": {"href": "api/HotPreview.PageUIComponentBaseTypeAttribute.html", "title": "Class PageUIComponentBaseTypeAttribute | Hot Preview Documentation", "summary": "Class PageUIComponentBaseTypeAttribute Namespace HotPreview Assembly HotPreview.dll Specifies the base type for page UI components on a specific platform. [AttributeUsage(AttributeTargets.Assembly, AllowMultiple = true)] public sealed class PageUIComponentBaseTypeAttribute : Attribute Inheritance object Attribute PageUIComponentBaseTypeAttribute Inherited Members Attribute.Equals(object) Attribute.GetCustomAttribute(Assembly, Type) Attribute.GetCustomAttribute(Assembly, Type, bool) Attribute.GetCustomAttribute(MemberInfo, Type) Attribute.GetCustomAttribute(MemberInfo, Type, bool) Attribute.GetCustomAttribute(Module, Type) Attribute.GetCustomAttribute(Module, Type, bool) Attribute.GetCustomAttribute(ParameterInfo, Type) Attribute.GetCustomAttribute(ParameterInfo, Type, bool) Attribute.GetCustomAttributes(Assembly) Attribute.GetCustomAttributes(Assembly, bool) Attribute.GetCustomAttributes(Assembly, Type) Attribute.GetCustomAttributes(Assembly, Type, bool) Attribute.GetCustomAttributes(MemberInfo) Attribute.GetCustomAttributes(MemberInfo, bool) Attribute.GetCustomAttributes(MemberInfo, Type) Attribute.GetCustomAttributes(MemberInfo, Type, bool) Attribute.GetCustomAttributes(Module) Attribute.GetCustomAttributes(Module, bool) Attribute.GetCustomAttributes(Module, Type) Attribute.GetCustomAttributes(Module, Type, bool) Attribute.GetCustomAttributes(ParameterInfo) Attribute.GetCustomAttributes(ParameterInfo, bool) Attribute.GetCustomAttributes(ParameterInfo, Type) Attribute.GetCustomAttributes(ParameterInfo, Type, bool) Attribute.GetHashCode() Attribute.IsDefaultAttribute() Attribute.IsDefined(Assembly, Type) Attribute.IsDefined(Assembly, Type, bool) Attribute.IsDefined(MemberInfo, Type) Attribute.IsDefined(MemberInfo, Type, bool) Attribute.IsDefined(Module, Type) Attribute.IsDefined(Module, Type, bool) Attribute.IsDefined(ParameterInfo, Type) Attribute.IsDefined(ParameterInfo, Type, bool) Attribute.Match(object) Attribute.TypeId object.Equals(object, object) object.GetType() object.ReferenceEquals(object, object) object.ToString() Constructors PageUIComponentBaseTypeAttribute(string, string) Specifies the base type for page UI components on a specific platform. public PageUIComponentBaseTypeAttribute(string platform, string baseType) Parameters platform string baseType string Properties BaseType public string BaseType { get; } Property Value string Platform public string Platform { get; } Property Value string"}, "api/HotPreview.PreviewAttribute-1.html": {"href": "api/HotPreview.PreviewAttribute-1.html", "title": "Class PreviewAttribute<TUIComponent> | Hot Preview Documentation", "summary": "Class PreviewAttribute<TUIComponent> Namespace HotPreview Assembly HotPreview.dll Specifies that this static method creates a preview for a UI component with an explicitly specified UI component type. [AttributeUsage(AttributeTargets.Class|AttributeTargets.Method)] public sealed class PreviewAttribute<TUIComponent> : PreviewAttribute Type Parameters TUIComponent The UI component type that this preview is associated with. Inheritance object Attribute PreviewAttribute PreviewAttribute<TUIComponent> Inherited Members PreviewAttribute.DisplayName PreviewAttribute.UIComponentType Attribute.Equals(object) Attribute.GetCustomAttribute(Assembly, Type) Attribute.GetCustomAttribute(Assembly, Type, bool) Attribute.GetCustomAttribute(MemberInfo, Type) Attribute.GetCustomAttribute(MemberInfo, Type, bool) Attribute.GetCustomAttribute(Module, Type) Attribute.GetCustomAttribute(Module, Type, bool) Attribute.GetCustomAttribute(ParameterInfo, Type) Attribute.GetCustomAttribute(ParameterInfo, Type, bool) Attribute.GetCustomAttributes(Assembly) Attribute.GetCustomAttributes(Assembly, bool) Attribute.GetCustomAttributes(Assembly, Type) Attribute.GetCustomAttributes(Assembly, Type, bool) Attribute.GetCustomAttributes(MemberInfo) Attribute.GetCustomAttributes(MemberInfo, bool) Attribute.GetCustomAttributes(MemberInfo, Type) Attribute.GetCustomAttributes(MemberInfo, Type, bool) Attribute.GetCustomAttributes(Module) Attribute.GetCustomAttributes(Module, bool) Attribute.GetCustomAttributes(Module, Type) Attribute.GetCustomAttributes(Module, Type, bool) Attribute.GetCustomAttributes(ParameterInfo) Attribute.GetCustomAttributes(ParameterInfo, bool) Attribute.GetCustomAttributes(ParameterInfo, Type) Attribute.GetCustomAttributes(ParameterInfo, Type, bool) Attribute.GetHashCode() Attribute.IsDefaultAttribute() Attribute.IsDefined(Assembly, Type) Attribute.IsDefined(Assembly, Type, bool) Attribute.IsDefined(MemberInfo, Type) Attribute.IsDefined(MemberInfo, Type, bool) Attribute.IsDefined(Module, Type) Attribute.IsDefined(Module, Type, bool) Attribute.IsDefined(ParameterInfo, Type) Attribute.IsDefined(ParameterInfo, Type, bool) Attribute.Match(object) Attribute.TypeId object.Equals(object, object) object.GetType() object.ReferenceEquals(object, object) object.ToString() Remarks This generic version is only needed when it's necessary to explicitly specify the UI component type. The type parameter TUIComponent specifies the UI component type that this preview is associated with. The method should return the preview UI object and the Hot Preview platform code will then navigate to that preview. If the method navigates to the preview itself (often true for a single page app) it can return void. Constructors PreviewAttribute(string?) Specifies that this static method creates a preview for a UI component with an explicitly specified UI component type. public PreviewAttribute(string? displayName = null) Parameters displayName string Optional override for the display name of the preview, determining how it appears in the navigation UI. If not specified, the name of the method (or class, for class-based previews) is used, converted to start case (e.g. \"MyPreview\" becomes \"My Preview\"). Storybook also uses this same start case convention. Remarks This generic version is only needed when it's necessary to explicitly specify the UI component type. The type parameter TUIComponent specifies the UI component type that this preview is associated with. The method should return the preview UI object and the Hot Preview platform code will then navigate to that preview. If the method navigates to the preview itself (often true for a single page app) it can return void."}, "api/HotPreview.PreviewAttribute.html": {"href": "api/HotPreview.PreviewAttribute.html", "title": "Class PreviewAttribute | Hot Preview Documentation", "summary": "Class PreviewAttribute Namespace HotPreview Assembly HotPreview.dll Specifies that this static method creates a preview for a UI component. [AttributeUsage(AttributeTargets.Class|AttributeTargets.Method)] public class PreviewAttribute : Attribute Inheritance object Attribute PreviewAttribute Derived PreviewAttribute<TUIComponent> Inherited Members Attribute.Equals(object) Attribute.GetCustomAttribute(Assembly, Type) Attribute.GetCustomAttribute(Assembly, Type, bool) Attribute.GetCustomAttribute(MemberInfo, Type) Attribute.GetCustomAttribute(MemberInfo, Type, bool) Attribute.GetCustomAttribute(Module, Type) Attribute.GetCustomAttribute(Module, Type, bool) Attribute.GetCustomAttribute(ParameterInfo, Type) Attribute.GetCustomAttribute(ParameterInfo, Type, bool) Attribute.GetCustomAttributes(Assembly) Attribute.GetCustomAttributes(Assembly, bool) Attribute.GetCustomAttributes(Assembly, Type) Attribute.GetCustomAttributes(Assembly, Type, bool) Attribute.GetCustomAttributes(MemberInfo) Attribute.GetCustomAttributes(MemberInfo, bool) Attribute.GetCustomAttributes(MemberInfo, Type) Attribute.GetCustomAttributes(MemberInfo, Type, bool) Attribute.GetCustomAttributes(Module) Attribute.GetCustomAttributes(Module, bool) Attribute.GetCustomAttributes(Module, Type) Attribute.GetCustomAttributes(Module, Type, bool) Attribute.GetCustomAttributes(ParameterInfo) Attribute.GetCustomAttributes(ParameterInfo, bool) Attribute.GetCustomAttributes(ParameterInfo, Type) Attribute.GetCustomAttributes(ParameterInfo, Type, bool) Attribute.GetHashCode() Attribute.IsDefaultAttribute() Attribute.IsDefined(Assembly, Type) Attribute.IsDefined(Assembly, Type, bool) Attribute.IsDefined(MemberInfo, Type) Attribute.IsDefined(MemberInfo, Type, bool) Attribute.IsDefined(Module, Type) Attribute.IsDefined(Module, Type, bool) Attribute.IsDefined(ParameterInfo, Type) Attribute.IsDefined(ParameterInfo, Type, bool) Attribute.Match(object) Attribute.TypeId object.Equals(object, object) object.GetType() object.MemberwiseClone() object.ReferenceEquals(object, object) object.ToString() Remarks The method should return the preview UI object and the Hot Preview platform code will then navigate to that preview. If the method navigates to the preview itself (often true for a single page app) it can return void. The UI component type is automatically inferred from the method return type or, if the method returns void, from the containing class. If you need to explicitly specify a different UI component type, use the generic PreviewAttribute<TUIComponent> instead. Constructors PreviewAttribute(string?) Specifies that this static method creates a preview for a UI component. public PreviewAttribute(string? displayName = null) Parameters displayName string Optional override for the display name of the preview, determining how it appears in the navigation UI. If not specified, the name of the method (or class, for class-based previews) is used, converted to start case (e.g. \"MyPreview\" becomes \"My Preview\"). Storybook also uses this start case convention. Remarks The method should return the preview UI object and the Hot Preview platform code will then navigate to that preview. If the method navigates to the preview itself (often true for a single page app) it can return void. The UI component type is automatically inferred from the method return type or, if the method returns void, from the containing class. If you need to explicitly specify a different UI component type, use the generic PreviewAttribute<TUIComponent> instead. PreviewAttribute(string?, Type?) protected PreviewAttribute(string? displayName, Type? uiComponentType) Parameters displayName string uiComponentType Type Properties DisplayName Optional override for the display name of the preview, determining how it appears in the navigation UI. If not specified, the name of the method (or class, for class-based previews) is used, converted to start case (e.g. \"MyPreview\" becomes \"My Preview\"). Storybook also uses this start case convention. public string? DisplayName { get; } Property Value string UIComponentType public Type? UIComponentType { get; } Property Value Type"}, "api/HotPreview.PreviewCommandAttribute.html": {"href": "api/HotPreview.PreviewCommandAttribute.html", "title": "Class PreviewCommandAttribute | Hot Preview Documentation", "summary": "Class PreviewCommandAttribute Namespace HotPreview Assembly HotPreview.dll Designates this method as a command that can be executed from the DevTools UI or as an MCP tool command. [AttributeUsage(AttributeTargets.Method)] public sealed class PreviewCommandAttribute : Attribute Inheritance object Attribute PreviewCommandAttribute Inherited Members Attribute.Equals(object) Attribute.GetCustomAttribute(Assembly, Type) Attribute.GetCustomAttribute(Assembly, Type, bool) Attribute.GetCustomAttribute(MemberInfo, Type) Attribute.GetCustomAttribute(MemberInfo, Type, bool) Attribute.GetCustomAttribute(Module, Type) Attribute.GetCustomAttribute(Module, Type, bool) Attribute.GetCustomAttribute(ParameterInfo, Type) Attribute.GetCustomAttribute(ParameterInfo, Type, bool) Attribute.GetCustomAttributes(Assembly) Attribute.GetCustomAttributes(Assembly, bool) Attribute.GetCustomAttributes(Assembly, Type) Attribute.GetCustomAttributes(Assembly, Type, bool) Attribute.GetCustomAttributes(MemberInfo) Attribute.GetCustomAttributes(MemberInfo, bool) Attribute.GetCustomAttributes(MemberInfo, Type) Attribute.GetCustomAttributes(MemberInfo, Type, bool) Attribute.GetCustomAttributes(Module) Attribute.GetCustomAttributes(Module, bool) Attribute.GetCustomAttributes(Module, Type) Attribute.GetCustomAttributes(Module, Type, bool) Attribute.GetCustomAttributes(ParameterInfo) Attribute.GetCustomAttributes(ParameterInfo, bool) Attribute.GetCustomAttributes(ParameterInfo, Type) Attribute.GetCustomAttributes(ParameterInfo, Type, bool) Attribute.GetHashCode() Attribute.IsDefaultAttribute() Attribute.IsDefined(Assembly, Type) Attribute.IsDefined(Assembly, Type, bool) Attribute.IsDefined(MemberInfo, Type) Attribute.IsDefined(MemberInfo, Type, bool) Attribute.IsDefined(Module, Type) Attribute.IsDefined(Module, Type, bool) Attribute.IsDefined(ParameterInfo, Type) Attribute.IsDefined(ParameterInfo, Type, bool) Attribute.Match(object) Attribute.TypeId object.Equals(object, object) object.GetType() object.ReferenceEquals(object, object) object.ToString() Remarks Commands typically update global state, changing the way that subsequent previews appear. For instance, commands could update the UI language for the app or switch the theme between light and dark. Commands normally don't update UI themselves (but they can - nothing prevents this). Commands for now should have no parameters and return void, though likely we'll add parameter and return support in the future once we've figured out the desired behavior. Constructors PreviewCommandAttribute(string?) Designates this method as a command that can be executed from the DevTools UI or as an MCP tool command. public PreviewCommandAttribute(string? displayName = null) Parameters displayName string Optional display name override for the command, determining how it appears in navigation UI. If not specified, the name of the method is used. Remarks Commands typically update global state, changing the way that subsequent previews appear. For instance, commands could update the UI language for the app or switch the theme between light and dark. Commands normally don't update UI themselves (but they can - nothing prevents this). Commands for now should have no parameters and return void, though likely we'll add parameter and return support in the future once we've figured out the desired behavior. Properties DisplayName Optional display name override for the command, determining how it appears in navigation UI. If not specified, the name of the method is used. public string? DisplayName { get; } Property Value string"}, "api/HotPreview.PreviewMode.html": {"href": "api/HotPreview.PreviewMode.html", "title": "Enum PreviewMode | Hot Preview Documentation", "summary": "Enum PreviewMode Namespace HotPreview Assembly HotPreview.dll public enum PreviewMode Fields Gallery = 1 None = 0 RemoteControl = 2"}, "api/HotPreview.PreviewssAttribute.html": {"href": "api/HotPreview.PreviewssAttribute.html", "title": "Class PreviewssAttribute | Hot Preview Documentation", "summary": "Class PreviewssAttribute Namespace HotPreview Assembly HotPreview.dll An attribute that specifies metadata for UI component that has previews. It can be used explicitly specify a Title, overriding the default title of the type name. [AttributeUsage(AttributeTargets.Class|AttributeTargets.Interface)] public sealed class PreviewssAttribute : Attribute Inheritance object Attribute PreviewssAttribute Inherited Members Attribute.Equals(object) Attribute.GetCustomAttribute(Assembly, Type) Attribute.GetCustomAttribute(Assembly, Type, bool) Attribute.GetCustomAttribute(MemberInfo, Type) Attribute.GetCustomAttribute(MemberInfo, Type, bool) Attribute.GetCustomAttribute(Module, Type) Attribute.GetCustomAttribute(Module, Type, bool) Attribute.GetCustomAttribute(ParameterInfo, Type) Attribute.GetCustomAttribute(ParameterInfo, Type, bool) Attribute.GetCustomAttributes(Assembly) Attribute.GetCustomAttributes(Assembly, bool) Attribute.GetCustomAttributes(Assembly, Type) Attribute.GetCustomAttributes(Assembly, Type, bool) Attribute.GetCustomAttributes(MemberInfo) Attribute.GetCustomAttributes(MemberInfo, bool) Attribute.GetCustomAttributes(MemberInfo, Type) Attribute.GetCustomAttributes(MemberInfo, Type, bool) Attribute.GetCustomAttributes(Module) Attribute.GetCustomAttributes(Module, bool) Attribute.GetCustomAttributes(Module, Type) Attribute.GetCustomAttributes(Module, Type, bool) Attribute.GetCustomAttributes(ParameterInfo) Attribute.GetCustomAttributes(ParameterInfo, bool) Attribute.GetCustomAttributes(ParameterInfo, Type) Attribute.GetCustomAttributes(ParameterInfo, Type, bool) Attribute.GetHashCode() Attribute.IsDefaultAttribute() Attribute.IsDefined(Assembly, Type) Attribute.IsDefined(Assembly, Type, bool) Attribute.IsDefined(MemberInfo, Type) Attribute.IsDefined(MemberInfo, Type, bool) Attribute.IsDefined(Module, Type) Attribute.IsDefined(Module, Type, bool) Attribute.IsDefined(ParameterInfo, Type) Attribute.IsDefined(ParameterInfo, Type, bool) Attribute.Match(object) Attribute.TypeId object.Equals(object, object) object.GetType() object.ReferenceEquals(object, object) object.ToString() Constructors PreviewssAttribute() public PreviewssAttribute() PreviewssAttribute(string) public PreviewssAttribute(string title) Parameters title string Properties Title Optional title for the preview, determining how it appears in navigation UI. \"/\" delimiters can be used to indicate hierarchy. public string? Title { get; } Property Value string"}, "api/HotPreview.RoutePreview-1.html": {"href": "api/HotPreview.RoutePreview-1.html", "title": "Class RoutePreview<T> | Hot Preview Documentation", "summary": "Class RoutePreview<T> Namespace HotPreview Assembly HotPreview.dll Represents a strongly-typed preview for a route-based navigation. public class RoutePreview<T> : RoutePreview where T : class Type Parameters T The type associated with this route preview. Inheritance object RoutePreview RoutePreview<T> Inherited Members RoutePreview.Route object.Equals(object) object.Equals(object, object) object.GetHashCode() object.GetType() object.MemberwiseClone() object.ReferenceEquals(object, object) object.ToString() Constructors RoutePreview(string) Represents a strongly-typed preview for a route-based navigation. public RoutePreview(string route) Parameters route string"}, "api/HotPreview.RoutePreview.html": {"href": "api/HotPreview.RoutePreview.html", "title": "Class RoutePreview | Hot Preview Documentation", "summary": "Class RoutePreview Namespace HotPreview Assembly HotPreview.dll Represents a preview for a route-based navigation. public class RoutePreview Inheritance object RoutePreview Derived RoutePreview<T> Inherited Members object.Equals(object) object.Equals(object, object) object.GetHashCode() object.GetType() object.MemberwiseClone() object.ReferenceEquals(object, object) object.ToString() Constructors RoutePreview(string) Represents a preview for a route-based navigation. public RoutePreview(string route) Parameters route string Properties Route public string Route { get; } Property Value string"}, "api/HotPreview.SharedModel.App.GetPreviewsViaReflection.html": {"href": "api/HotPreview.SharedModel.App.GetPreviewsViaReflection.html", "title": "Class GetPreviewsViaReflection | Hot Preview Documentation", "summary": "Class GetPreviewsViaReflection Namespace HotPreview.SharedModel.App Assembly HotPreview.SharedModel.dll public class GetPreviewsViaReflection : PreviewsManagerBuilderBase<UIComponentReflection, PreviewReflection, PreviewCommandReflection> Inheritance object PreviewsManagerBuilderBase<UIComponentReflection, PreviewReflection, PreviewCommandReflection> GetPreviewsViaReflection Inherited Members PreviewsManagerBuilderBase<UIComponentReflection, PreviewReflection, PreviewCommandReflection>._uiComponentsByName PreviewsManagerBuilderBase<UIComponentReflection, PreviewReflection, PreviewCommandReflection>._categories PreviewsManagerBuilderBase<UIComponentReflection, PreviewReflection, PreviewCommandReflection>._baseTypes PreviewsManagerBuilderBase<UIComponentReflection, PreviewReflection, PreviewCommandReflection>._commandsByName PreviewsManagerBuilderBase<UIComponentReflection, PreviewReflection, PreviewCommandReflection>.UIComponentsByName PreviewsManagerBuilderBase<UIComponentReflection, PreviewReflection, PreviewCommandReflection>.Categories PreviewsManagerBuilderBase<UIComponentReflection, PreviewReflection, PreviewCommandReflection>.CommandsByName PreviewsManagerBuilderBase<UIComponentReflection, PreviewReflection, PreviewCommandReflection>.AddUIComponentBaseType(UIComponentKind, string, string) PreviewsManagerBuilderBase<UIComponentReflection, PreviewReflection, PreviewCommandReflection>.AddUIComponent(UIComponentReflection) PreviewsManagerBuilderBase<UIComponentReflection, PreviewReflection, PreviewCommandReflection>.AddOrUpdateUIComponent(UIComponentReflection) PreviewsManagerBuilderBase<UIComponentReflection, PreviewReflection, PreviewCommandReflection>.AddCommand(PreviewCommandReflection) PreviewsManagerBuilderBase<UIComponentReflection, PreviewReflection, PreviewCommandReflection>.AddOrUpdateCommand(PreviewCommandReflection) PreviewsManagerBuilderBase<UIComponentReflection, PreviewReflection, PreviewCommandReflection>.AddCategory(UIComponentCategory) PreviewsManagerBuilderBase<UIComponentReflection, PreviewReflection, PreviewCommandReflection>.AddOrUpdateCategory(string, IReadOnlyList<string>) PreviewsManagerBuilderBase<UIComponentReflection, PreviewReflection, PreviewCommandReflection>.GetUIComponent(string) PreviewsManagerBuilderBase<UIComponentReflection, PreviewReflection, PreviewCommandReflection>.GetCommand(string) PreviewsManagerBuilderBase<UIComponentReflection, PreviewReflection, PreviewCommandReflection>.IsUIComponentBaseType(string, out UIComponentKind) PreviewsManagerBuilderBase<UIComponentReflection, PreviewReflection, PreviewCommandReflection>.Validate() object.Equals(object) object.Equals(object, object) object.GetHashCode() object.GetType() object.MemberwiseClone() object.ReferenceEquals(object, object) object.ToString() Constructors GetPreviewsViaReflection(IServiceProvider?, Assembly?, IEnumerable<string>, IUIComponentExclusionFilter?) Initializes a new instance of GetPreviewsViaReflection and processes assemblies to discover UI components via reflection. public GetPreviewsViaReflection(IServiceProvider? serviceProvider, Assembly? mainAssembly, IEnumerable<string> additionalAppAssemblies, IUIComponentExclusionFilter? exclusionFilter) Parameters serviceProvider IServiceProvider An optional IServiceProvider instance for dependency injection mainAssembly Assembly additionalAppAssemblies IEnumerable<string> Additional app assembly names to scan exclusionFilter IUIComponentExclusionFilter Optional filter to exclude certain types Methods ToImmutable() Creates an immutable PreviewsManagerReflection from the builder's current state. public PreviewsManagerReflection ToImmutable() Returns PreviewsManagerReflection An immutable PreviewsManagerReflection containing all the builder's data"}, "api/HotPreview.SharedModel.App.IPreviewNavigator.html": {"href": "api/HotPreview.SharedModel.App.IPreviewNavigator.html", "title": "Interface IPreviewNavigator | Hot Preview Documentation", "summary": "Interface IPreviewNavigator Namespace HotPreview.SharedModel.App Assembly HotPreview.SharedModel.dll public interface IPreviewNavigator Methods GetPreviewSnapshotAsync(UIComponentReflection, PreviewReflection) Task<byte[]> GetPreviewSnapshotAsync(UIComponentReflection uiComponent, PreviewReflection preview) Parameters uiComponent UIComponentReflection preview PreviewReflection Returns Task<byte[]> NavigateToPreviewAsync(UIComponentReflection, PreviewReflection) Task NavigateToPreviewAsync(UIComponentReflection uiComponent, PreviewReflection preview) Parameters uiComponent UIComponentReflection preview PreviewReflection Returns Task"}, "api/HotPreview.SharedModel.App.PreviewAppService.html": {"href": "api/HotPreview.SharedModel.App.PreviewAppService.html", "title": "Class PreviewAppService | Hot Preview Documentation", "summary": "Class PreviewAppService Namespace HotPreview.SharedModel.App Assembly HotPreview.SharedModel.dll public abstract class PreviewAppService : IPreviewAppService Inheritance object PreviewAppService Implements IPreviewAppService Inherited Members object.Equals(object) object.Equals(object, object) object.GetHashCode() object.GetType() object.MemberwiseClone() object.ReferenceEquals(object, object) object.ToString() Constructors PreviewAppService(PreviewApplication) protected PreviewAppService(PreviewApplication previewApplication) Parameters previewApplication PreviewApplication Properties PreviewApplication public PreviewApplication PreviewApplication { get; } Property Value PreviewApplication Methods GetCommand(string) protected PreviewCommandReflection GetCommand(string commandName) Parameters commandName string Returns PreviewCommandReflection GetCommandIfExists(string) protected PreviewCommandReflection? GetCommandIfExists(string commandName) Parameters commandName string Returns PreviewCommandReflection GetCommandsAsync() public Task<PreviewCommandInfo[]> GetCommandsAsync() Returns Task<PreviewCommandInfo[]> GetPreviewSnapshotAsync(string, string) public Task<byte[]> GetPreviewSnapshotAsync(string uiComponentName, string previewName) Parameters uiComponentName string previewName string Returns Task<byte[]> GetUIComponent(string) protected UIComponentReflection GetUIComponent(string uiComponentName) Parameters uiComponentName string Returns UIComponentReflection GetUIComponentIfExists(string) protected UIComponentReflection? GetUIComponentIfExists(string uiComponentName) Parameters uiComponentName string Returns UIComponentReflection GetUIComponentPreviewPair(string, string) protected UIComponentPreviewPairReflection GetUIComponentPreviewPair(string uiComponentName, string previewName) Parameters uiComponentName string previewName string Returns UIComponentPreviewPairReflection GetUIComponentPreviewsAsync(string) public Task<string[]> GetUIComponentPreviewsAsync(string componentName) Parameters componentName string Returns Task<string[]> GetUIComponentsAsync() public Task<UIComponentInfo[]> GetUIComponentsAsync() Returns Task<UIComponentInfo[]> InvokeCommandAsync(string) public Task InvokeCommandAsync(string commandName) Parameters commandName string Returns Task NavigateToPreviewAsync(string, string) public Task NavigateToPreviewAsync(string uiComponentName, string previewName) Parameters uiComponentName string previewName string Returns Task"}, "api/HotPreview.SharedModel.App.PreviewApplication.html": {"href": "api/HotPreview.SharedModel.App.PreviewApplication.html", "title": "Class PreviewApplication | Hot Preview Documentation", "summary": "Class PreviewApplication Namespace HotPreview.SharedModel.App Assembly HotPreview.SharedModel.dll public abstract class PreviewApplication : IDisposable Inheritance object PreviewApplication Implements IDisposable Inherited Members object.Equals(object) object.Equals(object, object) object.GetHashCode() object.GetType() object.MemberwiseClone() object.ReferenceEquals(object, object) object.ToString() Properties AdditionalAppAssemblies public IEnumerable<string> AdditionalAppAssemblies { get; } Property Value IEnumerable<string> EnableJsonRpcTracing Gets or sets whether JsonRpc diagnostic tracing is enabled. When true, JsonRpc communication will be traced to the console. public bool EnableJsonRpcTracing { get; set; } Property Value bool MainAssembly public Assembly? MainAssembly { get; set; } Property Value Assembly PlatformName public abstract string PlatformName { get; set; } Property Value string ProjectPath public string? ProjectPath { get; set; } Property Value string ServiceProvider The app's service provider, which when present can be used to instantiate UI components via dependency injection. public IServiceProvider? ServiceProvider { get; set; } Property Value IServiceProvider ToolingConnectionString public string? ToolingConnectionString { get; set; } Property Value string Methods AddAdditionalAppAssembly(string) public void AddAdditionalAppAssembly(string assemblyName) Parameters assemblyName string Dispose() public void Dispose() Dispose(bool) protected virtual void Dispose(bool disposing) Parameters disposing bool GetInstance() public static PreviewApplication GetInstance() Returns PreviewApplication GetPreviewAppService() public abstract PreviewAppService GetPreviewAppService() Returns PreviewAppService GetPreviewNavigator() public abstract IPreviewNavigator GetPreviewNavigator() Returns IPreviewNavigator GetPreviewsManager() public abstract PreviewsManagerReflection GetPreviewsManager() Returns PreviewsManagerReflection GetRequiredService<TService>() public TService GetRequiredService<TService>() where TService : class Returns TService Type Parameters TService InitInstance(PreviewApplication) protected static void InitInstance(PreviewApplication instance) Parameters instance PreviewApplication StartToolingConnection() public void StartToolingConnection() StopToolingConnection() public void StopToolingConnection() TransformConnectionStringForPlatform(string) Transforms a tooling connection string to be appropriate for the current platform. By default, returns the input string unchanged. Platform-specific overrides can adjust the connection string (such as IP addresses or ports) to match the requirements or conventions of the target platform or device. public virtual string TransformConnectionStringForPlatform(string connectionString) Parameters connectionString string Returns string"}, "api/HotPreview.SharedModel.App.PreviewClassReflection.html": {"href": "api/HotPreview.SharedModel.App.PreviewClassReflection.html", "title": "Class PreviewClassReflection | Hot Preview Documentation", "summary": "Class PreviewClassReflection Namespace HotPreview.SharedModel.App Assembly HotPreview.SharedModel.dll public class PreviewClassReflection : PreviewReflection Inheritance object PreviewBase PreviewReflection PreviewClassReflection Inherited Members PreviewReflection.UIComponentType PreviewReflection.GetPreviewInfo() PreviewBase.DisplayName PreviewBase.DisplayNameOverride object.Equals(object) object.Equals(object, object) object.GetHashCode() object.GetType() object.MemberwiseClone() object.ReferenceEquals(object, object) object.ToString() Constructors PreviewClassReflection(PreviewAttribute, Type) public PreviewClassReflection(PreviewAttribute previewAttribute, Type type) Parameters previewAttribute PreviewAttribute type Type PreviewClassReflection(Type, bool) public PreviewClassReflection(Type type, bool isAutoGenerated) Parameters type Type isAutoGenerated bool Properties DefaultUIComponentType Default component type (when there is one), e.g. based on the method return type. If there's no default type, this will be null. public override Type? DefaultUIComponentType { get; } Property Value Type IsAutoGenerated public override bool IsAutoGenerated { get; } Property Value bool Name Name is intended to be used by code to uniquely identify the preview. It's the preview's full qualified method name. public override string Name { get; } Property Value string Type public Type Type { get; } Property Value Type Methods Create() Create an instance of the preview. Normally this returns an instance of a UI framework control/page, suitable for display. public override object Create() Returns object instantiated preview GetPreviewTypeInfo() Gets the preview type (e.g., \"Class\", \"StaticMethod\"). public override string GetPreviewTypeInfo() Returns string A string representing the preview type"}, "api/HotPreview.SharedModel.App.PreviewCommandReflection.html": {"href": "api/HotPreview.SharedModel.App.PreviewCommandReflection.html", "title": "Class PreviewCommandReflection | Hot Preview Documentation", "summary": "Class PreviewCommandReflection Namespace HotPreview.SharedModel.App Assembly HotPreview.SharedModel.dll Reflection-based command implementation for static methods marked with [PreviewCommand]. public class PreviewCommandReflection : PreviewCommandBase Inheritance object PreviewCommandBase PreviewCommandReflection Inherited Members PreviewCommandBase.DisplayName PreviewCommandBase.DisplayNameOverride object.Equals(object) object.Equals(object, object) object.GetHashCode() object.GetType() object.MemberwiseClone() object.ReferenceEquals(object, object) object.ToString() Constructors PreviewCommandReflection(PreviewCommandAttribute, MethodInfo) Reflection-based command implementation for static methods marked with [PreviewCommand]. public PreviewCommandReflection(PreviewCommandAttribute commandAttribute, MethodInfo methodInfo) Parameters commandAttribute PreviewCommandAttribute methodInfo MethodInfo Properties MethodInfo public MethodInfo MethodInfo { get; } Property Value MethodInfo Name FullName is intended to be what's used by the code to identify the command. It's the command's full qualified method name. public override string Name { get; } Property Value string Methods Execute() Execute the command by invoking the static method. public void Execute()"}, "api/HotPreview.SharedModel.App.PreviewReflection.html": {"href": "api/HotPreview.SharedModel.App.PreviewReflection.html", "title": "Class PreviewReflection | Hot Preview Documentation", "summary": "Class PreviewReflection Namespace HotPreview.SharedModel.App Assembly HotPreview.SharedModel.dll public abstract class PreviewReflection : PreviewBase Inheritance object PreviewBase PreviewReflection Derived PreviewClassReflection PreviewStaticMethodReflection Inherited Members PreviewBase.IsAutoGenerated PreviewBase.DisplayName PreviewBase.DisplayNameOverride PreviewBase.Name object.Equals(object) object.Equals(object, object) object.GetHashCode() object.GetType() object.MemberwiseClone() object.ReferenceEquals(object, object) object.ToString() Constructors PreviewReflection(PreviewAttribute) public PreviewReflection(PreviewAttribute previewAttribute) Parameters previewAttribute PreviewAttribute PreviewReflection(Type) public PreviewReflection(Type uiComponentType) Parameters uiComponentType Type Properties DefaultUIComponentType Default component type (when there is one), e.g. based on the method return type. If there's no default type, this will be null. public abstract Type? DefaultUIComponentType { get; } Property Value Type UIComponentType public Type UIComponentType { get; } Property Value Type Methods Create() Create an instance of the preview. Normally this returns an instance of a UI framework control/page, suitable for display. public abstract object Create() Returns object instantiated preview GetPreviewInfo() Gets the preview information including name, display name, auto-generated status, and preview type. public virtual PreviewInfo GetPreviewInfo() Returns PreviewInfo A PreviewInfo record with the preview details, for use in the JSON RPC protocol GetPreviewTypeInfo() Gets the preview type (e.g., \"Class\", \"StaticMethod\"). public abstract string GetPreviewTypeInfo() Returns string A string representing the preview type"}, "api/HotPreview.SharedModel.App.PreviewStaticMethodReflection.html": {"href": "api/HotPreview.SharedModel.App.PreviewStaticMethodReflection.html", "title": "Class PreviewStaticMethodReflection | Hot Preview Documentation", "summary": "Class PreviewStaticMethodReflection Namespace HotPreview.SharedModel.App Assembly HotPreview.SharedModel.dll public class PreviewStaticMethodReflection : PreviewReflection Inheritance object PreviewBase PreviewReflection PreviewStaticMethodReflection Inherited Members PreviewReflection.UIComponentType PreviewReflection.GetPreviewInfo() PreviewBase.IsAutoGenerated PreviewBase.DisplayName PreviewBase.DisplayNameOverride object.Equals(object) object.Equals(object, object) object.GetHashCode() object.GetType() object.MemberwiseClone() object.ReferenceEquals(object, object) object.ToString() Constructors PreviewStaticMethodReflection(PreviewAttribute, MethodInfo) public PreviewStaticMethodReflection(PreviewAttribute previewAttribute, MethodInfo methodInfo) Parameters previewAttribute PreviewAttribute methodInfo MethodInfo Properties DefaultUIComponentType Default component type (when there is one), e.g. based on the method return type. If there's no default type, this will be null. public override Type? DefaultUIComponentType { get; } Property Value Type MethodInfo public MethodInfo MethodInfo { get; } Property Value MethodInfo Name FullName is intended to be what's used by the code to identify the preview. It's the preview's full qualified method name. public override string Name { get; } Property Value string Methods Create() Create an instance of the preview. Normally this returns an instance of a UI framework control/page, suitable for display. public override object Create() Returns object instantiated preview GetPreviewTypeInfo() Gets the preview type (e.g., \"Class\", \"StaticMethod\"). public override string GetPreviewTypeInfo() Returns string A string representing the preview type"}, "api/HotPreview.SharedModel.App.PreviewsManagerReflection.html": {"href": "api/HotPreview.SharedModel.App.PreviewsManagerReflection.html", "title": "Class PreviewsManagerReflection | Hot Preview Documentation", "summary": "Class PreviewsManagerReflection Namespace HotPreview.SharedModel.App Assembly HotPreview.SharedModel.dll public class PreviewsManagerReflection : PreviewsManagerBase<UIComponentReflection, PreviewReflection, PreviewCommandReflection> Inheritance object PreviewsManagerBase<UIComponentReflection, PreviewReflection, PreviewCommandReflection> PreviewsManagerReflection Inherited Members PreviewsManagerBase<UIComponentReflection, PreviewReflection, PreviewCommandReflection>.Categories PreviewsManagerBase<UIComponentReflection, PreviewReflection, PreviewCommandReflection>.UIComponents PreviewsManagerBase<UIComponentReflection, PreviewReflection, PreviewCommandReflection>.Commands PreviewsManagerBase<UIComponentReflection, PreviewReflection, PreviewCommandReflection>.CategorizedUIComponents PreviewsManagerBase<UIComponentReflection, PreviewReflection, PreviewCommandReflection>.HasCategories PreviewsManagerBase<UIComponentReflection, PreviewReflection, PreviewCommandReflection>.GetUIComponent(string) PreviewsManagerBase<UIComponentReflection, PreviewReflection, PreviewCommandReflection>.GetCommand(string) PreviewsManagerBase<UIComponentReflection, PreviewReflection, PreviewCommandReflection>.HasPreview(string, string) object.Equals(object) object.Equals(object, object) object.GetHashCode() object.GetType() object.MemberwiseClone() object.ReferenceEquals(object, object) object.ToString() Constructors PreviewsManagerReflection(IReadOnlyDictionary<string, UIComponentReflection>, IReadOnlyDictionary<string, UIComponentCategory>, IReadOnlyDictionary<string, PreviewCommandReflection>) public PreviewsManagerReflection(IReadOnlyDictionary<string, UIComponentReflection> uiComponents, IReadOnlyDictionary<string, UIComponentCategory> categories, IReadOnlyDictionary<string, PreviewCommandReflection> commands) Parameters uiComponents IReadOnlyDictionary<string, UIComponentReflection> categories IReadOnlyDictionary<string, UIComponentCategory> commands IReadOnlyDictionary<string, PreviewCommandReflection>"}, "api/HotPreview.SharedModel.App.ToolingAppClientConnection.html": {"href": "api/HotPreview.SharedModel.App.ToolingAppClientConnection.html", "title": "Class ToolingAppClientConnection | Hot Preview Documentation", "summary": "Class ToolingAppClientConnection Namespace HotPreview.SharedModel.App Assembly HotPreview.SharedModel.dll public sealed class ToolingAppClientConnection : IDisposable Inheritance object ToolingAppClientConnection Implements IDisposable Inherited Members object.Equals(object) object.Equals(object, object) object.GetHashCode() object.GetType() object.ReferenceEquals(object, object) object.ToString() Constructors ToolingAppClientConnection(string) public ToolingAppClientConnection(string connectionString) Parameters connectionString string Methods Dispose() public void Dispose() StartConnectionAsync(PreviewAppService) public Task StartConnectionAsync(PreviewAppService appService) Parameters appService PreviewAppService Returns Task"}, "api/HotPreview.SharedModel.App.UIComponentPreviewPairReflection.html": {"href": "api/HotPreview.SharedModel.App.UIComponentPreviewPairReflection.html", "title": "Struct UIComponentPreviewPairReflection | Hot Preview Documentation", "summary": "Struct UIComponentPreviewPairReflection Namespace HotPreview.SharedModel.App Assembly HotPreview.SharedModel.dll public readonly struct UIComponentPreviewPairReflection Inherited Members ValueType.Equals(object) ValueType.GetHashCode() ValueType.ToString() object.Equals(object, object) object.GetType() object.ReferenceEquals(object, object) Constructors UIComponentPreviewPairReflection(UIComponentReflection, PreviewReflection) public UIComponentPreviewPairReflection(UIComponentReflection uiComponent, PreviewReflection preview) Parameters uiComponent UIComponentReflection preview PreviewReflection Properties Preview public PreviewReflection Preview { get; } Property Value PreviewReflection UIComponent public UIComponentReflection UIComponent { get; } Property Value UIComponentReflection"}, "api/HotPreview.SharedModel.App.UIComponentReflection.html": {"href": "api/HotPreview.SharedModel.App.UIComponentReflection.html", "title": "Class UIComponentReflection | Hot Preview Documentation", "summary": "Class UIComponentReflection Namespace HotPreview.SharedModel.App Assembly HotPreview.SharedModel.dll public class UIComponentReflection : UIComponentBase<PreviewReflection> Inheritance object UIComponentBase<PreviewReflection> UIComponentReflection Inherited Members UIComponentBase<PreviewReflection>.Category UIComponentBase<PreviewReflection>.Kind UIComponentBase<PreviewReflection>.DisplayName UIComponentBase<PreviewReflection>.DisplayNameOverride UIComponentBase<PreviewReflection>.PathIcon UIComponentBase<PreviewReflection>.HasPreview UIComponentBase<PreviewReflection>.HasNoPreviews UIComponentBase<PreviewReflection>.HasSinglePreview UIComponentBase<PreviewReflection>.HasMultiplePreviews UIComponentBase<PreviewReflection>.Previews UIComponentBase<PreviewReflection>.GetPreview(string) UIComponentBase<PreviewReflection>.DefaultPreview UIComponentBase<PreviewReflection>.IsAutoGenerated UIComponentBase<PreviewReflection>.GetUpdatedPreviews(PreviewReflection) object.Equals(object) object.Equals(object, object) object.GetHashCode() object.GetType() object.MemberwiseClone() object.ReferenceEquals(object, object) object.ToString() Constructors UIComponentReflection(Type, UIComponentKind, string?, IReadOnlyList<PreviewReflection>) public UIComponentReflection(Type type, UIComponentKind kind, string? displayNameOverride, IReadOnlyList<PreviewReflection> previews) Parameters type Type kind UIComponentKind displayNameOverride string previews IReadOnlyList<PreviewReflection> Properties Name Name is intended to be what's used by the code to identify the component. It's the component's full qualified type name and is unique. public override string Name { get; } Property Value string Type public Type Type { get; } Property Value Type Methods GetUIComponentInfo() Gets the UI component information including name, display name, and preview information. public UIComponentInfo GetUIComponentInfo() Returns UIComponentInfo A UIComponentInfo record with the component details, for use in the JSON RPC protocol WithAddedPreview(PreviewReflection) Creates a copy of this UI component with an additional preview. If the new preview is not auto-generated, removes any auto-generated previews from the result. public override UIComponentBase<PreviewReflection> WithAddedPreview(PreviewReflection preview) Parameters preview PreviewReflection The preview to add Returns UIComponentBase<PreviewReflection> A new UI component instance with the added preview"}, "api/HotPreview.SharedModel.App.html": {"href": "api/HotPreview.SharedModel.App.html", "title": "Namespace HotPreview.SharedModel.App | Hot Preview Documentation", "summary": "Namespace HotPreview.SharedModel.App Classes GetPreviewsViaReflection PreviewAppService PreviewApplication PreviewClassReflection PreviewCommandReflection Reflection-based command implementation for static methods marked with [PreviewCommand]. PreviewReflection PreviewStaticMethodReflection PreviewsManagerReflection ToolingAppClientConnection UIComponentReflection Structs UIComponentPreviewPairReflection Interfaces IPreviewNavigator"}, "api/HotPreview.SharedModel.IUIComponentExclusionFilter.html": {"href": "api/HotPreview.SharedModel.IUIComponentExclusionFilter.html", "title": "Interface IUIComponentExclusionFilter | Hot Preview Documentation", "summary": "Interface IUIComponentExclusionFilter Namespace HotPreview.SharedModel Assembly HotPreview.SharedModel.dll public interface IUIComponentExclusionFilter Methods ExcludeAssembly(Assembly) bool ExcludeAssembly(Assembly assembly) Parameters assembly Assembly Returns bool ExcludeType(Type) bool ExcludeType(Type type) Parameters type Type Returns bool"}, "api/HotPreview.SharedModel.PreviewBase.html": {"href": "api/HotPreview.SharedModel.PreviewBase.html", "title": "Class PreviewBase | Hot Preview Documentation", "summary": "Class PreviewBase Namespace HotPreview.SharedModel Assembly HotPreview.SharedModel.dll public abstract class PreviewBase Inheritance object PreviewBase Derived PreviewReflection Inherited Members object.Equals(object) object.Equals(object, object) object.GetHashCode() object.GetType() object.MemberwiseClone() object.ReferenceEquals(object, object) object.ToString() Constructors PreviewBase(string?) protected PreviewBase(string? displayNameOverride) Parameters displayNameOverride string Properties DisplayName DisplayName is intended to be what's shown in UI to identify the preview. It can contain spaces and isn't necessarily unique. It defaults to the preview method/class name (with no namespace) but can be overridden by the developer. public string DisplayName { get; } Property Value string DisplayNameOverride public string? DisplayNameOverride { get; } Property Value string IsAutoGenerated public virtual bool IsAutoGenerated { get; } Property Value bool Name Name is intended to be used by code to uniquely identify the preview. It's the preview's full qualified method name. public abstract string Name { get; } Property Value string"}, "api/HotPreview.SharedModel.PreviewCommandBase.html": {"href": "api/HotPreview.SharedModel.PreviewCommandBase.html", "title": "Class PreviewCommandBase | Hot Preview Documentation", "summary": "Class PreviewCommandBase Namespace HotPreview.SharedModel Assembly HotPreview.SharedModel.dll Base class for command reflection, representing commands that can be executed from the DevTools UI. public abstract class PreviewCommandBase Inheritance object PreviewCommandBase Derived PreviewCommandReflection Inherited Members object.Equals(object) object.Equals(object, object) object.GetHashCode() object.GetType() object.MemberwiseClone() object.ReferenceEquals(object, object) object.ToString() Constructors PreviewCommandBase(string?) Base class for command reflection, representing commands that can be executed from the DevTools UI. protected PreviewCommandBase(string? displayNameOverride) Parameters displayNameOverride string Properties DisplayName DisplayName is intended to be what's shown in UI to identify the command. It can contain spaces and isn't necessarily unique. It defaults to the command method name (with no namespace) but can be overridden by the developer. public string DisplayName { get; } Property Value string DisplayNameOverride public string? DisplayNameOverride { get; } Property Value string Name Name is intended to be used by code to uniquely identify the command. It's the command's full qualified method name. public abstract string Name { get; } Property Value string"}, "api/HotPreview.SharedModel.PreviewNotFoundException.html": {"href": "api/HotPreview.SharedModel.PreviewNotFoundException.html", "title": "Class PreviewNotFoundException | Hot Preview Documentation", "summary": "Class PreviewNotFoundException Namespace HotPreview.SharedModel Assembly HotPreview.SharedModel.dll public class PreviewNotFoundException : Exception, ISerializable Inheritance object Exception PreviewNotFoundException Implements ISerializable Inherited Members Exception.GetBaseException() Exception.GetObjectData(SerializationInfo, StreamingContext) Exception.GetType() Exception.ToString() Exception.Data Exception.HelpLink Exception.HResult Exception.InnerException Exception.Message Exception.Source Exception.StackTrace Exception.TargetSite Exception.SerializeObjectState object.Equals(object) object.Equals(object, object) object.GetHashCode() object.MemberwiseClone() object.ReferenceEquals(object, object) Constructors PreviewNotFoundException(string) public PreviewNotFoundException(string message) Parameters message string"}, "api/HotPreview.SharedModel.PreviewsManagerBase-3.html": {"href": "api/HotPreview.SharedModel.PreviewsManagerBase-3.html", "title": "Class PreviewsManagerBase<TUIComponent, TPreview, TCommand> | Hot Preview Documentation", "summary": "Class PreviewsManagerBase<TUIComponent, TPreview, TCommand> Namespace HotPreview.SharedModel Assembly HotPreview.SharedModel.dll public abstract class PreviewsManagerBase<TUIComponent, TPreview, TCommand> where TUIComponent : UIComponentBase<TPreview> where TPreview : PreviewBase where TCommand : PreviewCommandBase Type Parameters TUIComponent TPreview TCommand Inheritance object PreviewsManagerBase<TUIComponent, TPreview, TCommand> Derived PreviewsManagerReflection Inherited Members object.Equals(object) object.Equals(object, object) object.GetHashCode() object.GetType() object.MemberwiseClone() object.ReferenceEquals(object, object) object.ToString() Constructors PreviewsManagerBase(IReadOnlyDictionary<string, TUIComponent>, IReadOnlyDictionary<string, UIComponentCategory>, IReadOnlyDictionary<string, TCommand>) protected PreviewsManagerBase(IReadOnlyDictionary<string, TUIComponent> uiComponents, IReadOnlyDictionary<string, UIComponentCategory> categories, IReadOnlyDictionary<string, TCommand> commands) Parameters uiComponents IReadOnlyDictionary<string, TUIComponent> categories IReadOnlyDictionary<string, UIComponentCategory> commands IReadOnlyDictionary<string, TCommand> Properties Categories public IEnumerable<UIComponentCategory> Categories { get; } Property Value IEnumerable<UIComponentCategory> CategorizedUIComponents Get UI components grouped by category, sorted by category name, with UI components sorted by display name. Includes \"Pages\" and \"Controls\" categories for uncategorized components based on their Kind. public IReadOnlyList<(UIComponentCategory Category, IReadOnlyList<TUIComponent> UIComponents)> CategorizedUIComponents { get; } Property Value IReadOnlyList<(UIComponentCategory Category, IReadOnlyList<TUIComponent> UIComponents)> Commands public IEnumerable<TCommand> Commands { get; } Property Value IEnumerable<TCommand> HasCategories public bool HasCategories { get; } Property Value bool UIComponents public IEnumerable<TUIComponent> UIComponents { get; } Property Value IEnumerable<TUIComponent> Methods GetCommand(string) public TCommand? GetCommand(string name) Parameters name string Returns TCommand GetUIComponent(string) public TUIComponent? GetUIComponent(string name) Parameters name string Returns TUIComponent HasPreview(string, string) Returns true if the manager contains the specified UI component and that component contains the specified preview. public bool HasPreview(string uiComponentName, string previewName) Parameters uiComponentName string The name of the UI component to check. previewName string The name of the preview to check. Returns bool True if both the UI component and preview exist; otherwise, false."}, "api/HotPreview.SharedModel.PreviewsManagerBuilderBase-3.html": {"href": "api/HotPreview.SharedModel.PreviewsManagerBuilderBase-3.html", "title": "Class PreviewsManagerBuilderBase<TUIComponent, TPreview, TCommand> | Hot Preview Documentation", "summary": "Class PreviewsManagerBuilderBase<TUIComponent, TPreview, TCommand> Namespace HotPreview.SharedModel Assembly HotPreview.SharedModel.dll A builder class for constructing PreviewsManager instances. This class provides mutable operations to build up the state before creating an immutable manager. public class PreviewsManagerBuilderBase<TUIComponent, TPreview, TCommand> where TUIComponent : <PERSON>IComponentBase<TPreview> where TPreview : PreviewBase where TCommand : PreviewCommandBase Type Parameters TUIComponent The type of UI component TPreview The type of preview TCommand The type of command Inheritance object PreviewsManagerBuilderBase<TUIComponent, TPreview, TCommand> Derived GetPreviewsViaReflection Inherited Members object.Equals(object) object.Equals(object, object) object.GetHashCode() object.GetType() object.MemberwiseClone() object.ReferenceEquals(object, object) object.ToString() Constructors PreviewsManagerBuilderBase() Initializes a new instance of the PreviewsManagerBuilderBase class. public PreviewsManagerBuilderBase() Fields _baseTypes protected readonly Dictionary<(UIComponentKind kind, string platform), List<string>> _baseTypes Field Value Dictionary<(UIComponentKind kind, string platform), List<string>> _categories protected readonly Dictionary<string, UIComponentCategory> _categories Field Value Dictionary<string, UIComponentCategory> _commandsByName protected readonly Dictionary<string, TCommand> _commandsByName Field Value Dictionary<string, TCommand> _uiComponentsByName protected readonly Dictionary<string, TUIComponent> _uiComponentsByName Field Value Dictionary<string, TUIComponent> Properties Categories public IReadOnlyDictionary<string, UIComponentCategory> Categories { get; } Property Value IReadOnlyDictionary<string, UIComponentCategory> CommandsByName public IReadOnlyDictionary<string, TCommand> CommandsByName { get; } Property Value IReadOnlyDictionary<string, TCommand> UIComponentsByName public IReadOnlyDictionary<string, TUIComponent> UIComponentsByName { get; } Property Value IReadOnlyDictionary<string, TUIComponent> Methods AddCategory(UIComponentCategory) Adds a category to the builder. public void AddCategory(UIComponentCategory category) Parameters category UIComponentCategory The category to add AddCommand(TCommand) Adds a command to the builder. public void AddCommand(TCommand command) Parameters command TCommand The command to add Exceptions ArgumentException Thrown when a command with the same name already exists AddOrUpdateCategory(string, IReadOnlyList<string>) Adds a category if it doesn't exist, or updates it with the additional uiComponentNames if it does. public void AddOrUpdateCategory(string categoryName, IReadOnlyList<string> uiComponentNames) Parameters categoryName string The name of the category uiComponentNames IReadOnlyList<string> AddOrUpdateCommand(TCommand) Adds or updates a command in the builder. public void AddOrUpdateCommand(TCommand command) Parameters command TCommand The command to add or update AddOrUpdateUIComponent(TUIComponent) Adds or updates a UI component in the builder. public void AddOrUpdateUIComponent(TUIComponent component) Parameters component TUIComponent The component to add or update AddUIComponent(TUIComponent) Adds a UI component to the builder. public void AddUIComponent(TUIComponent component) Parameters component TUIComponent The component to add Exceptions ArgumentException Thrown when a component with the same name already exists AddUIComponentBaseType(UIComponentKind, string, string) Adds a UI component base type for the specified kind and platform. public void AddUIComponentBaseType(UIComponentKind kind, string platform, string baseType) Parameters kind UIComponentKind The kind of UI component (Page or Control) platform string The platform name baseType string The base type name GetCommand(string) Gets a command by name. public TCommand? GetCommand(string name) Parameters name string The name of the command Returns TCommand The command if found, otherwise null GetUIComponent(string) Gets a UI component by name. public TUIComponent? GetUIComponent(string name) Parameters name string The name of the component Returns TUIComponent The component if found, otherwise null IsUIComponentBaseType(string, out UIComponentKind) Checks if a type name is a registered UI component base type. public bool IsUIComponentBaseType(string typeName, out UIComponentKind kind) Parameters typeName string The type name to check kind UIComponentKind The UI component kind if found Returns bool True if the type is a registered base type, false otherwise Validate() Validates the builder state before building. public void Validate() Exceptions InvalidOperationException Thrown when the builder state is invalid"}, "api/HotPreview.SharedModel.Protocol.IPreviewAppControllerService.html": {"href": "api/HotPreview.SharedModel.Protocol.IPreviewAppControllerService.html", "title": "Interface IPreviewAppControllerService | Hot Preview Documentation", "summary": "Interface IPreviewAppControllerService Namespace HotPreview.SharedModel.Protocol Assembly HotPreview.SharedModel.dll public interface IPreviewAppControllerService Methods NotifyPreviewsChangedAsync() Task NotifyPreviewsChangedAsync() Returns Task RegisterAppAsync(string, string) Task RegisterAppAsync(string projectPath, string platformName) Parameters projectPath string platformName string Returns Task"}, "api/HotPreview.SharedModel.Protocol.IPreviewAppService.html": {"href": "api/HotPreview.SharedModel.Protocol.IPreviewAppService.html", "title": "Interface IPreviewAppService | Hot Preview Documentation", "summary": "Interface IPreviewAppService Namespace HotPreview.SharedModel.Protocol Assembly HotPreview.SharedModel.dll public interface IPreviewAppService Methods GetCommandsAsync() Task<PreviewCommandInfo[]> GetCommandsAsync() Returns Task<PreviewCommandInfo[]> GetPreviewSnapshotAsync(string, string) Task<byte[]> GetPreviewSnapshotAsync(string uiComponentName, string previewName) Parameters uiComponentName string previewName string Returns Task<byte[]> GetUIComponentPreviewsAsync(string) Task<string[]> GetUIComponentPreviewsAsync(string componentName) Parameters componentName string Returns Task<string[]> GetUIComponentsAsync() Task<UIComponentInfo[]> GetUIComponentsAsync() Returns Task<UIComponentInfo[]> InvokeCommandAsync(string) Task InvokeCommandAsync(string commandName) Parameters commandName string Returns Task NavigateToPreviewAsync(string, string) Task NavigateToPreviewAsync(string componentName, string previewName) Parameters componentName string previewName string Returns Task"}, "api/HotPreview.SharedModel.Protocol.PreviewCommandInfo.html": {"href": "api/HotPreview.SharedModel.Protocol.PreviewCommandInfo.html", "title": "Class PreviewCommandInfo | Hot Preview Documentation", "summary": "Class PreviewCommandInfo Namespace HotPreview.SharedModel.Protocol Assembly HotPreview.SharedModel.dll public record PreviewCommandInfo : IEquatable<PreviewCommandInfo> Inheritance object PreviewCommandInfo Implements IEquatable<PreviewCommandInfo> Inherited Members object.Equals(object) object.Equals(object, object) object.GetHashCode() object.GetType() object.MemberwiseClone() object.ReferenceEquals(object, object) object.ToString() Constructors PreviewCommandInfo(string, string?) public PreviewCommandInfo(string Name, string? DisplayName) Parameters Name string DisplayName string Properties DisplayName public string? DisplayName { get; init; } Property Value string Name public string Name { get; init; } Property Value string"}, "api/HotPreview.SharedModel.Protocol.PreviewInfo.html": {"href": "api/HotPreview.SharedModel.Protocol.PreviewInfo.html", "title": "Class PreviewInfo | Hot Preview Documentation", "summary": "Class PreviewInfo Namespace HotPreview.SharedModel.Protocol Assembly HotPreview.SharedModel.dll public record PreviewInfo : IEquatable<PreviewInfo> Inheritance object PreviewInfo Implements IEquatable<PreviewInfo> Inherited Members object.Equals(object) object.Equals(object, object) object.GetHashCode() object.GetType() object.MemberwiseClone() object.ReferenceEquals(object, object) object.ToString() Constructors PreviewInfo(string, string, string?, bool) public PreviewInfo(string PreviewType, string Name, string? DisplayName, bool IsAutoGenerated) Parameters PreviewType string Name string DisplayName string IsAutoGenerated bool Properties DisplayName public string? DisplayName { get; init; } Property Value string IsAutoGenerated public bool IsAutoGenerated { get; init; } Property Value bool Name public string Name { get; init; } Property Value string PreviewType public string PreviewType { get; init; } Property Value string"}, "api/HotPreview.SharedModel.Protocol.PreviewTypeInfo.html": {"href": "api/HotPreview.SharedModel.Protocol.PreviewTypeInfo.html", "title": "Class PreviewTypeInfo | Hot Preview Documentation", "summary": "Class PreviewTypeInfo Namespace HotPreview.SharedModel.Protocol Assembly HotPreview.SharedModel.dll public static class PreviewTypeInfo Inheritance object PreviewTypeInfo Inherited Members object.Equals(object) object.Equals(object, object) object.GetHashCode() object.GetType() object.MemberwiseClone() object.ReferenceEquals(object, object) object.ToString() Fields Class public const string Class = \"class\" Field Value string StaticMethod public const string StaticMethod = \"staticMethod\" Field Value string"}, "api/HotPreview.SharedModel.Protocol.UIComponentInfo.html": {"href": "api/HotPreview.SharedModel.Protocol.UIComponentInfo.html", "title": "Class UIComponentInfo | Hot Preview Documentation", "summary": "Class UIComponentInfo Namespace HotPreview.SharedModel.Protocol Assembly HotPreview.SharedModel.dll public record UIComponentInfo : IEquatable<UIComponentInfo> Inheritance object UIComponentInfo Implements IEquatable<UIComponentInfo> Inherited Members object.Equals(object) object.Equals(object, object) object.GetHashCode() object.GetType() object.MemberwiseClone() object.ReferenceEquals(object, object) object.ToString() Constructors UIComponentInfo(string, string, string?, PreviewInfo[]) public UIComponentInfo(string Name, string UIComponentKindInfo, string? DisplayName, PreviewInfo[] Previews) Parameters Name string UIComponentKindInfo string DisplayName string Previews PreviewInfo[] Properties DisplayName public string? DisplayName { get; init; } Property Value string Name public string Name { get; init; } Property Value string Previews public PreviewInfo[] Previews { get; init; } Property Value PreviewInfo[] UIComponentKindInfo public string UIComponentKindInfo { get; init; } Property Value string"}, "api/HotPreview.SharedModel.Protocol.UIComponentKindInfo.html": {"href": "api/HotPreview.SharedModel.Protocol.UIComponentKindInfo.html", "title": "Class UIComponentKindInfo | Hot Preview Documentation", "summary": "Class UIComponentKindInfo Namespace HotPreview.SharedModel.Protocol Assembly HotPreview.SharedModel.dll public static class UIComponentKindInfo Inheritance object UIComponentKindInfo Inherited Members object.Equals(object) object.Equals(object, object) object.GetHashCode() object.GetType() object.MemberwiseClone() object.ReferenceEquals(object, object) object.ToString() Fields Control public const string Control = \"control\" Field Value string Page public const string Page = \"page\" Field Value string Unknown public const string Unknown = \"unknown\" Field Value string Methods FromUIComponentKind(UIComponentKind) Converts a UIComponentKind enum value to its string representation. public static string FromUIComponentKind(UIComponentKind kind) Parameters kind UIComponentKind The UIComponentKind enum value to convert Returns string The string representation of the UIComponentKind ToUIComponentKind(string) Converts a string representation to a UIComponentKind enum value. public static UIComponentKind ToUIComponentKind(string kindString) Parameters kindString string The string representation to convert Returns UIComponentKind The UIComponentKind enum value Exceptions ArgumentException Thrown when the string does not match any known UIComponentKind"}, "api/HotPreview.SharedModel.Protocol.html": {"href": "api/HotPreview.SharedModel.Protocol.html", "title": "Namespace HotPreview.SharedModel.Protocol | Hot Preview Documentation", "summary": "Namespace HotPreview.SharedModel.Protocol Classes PreviewCommandInfo PreviewInfo PreviewTypeInfo UIComponentInfo UIComponentKindInfo Interfaces IPreviewAppControllerService IPreviewAppService"}, "api/HotPreview.SharedModel.StringUtilities.html": {"href": "api/HotPreview.SharedModel.StringUtilities.html", "title": "Class StringUtilities | Hot Preview Documentation", "summary": "Class StringUtilities Namespace HotPreview.SharedModel Assembly HotPreview.SharedModel.dll public static class StringUtilities Inheritance object StringUtilities Inherited Members object.Equals(object) object.Equals(object, object) object.GetHashCode() object.GetType() object.MemberwiseClone() object.ReferenceEquals(object, object) object.ToString() Methods StartCase(string) Converts string to start case. Matches the behavior of Lodash's startCase function, which is used by Storybook for automatically creating story names from exported function names. public static string StartCase(string input) Parameters input string The string to convert. Returns string The converted string in start case format."}, "api/HotPreview.SharedModel.UIComponentBase-1.html": {"href": "api/HotPreview.SharedModel.UIComponentBase-1.html", "title": "Class UIComponentBase<TPreview> | Hot Preview Documentation", "summary": "Class UIComponentBase<TPreview> Namespace HotPreview.SharedModel Assembly HotPreview.SharedModel.dll public abstract class UIComponentBase<TPreview> where TPreview : PreviewBase Type Parameters TPreview Inheritance object UIComponentBase<TPreview> Derived UIComponentReflection Inherited Members object.Equals(object) object.Equals(object, object) object.GetHashCode() object.GetType() object.MemberwiseClone() object.ReferenceEquals(object, object) object.ToString() Constructors UIComponentBase(UIComponentKind, string?, IReadOnlyList<TPreview>) protected UIComponentBase(UIComponentKind kind, string? displayNameOverride, IReadOnlyList<TPreview> previews) Parameters kind UIComponentKind displayNameOverride string previews IReadOnlyList<TPreview> Properties Category public UIComponentCategory? Category { get; } Property Value UIComponentCategory DefaultPreview public TPreview DefaultPreview { get; } Property Value TPreview DisplayName DisplayName is intended to be what's shown in the UI to identify the component. It can contain spaces and isn't necessarily unique. It defaults to the class name (with no namespace qualifier) but can be overridden by the developer. public string DisplayName { get; } Property Value string DisplayNameOverride public string? DisplayNameOverride { get; } Property Value string HasMultiplePreviews public bool HasMultiplePreviews { get; } Property Value bool HasNoPreviews public bool HasNoPreviews { get; } Property Value bool HasPreview public bool HasPreview { get; } Property Value bool HasSinglePreview public bool HasSinglePreview { get; } Property Value bool IsAutoGenerated public bool IsAutoGenerated { get; } Property Value bool Kind public UIComponentKind Kind { get; } Property Value UIComponentKind Name Name is intended to be what's used by the code to identify the component. It's the component's full qualified type name and is unique. public abstract string Name { get; } Property Value string PathIcon Returns a path that can be used as a monochrome icon (e.g. via WinUI's PathIcon). The path here is assumed t be in a 24x24 viewport (the convention used by Material icons and most Fluent icons). If you have a path with a different viewport, you can scale it to 24x24 by applying a scale operation with https://yqnn.github.io/svg-path-editor/ or similar tools. TODO: Make this customizable with UIComponent attribute. public string PathIcon { get; } Property Value string Previews public IReadOnlyList<TPreview> Previews { get; } Property Value IReadOnlyList<TPreview> Methods GetPreview(string) public TPreview? GetPreview(string name) Parameters name string Returns TPreview GetUpdatedPreviews(TPreview) Helper method for subclasses to apply the auto-generated preview removal logic for WithAddedPreview. protected IReadOnlyList<TPreview> GetUpdatedPreviews(TPreview newPreview) Parameters newPreview TPreview Returns IReadOnlyList<TPreview> WithAddedPreview(TPreview) Creates a copy of this UI component with an additional preview. If the new preview is not auto-generated, removes any auto-generated previews from the result. public abstract UIComponentBase<TPreview> WithAddedPreview(TPreview preview) Parameters preview TPreview The preview to add Returns UIComponentBase<TPreview> A new UI component instance with the added preview"}, "api/HotPreview.SharedModel.UIComponentBaseTypes.html": {"href": "api/HotPreview.SharedModel.UIComponentBaseTypes.html", "title": "Class UIComponentBaseTypes | Hot Preview Documentation", "summary": "Class UIComponentBaseTypes Namespace HotPreview.SharedModel Assembly HotPreview.SharedModel.dll public class UIComponentBaseTypes Inheritance object UIComponentBaseTypes Inherited Members object.Equals(object) object.Equals(object, object) object.GetHashCode() object.GetType() object.MemberwiseClone() object.ReferenceEquals(object, object) object.ToString() Methods AddBaseType(string, string) public void AddBaseType(string platform, string baseType) Parameters platform string baseType string IsUIComponentBaseType(string) public string? IsUIComponentBaseType(string typeName) Parameters typeName string Returns string"}, "api/HotPreview.SharedModel.UIComponentCategory.html": {"href": "api/HotPreview.SharedModel.UIComponentCategory.html", "title": "Class UIComponentCategory | Hot Preview Documentation", "summary": "Class UIComponentCategory Namespace HotPreview.SharedModel Assembly HotPreview.SharedModel.dll public class UIComponentCategory Inheritance object UIComponentCategory Inherited Members object.Equals(object) object.Equals(object, object) object.GetHashCode() object.GetType() object.MemberwiseClone() object.ReferenceEquals(object, object) object.ToString() Constructors UIComponentCategory(string, IReadOnlyList<string>) public UIComponentCategory(string name, IReadOnlyList<string> uiComponentNames) Parameters name string uiComponentNames IReadOnlyList<string> Properties Name public string Name { get; } Property Value string UIComponentNames public IReadOnlyList<string> UIComponentNames { get; } Property Value IReadOnlyList<string> Methods WithAddedUIComponentNames(IReadOnlyList<string>) public UIComponentCategory WithAddedUIComponentNames(IReadOnlyList<string> additionalUIComponentNames) Parameters additionalUIComponentNames IReadOnlyList<string> Returns UIComponentCategory"}, "api/HotPreview.SharedModel.UIComponentKind.html": {"href": "api/HotPreview.SharedModel.UIComponentKind.html", "title": "Enum UIComponentKind | Hot Preview Documentation", "summary": "Enum UIComponentKind Namespace HotPreview.SharedModel Assembly HotPreview.SharedModel.dll public enum UIComponentKind Fields Control = 1 Page = 0 Unknown = 2"}, "api/HotPreview.SharedModel.UIComponentNotFoundException.html": {"href": "api/HotPreview.SharedModel.UIComponentNotFoundException.html", "title": "Class UIComponentNotFoundException | Hot Preview Documentation", "summary": "Class UIComponentNotFoundException Namespace HotPreview.SharedModel Assembly HotPreview.SharedModel.dll public class UIComponentNotFoundException : Exception, ISerializable Inheritance object Exception UIComponentNotFoundException Implements ISerializable Inherited Members Exception.GetBaseException() Exception.GetObjectData(SerializationInfo, StreamingContext) Exception.GetType() Exception.ToString() Exception.Data Exception.HelpLink Exception.HResult Exception.InnerException Exception.Message Exception.Source Exception.StackTrace Exception.TargetSite Exception.SerializeObjectState object.Equals(object) object.Equals(object, object) object.GetHashCode() object.MemberwiseClone() object.ReferenceEquals(object, object) Constructors UIComponentNotFoundException(string) public UIComponentNotFoundException(string message) Parameters message string"}, "api/HotPreview.SharedModel.UIComponentPreviewPair-2.html": {"href": "api/HotPreview.SharedModel.UIComponentPreviewPair-2.html", "title": "Struct UIComponentPreviewPair<TUIComponent, TPreview> | Hot Preview Documentation", "summary": "Struct UIComponentPreviewPair<TUIComponent, TPreview> Namespace HotPreview.SharedModel Assembly HotPreview.SharedModel.dll public readonly struct UIComponentPreviewPair<TUIComponent, TPreview> where TUIComponent : UIComponentBase<TPreview> where TPreview : PreviewBase Type Parameters TUIComponent TPreview Inherited Members ValueType.Equals(object) ValueType.GetHashCode() ValueType.ToString() object.Equals(object, object) object.GetType() object.ReferenceEquals(object, object) Constructors UIComponentPreviewPair(TUIComponent, TPreview) public UIComponentPreviewPair(TUIComponent uiComponent, TPreview preview) Parameters uiComponent TUIComponent preview TPreview Properties Preview public TPreview Preview { get; } Property Value TPreview UIComponent public TUIComponent UIComponent { get; } Property Value TUIComponent"}, "api/HotPreview.SharedModel.html": {"href": "api/HotPreview.SharedModel.html", "title": "Namespace HotPreview.SharedModel | Hot Preview Documentation", "summary": "Namespace HotPreview.SharedModel Classes PreviewBase PreviewCommandBase Base class for command reflection, representing commands that can be executed from the DevTools UI. PreviewNotFoundException PreviewsManagerBase<TUIComponent, TPreview, TCommand> PreviewsManagerBuilderBase<TUIComponent, TPreview, TCommand> A builder class for constructing PreviewsManager instances. This class provides mutable operations to build up the state before creating an immutable manager. StringUtilities UIComponentBaseTypes UIComponentBase<TPreview> UIComponentCategory UIComponentNotFoundException Structs UIComponentPreviewPair<TUIComponent, TPreview> Interfaces IUIComponentExclusionFilter Enums UIComponentKind"}, "api/HotPreview.SpecialUIComponentNames.html": {"href": "api/HotPreview.SpecialUIComponentNames.html", "title": "Class SpecialUIComponentNames | Hot Preview Documentation", "summary": "Class SpecialUIComponentNames Namespace HotPreview Assembly HotPreview.dll public static class SpecialUIComponentNames Inheritance object SpecialUIComponentNames Inherited Members object.Equals(object) object.Equals(object, object) object.GetHashCode() object.GetType() object.MemberwiseClone() object.ReferenceEquals(object, object) object.ToString() Fields FullApp Navigating to this component switches to \"full app\" mode. public const string FullApp = \"$FullApp\" Field Value string"}, "api/HotPreview.UIComponentAttribute.html": {"href": "api/HotPreview.UIComponentAttribute.html", "title": "Class UIComponentAttribute | Hot Preview Documentation", "summary": "Class UIComponentAttribute Namespace HotPreview Assembly HotPreview.dll Specifies that this class is a UI component. [AttributeUsage(AttributeTargets.Class)] public sealed class UIComponentAttribute : Attribute Inheritance object Attribute UIComponentAttribute Inherited Members Attribute.Equals(object) Attribute.GetCustomAttribute(Assembly, Type) Attribute.GetCustomAttribute(Assembly, Type, bool) Attribute.GetCustomAttribute(MemberInfo, Type) Attribute.GetCustomAttribute(MemberInfo, Type, bool) Attribute.GetCustomAttribute(Module, Type) Attribute.GetCustomAttribute(Module, Type, bool) Attribute.GetCustomAttribute(ParameterInfo, Type) Attribute.GetCustomAttribute(ParameterInfo, Type, bool) Attribute.GetCustomAttributes(Assembly) Attribute.GetCustomAttributes(Assembly, bool) Attribute.GetCustomAttributes(Assembly, Type) Attribute.GetCustomAttributes(Assembly, Type, bool) Attribute.GetCustomAttributes(MemberInfo) Attribute.GetCustomAttributes(MemberInfo, bool) Attribute.GetCustomAttributes(MemberInfo, Type) Attribute.GetCustomAttributes(MemberInfo, Type, bool) Attribute.GetCustomAttributes(Module) Attribute.GetCustomAttributes(Module, bool) Attribute.GetCustomAttributes(Module, Type) Attribute.GetCustomAttributes(Module, Type, bool) Attribute.GetCustomAttributes(ParameterInfo) Attribute.GetCustomAttributes(ParameterInfo, bool) Attribute.GetCustomAttributes(ParameterInfo, Type) Attribute.GetCustomAttributes(ParameterInfo, Type, bool) Attribute.GetHashCode() Attribute.IsDefaultAttribute() Attribute.IsDefined(Assembly, Type) Attribute.IsDefined(Assembly, Type, bool) Attribute.IsDefined(MemberInfo, Type) Attribute.IsDefined(MemberInfo, Type, bool) Attribute.IsDefined(Module, Type) Attribute.IsDefined(Module, Type, bool) Attribute.IsDefined(ParameterInfo, Type) Attribute.IsDefined(ParameterInfo, Type, bool) Attribute.Match(object) Attribute.TypeId object.Equals(object, object) object.GetType() object.ReferenceEquals(object, object) object.ToString() Remarks Normally UI components don't need to be defined explicitly (defining a preview is sufficient), but this can be used to define a display name for the component. Constructors UIComponentAttribute(string?) Specifies that this class is a UI component. public UIComponentAttribute(string? displayName = null) Parameters displayName string Optional override for the display name for the UI component. If not specified, the name of the class is used (with no namespace). Remarks Normally UI components don't need to be defined explicitly (defining a preview is sufficient), but this can be used to define a display name for the component. Properties DisplayName Optional override for the display name for the UI component. If not specified, the name of the class is used (with no namespace). public string? DisplayName { get; } Property Value string"}, "api/HotPreview.UIComponentCategoryAttribute.html": {"href": "api/HotPreview.UIComponentCategoryAttribute.html", "title": "Class UIComponentCategoryAttribute | Hot Preview Documentation", "summary": "Class UIComponentCategoryAttribute Namespace HotPreview Assembly HotPreview.dll Specifies the category name for a set of UI components. [AttributeUsage(AttributeTargets.Assembly, AllowMultiple = true)] public sealed class UIComponentCategoryAttribute : Attribute Inheritance object Attribute UIComponentCategoryAttribute Inherited Members Attribute.Equals(object) Attribute.GetCustomAttribute(Assembly, Type) Attribute.GetCustomAttribute(Assembly, Type, bool) Attribute.GetCustomAttribute(MemberInfo, Type) Attribute.GetCustomAttribute(MemberInfo, Type, bool) Attribute.GetCustomAttribute(Module, Type) Attribute.GetCustomAttribute(Module, Type, bool) Attribute.GetCustomAttribute(ParameterInfo, Type) Attribute.GetCustomAttribute(ParameterInfo, Type, bool) Attribute.GetCustomAttributes(Assembly) Attribute.GetCustomAttributes(Assembly, bool) Attribute.GetCustomAttributes(Assembly, Type) Attribute.GetCustomAttributes(Assembly, Type, bool) Attribute.GetCustomAttributes(MemberInfo) Attribute.GetCustomAttributes(MemberInfo, bool) Attribute.GetCustomAttributes(MemberInfo, Type) Attribute.GetCustomAttributes(MemberInfo, Type, bool) Attribute.GetCustomAttributes(Module) Attribute.GetCustomAttributes(Module, bool) Attribute.GetCustomAttributes(Module, Type) Attribute.GetCustomAttributes(Module, Type, bool) Attribute.GetCustomAttributes(ParameterInfo) Attribute.GetCustomAttributes(ParameterInfo, bool) Attribute.GetCustomAttributes(ParameterInfo, Type) Attribute.GetCustomAttributes(ParameterInfo, Type, bool) Attribute.GetHashCode() Attribute.IsDefaultAttribute() Attribute.IsDefined(Assembly, Type) Attribute.IsDefined(Assembly, Type, bool) Attribute.IsDefined(MemberInfo, Type) Attribute.IsDefined(MemberInfo, Type, bool) Attribute.IsDefined(Module, Type) Attribute.IsDefined(Module, Type, bool) Attribute.IsDefined(ParameterInfo, Type) Attribute.IsDefined(ParameterInfo, Type, bool) Attribute.Match(object) Attribute.TypeId object.Equals(object, object) object.GetType() object.ReferenceEquals(object, object) object.ToString() Remarks Categories are just used for display purposes. If no category is specified for a component, the category name defaults to \"Pages\" or \"Controls\", depending on whether the UI component is a page or not. This attribute can be specified multiple times for a single category, in which case the UI components are combined together. Constructors UIComponentCategoryAttribute(string, params Type[]) Specifies the category name for a set of UI components. public UIComponentCategoryAttribute(string name, params Type[] uiComponents) Parameters name string The name of the category. uiComponents Type[] The UI components that belong to this category. Remarks Categories are just used for display purposes. If no category is specified for a component, the category name defaults to \"Pages\" or \"Controls\", depending on whether the UI component is a page or not. This attribute can be specified multiple times for a single category, in which case the UI components are combined together. Properties Name public string Name { get; } Property Value string UIComponentTypes public Type[] UIComponentTypes { get; } Property Value Type[]"}, "api/HotPreview.html": {"href": "api/HotPreview.html", "title": "Namespace HotPreview | Hot Preview Documentation", "summary": "Namespace HotPreview Classes AutoGeneratePreviewAttribute Controls whether auto-generated previews should be created for a UI component. ControlUIComponentBaseTypeAttribute Specifies the base type for control UI components on a specific platform. PageUIComponentBaseTypeAttribute Specifies the base type for page UI components on a specific platform. PreviewAttribute Specifies that this static method creates a preview for a UI component. PreviewAttribute<TUIComponent> Specifies that this static method creates a preview for a UI component with an explicitly specified UI component type. PreviewCommandAttribute Designates this method as a command that can be executed from the DevTools UI or as an MCP tool command. RoutePreview Represents a preview for a route-based navigation. RoutePreview<T> Represents a strongly-typed preview for a route-based navigation. SpecialUIComponentNames UIComponentAttribute Specifies that this class is a UI component. UIComponentCategoryAttribute Specifies the category name for a set of UI components."}, "docs/architecture.html": {"href": "docs/architecture.html", "title": "Architecture | Hot Preview Documentation", "summary": "Architecture HotPreview is designed as a cross-platform UI component preview system for .NET, with a modular architecture that separates concerns and enables platform-specific implementations. System Overview graph LR subgraph \"DevTools Process\" DT[\"DevTools<br/>(Uno+Skia)\"] end subgraph \"Your App Process\" YA[\"Your App\"] HPL[\"Hot Preview<br/>Platform Library\"] YA -.-> HPL end DT -.->|\"JSON-RPC Protocol\"| YA Core Components 1. HotPreview Core Library Location: src/HotPreview/ Purpose: Base attributes and types for defining previews Key Components: PreviewAttribute - Marks methods as preview definitions UIComponentAttribute - Explicitly marks classes as UI components PreviewCommandAttribute - Defines executable commands AutoGeneratePreviewAttribute - Controls auto-generation behavior 2. HotPreview Shared Model Location: src/HotPreview.SharedModel/ Purpose: Cross-platform protocol and reflection utilities Key Components: Protocol Definitions: JSON-RPC interfaces for tool communication Reflection Utilities: Component and preview discovery at runtime App Services: Base classes for platform-specific implementations Protocol Classes: public interface IPreviewAppService { Task<PreviewInfo[]> GetPreviewsAsync(); Task NavigateToPreviewAsync(string previewId); Task ExecuteCommandAsync(string commandId); } 3. Platform Applications Location: src/platforms/ Purpose: Platform-specific preview applications MAUI Implementation (HotPreview.App.Maui) MauiPreviewApplication - Main application service MauiPreviewNavigatorService - Navigation handling PreviewsPage - UI for displaying component tree Platform-specific resource handling WPF Implementation (HotPreview.App.Wpf) (Planned) Similar structure adapted for WPF platform Windows-specific navigation and rendering 4. DevTools Infrastructure Location: src/tooling/ Purpose: Visual development environment and tooling DevTools Application (HotPreview.DevToolsApp) Uno+Skia Desktop App: Main user interface Connection Management: Handles multiple app connections Component Tree View: Hierarchical navigation interface Command Execution: UI for running preview commands DevTools Launcher (HotPreview.DevTools) Global Tool: dotnet tool install -g HotPreview.DevTools Process Management: Launches and manages DevTools instances Single Instance Logic: Prevents multiple DevTools instances Tooling Infrastructure (HotPreview.Tooling) Roslyn Analysis: Component discovery through source analysis JSON-RPC Server: Communication protocol implementation MCP Server: AI integration capabilities Visual Testing: Screenshot and comparison utilities Communication Protocol JSON-RPC Over Pipes HotPreview uses JSON-RPC over named pipes for communication between DevTools and applications. Message Flow: App Startup: Application connects to DevTools via named pipe Discovery: DevTools requests component and preview information Navigation: User clicks component, DevTools sends navigation command Command Execution: User triggers command, DevTools forwards to app Example Messages: // Get Previews Request { \"id\": 1, \"method\": \"getPreviewsAsync\", \"params\": {} } // Navigate to Preview Request { \"id\": 2, \"method\": \"navigateToPreviewAsync\", \"params\": { \"previewId\": \"MyApp.Views.ProductCard.Preview\" } } Component Discovery Runtime Discovery (Reflection) Location: HotPreview.SharedModel/App/GetPreviewsViaReflection.cs Process: Assembly Scanning: Scan loaded assemblies for components Type Analysis: Identify types inheriting from platform base types Attribute Detection: Find [Preview] and [UIComponent] attributes Auto-Generation: Create default previews for discovered components Example Code: public static PreviewInfo[] GetPreviews(Assembly assembly) { var types = assembly.GetTypes() .Where(t => IsUIComponent(t)) .ToArray(); return types.SelectMany(CreatePreviewsForType).ToArray(); } Build-Time Discovery (Roslyn) Location: HotPreview.Tooling/GetPreviewsFromRoslyn.cs Process: Source Analysis: Parse C# source files using Roslyn Syntax Tree Walking: Find class declarations and attributes Preview Method Detection: Locate [Preview] methods Metadata Generation: Create preview metadata without runtime loading Benefits: No runtime overhead Works with uncompiled code Better performance for large applications MSBuild Integration Build Tasks Location: src/HotPreview.AppBuildTasks/ Key Tasks: GeneratePreviewAppSettingsTask - Creates app configuration DevTools Launch: Automatically starts DevTools during builds Symbol Definition: Ensures PREVIEWS symbol is defined in Debug builds Integration Points: <Target Name=\"LaunchDevTools\" BeforeTargets=\"Build\" Condition=\"$(Configuration) == 'Debug'\"> <Exec Command=\"hotpreview\" ContinueOnError=\"true\" /> </Target> Platform Abstraction Base Type Configuration Platform-specific base types are configured via assembly attributes: [assembly: ControlUIComponentBaseType(\"MAUI\", \"Microsoft.Maui.Controls.View\")] [assembly: PageUIComponentBaseType(\"MAUI\", \"Microsoft.Maui.Controls.Page\")] Service Implementation Each platform implements the core interfaces: public class MauiPreviewApplication : PreviewApplication { protected override IPreviewNavigator CreateNavigator() => new MauiPreviewNavigatorService(); protected override IUIComponentExclusionFilter CreateExclusionFilter() => new MauiUIComponentExclusionFilter(); } AI Integration (MCP Server) Model Context Protocol Location: HotPreview.Tooling/McpServer/ Capabilities: Device Management: Android/iOS device discovery and control Screenshot Capture: Automated visual testing App Management: Launch and control preview applications Preview Generation: AI-assisted component creation Architecture: ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ │ AI Agent │◄──►│ MCP Server │◄──►│ HotPreview │ │ (Claude) │ │ │ │ DevTools │ └─────────────┘ └─────────────┘ └─────────────┘ Visual Testing Snapshot Generation Location: HotPreview.Tooling/VisualTestUtils/ Components: IImageEditor - Image manipulation interface IVisualComparer - Visual difference detection VisualRegressionTester - Test orchestration MagickNet Integration: ImageMagick-based image processing Workflow: Capture Screenshot: Take snapshot of component Compare Images: Detect visual differences Generate Report: Create difference visualization Assert Results: Integrate with test frameworks Extension Points Custom Platform Support To add support for a new platform: Implement Base Services: public class MyPlatformPreviewApplication : PreviewApplication { // Platform-specific implementation } Configure Base Types: [assembly: ControlUIComponentBaseType(\"MyPlatform\", \"MyPlatform.Controls.Control\")] Create Package: Distribute as HotPreview.App.MyPlatform Custom Discovery Extend component discovery: public class CustomComponentDiscovery : IComponentDiscovery { public PreviewInfo[] DiscoverPreviews(Assembly assembly) { // Custom discovery logic } } Performance Considerations Memory Management Lazy Loading: Components loaded on-demand Weak References: Prevent memory leaks in long-running sessions Resource Cleanup: Proper disposal of platform resources Communication Optimization Batch Operations: Reduce JSON-RPC round trips Incremental Updates: Send only changed data Connection Pooling: Reuse connections across sessions Scalability Concurrent Discovery: Parallel component analysis Caching: Cache discovered components and metadata Selective Loading: Load only visible components in DevTools"}, "docs/attributes.html": {"href": "docs/attributes.html", "title": "HotPreview Attributes Reference | Hot Preview Documentation", "summary": "HotPreview Attributes Reference HotPreview provides several attributes to control how your UI components are discovered, displayed, and organized in the DevTools interface. This reference covers all available attributes and their usage. Core Attributes PreviewAttribute The [Preview] attribute is the primary way to define custom previews for your UI components. Target: Methods and Classes Namespace: HotPreview Parameters Parameter Type Description displayName string? Optional display name for the preview. Use \"/\" delimiters for hierarchy (e.g., \"Cards/Empty State\") uiComponent Type? Optional UI component type this preview represents Constructors [Preview()] // Basic preview [Preview(\"Display Name\")] // Named preview [Preview(typeof(MyComponent))] // Explicit component type [Preview(\"Display Name\", typeof(MyComponent))] // Named with explicit type Usage Examples #if PREVIEWS // Basic preview [Preview] public static CardView Preview() => new(PreviewData.GetCards(3)); // Multiple named previews [Preview(\"Empty State\")] public static CardView NoCards() => new(PreviewData.GetCards(0)); [Preview(\"Cards/Single Card\")] public static CardView SingleCard() => new(PreviewData.GetCards(1)); [Preview(\"Cards/Multiple Cards\")] public static CardView MultipleCards() => new(PreviewData.GetCards(6)); // Preview in different class with explicit type [Preview(\"Custom Layout\", typeof(ProductView))] public static ProductView CustomProductLayout() => new(PreviewData.GetProduct()); #endif UIComponentAttribute The [UIComponent] attribute allows you to explicitly mark classes as UI components and provide custom display names. Target: Classes Namespace: HotPreview Parameters Parameter Type Description displayName string? Optional custom display name for the UI component Usage Examples [UIComponent(\"Shopping Cart\")] public partial class CartView : ContentView { // Component implementation } [UIComponent] // Uses class name as display name public partial class ProductCard : ContentView { // Component implementation } PreviewCommandAttribute The [PreviewCommand] attribute defines commands that can be executed from the DevTools interface. Target: Methods Namespace: HotPreview Parameters Parameter Type Description displayName string? Optional display name for the command. Use \"/\" delimiters for hierarchy Usage Examples #if PREVIEWS [PreviewCommand(\"Reset App State\")] public static void ResetAppState() { // Reset global state for testing App.Current.MainPage = new AppShell(); } [PreviewCommand(\"Data/Load Sample Data\")] public static void LoadSampleData() { // Load test data DataService.LoadSampleData(); } #endif Configuration Attributes AutoGeneratePreviewAttribute Controls whether auto-generated previews should be created for a UI component. Target: Classes Namespace: HotPreview Parameters Parameter Type Description autoGenerate bool true to enable auto-generation, false to disable Usage Examples // Disable auto-generated previews for this component [AutoGeneratePreview(false)] public partial class ComplexView : ContentView { // This component won't get auto-generated previews // You must define custom [Preview] methods } // Explicitly enable (though this is the default) [AutoGeneratePreview(true)] public partial class SimpleView : ContentView { // Auto-generated previews will be created } Assembly-Level Attributes UIComponentCategoryAttribute Defines categories for organizing UI components in the DevTools interface. Target: Assembly Namespace: HotPreview Parameters Parameter Type Description name string Category name uiComponents Type[] Array of UI component types in this category Usage Examples // In AssemblyInfo.cs or any source file using HotPreview; [assembly: UIComponentCategory(\"Navigation\", typeof(HeaderView), typeof(FooterView), typeof(TabBar))] [assembly: UIComponentCategory(\"Cards\", typeof(ProductCard), typeof(CategoryCard), typeof(InfoCard))] [assembly: UIComponentCategory(\"Forms\", typeof(LoginForm), typeof(RegisterForm), typeof(ContactForm))] ControlUIComponentBaseTypeAttribute Defines platform-specific base types for control components. Target: Assembly Namespace: HotPreview Parameters Parameter Type Description platform string Platform identifier (e.g., \"MAUI\", \"WPF\") baseType string Fully qualified base type name Usage Examples // In AssemblyInfo.cs [assembly: ControlUIComponentBaseType(\"MAUI\", \"Microsoft.Maui.Controls.View\")] [assembly: ControlUIComponentBaseType(\"WPF\", \"System.Windows.Controls.Control\")] PageUIComponentBaseTypeAttribute Defines platform-specific base types for page components. Target: Assembly Namespace: HotPreview Parameters Parameter Type Description platform string Platform identifier (e.g., \"MAUI\", \"WPF\") baseType string Fully qualified base type name Usage Examples // In AssemblyInfo.cs [assembly: PageUIComponentBaseType(\"MAUI\", \"Microsoft.Maui.Controls.Page\")] [assembly: PageUIComponentBaseType(\"WPF\", \"System.Windows.Window\")] Best Practices Naming Conventions Use descriptive names that clearly indicate the preview state or variant Use \"/\" delimiters to create hierarchical organization in DevTools Keep names concise but meaningful Preview Organization #if PREVIEWS // Group related previews using hierarchy [Preview(\"States/Loading\")] public static ProductView LoadingState() => new(isLoading: true); [Preview(\"States/Error\")] public static ProductView ErrorState() => new(hasError: true); [Preview(\"States/Empty\")] public static ProductView EmptyState() => new(isEmpty: true); // Organize by data variations [Preview(\"Data/Single Item\")] public static CartView SingleItem() => new(PreviewData.GetCart(1)); [Preview(\"Data/Multiple Items\")] public static CartView MultipleItems() => new(PreviewData.GetCart(5)); #endif Conditional Compilation Always wrap preview code in conditional compilation directives: #if PREVIEWS [Preview] public static MyView Preview() => new(PreviewData.GetSampleData()); [PreviewCommand(\"Reset State\")] public static void ResetState() => AppState.Reset(); #endif Assembly Configuration Set up assembly-level attributes to customize component discovery: // Configure platform-specific base types [assembly: ControlUIComponentBaseType(\"MAUI\", \"Microsoft.Maui.Controls.View\")] [assembly: PageUIComponentBaseType(\"MAUI\", \"Microsoft.Maui.Controls.Page\")] // Organize components into logical categories [assembly: UIComponentCategory(\"Layout\", typeof(HeaderView), typeof(SidebarView))] [assembly: UIComponentCategory(\"Data Display\", typeof(ProductCard), typeof(UserProfile))] Troubleshooting Previews not appearing: Ensure you're building in Debug mode and the PREVIEWS symbol is defined Categories not working: Check that assembly-level attributes are properly declared Auto-generation issues: Use [AutoGeneratePreview(false)] to disable automatic preview creation for complex components"}, "docs/features.html": {"href": "docs/features.html", "title": "Features | Hot Preview Documentation", "summary": "Features HotPreview provides a comprehensive set of features for efficient UI development and testing in .NET applications. Core Features 🚀 Streamlined Navigation Jump directly to specific UI pages and components without navigating through multiple app screens. Benefits: Dramatically reduces development time Eliminates repetitive manual testing workflows Enables instant access to any component state How it works: DevTools displays a hierarchical tree of all UI components Click any component to navigate directly to it in your running app No need to manually navigate through complex app flows 🔄 Multi-state Testing Quickly visualize UI components with different data inputs and states, ensuring responsive and robust interfaces across all scenarios. Supported scenarios: Empty states Loading states Error conditions Different data volumes (single item, multiple items, large datasets) Various user permissions or app states Example: #if PREVIEWS [Preview(\"Empty Cart\")] public static CartView EmptyCart() => new(PreviewData.GetCart(0)); [Preview(\"Single Item\")] public static CartView SingleItem() => new(PreviewData.GetCart(1)); [Preview(\"Full Cart\")] public static CartView FullCart() => new(PreviewData.GetCart(10)); [Preview(\"Cart with Error\")] public static CartView ErrorCart() => new(PreviewData.GetCartWithError()); #endif 📱 Cross-Platform Visualization View your UI on multiple platforms simultaneously, enabling instant cross-platform comparison and consistency validation. Capabilities: Run the same app on Windows, Android, iOS side-by-side Navigate to the same component across platforms instantly Compare visual consistency and behavior Test platform-specific adaptations 🛠️ DevTools Integration A powerful desktop application that serves as your command center for UI development. DevTools features: Component Tree: Hierarchical view of all UI components Live Navigation: Click-to-navigate functionality Command Execution: Run preview commands for state management Multi-App Support: Connect multiple app instances simultaneously Auto-Discovery: Automatic detection of components and previews Auto-Generation Features Intelligent Component Discovery HotPreview automatically discovers and creates previews for UI components without any configuration. Auto-generated for: Pages: Any class inheriting from platform page base types Controls: Any class inheriting from platform view base types Dependency Injection: Components with constructor parameters resolved via DI Customizable discovery: Configure platform-specific base types Control auto-generation per component Organize components into categories Zero-Configuration Setup Get started immediately without complex configuration: Install DevTools globally Add package reference to your app Build and run in Debug mode Advanced Features Custom Preview Commands Define commands that can be executed from DevTools to manipulate app state or perform testing actions. #if PREVIEWS [PreviewCommand(\"Reset User Session\")] public static void ResetSession() { UserService.ClearSession(); App.Current.MainPage = new LoginPage(); } [PreviewCommand(\"Load Test Data\")] public static async Task LoadTestData() { await DataService.LoadSampleDataAsync(); } #endif Hierarchical Organization Use path-like naming to organize components and previews into logical hierarchies. #if PREVIEWS [Preview(\"Authentication/Login Form\")] public static LoginView LoginForm() => new(); [Preview(\"Authentication/Registration Form\")] public static RegisterView RegisterForm() => new(); [Preview(\"Shop/Product Card/Featured\")] public static ProductCard FeaturedProduct() => new(PreviewData.GetFeaturedProduct()); #endif Conditional Compilation Ensure preview code is completely excluded from release builds. #if PREVIEWS // All preview code goes here // Excluded from Release builds automatically #endif Upcoming Features 🤖 AI-Driven Development (Coming Soon) Built-in MCP (Model Context Protocol) server for AI-assisted UI development workflows. Planned capabilities: AI agents can generate and execute previews Automatic screenshot comparison and feedback Visual regression testing with AI analysis Intelligent component generation based on visual requirements 📊 Visual Testing Utils Advanced utilities for visual regression testing and comparison. Current capabilities: Image snapshot generation Visual difference detection Automated screenshot comparison Integration with testing frameworks 🔧 Enhanced DevTools Continuous improvements to the DevTools experience: Performance profiling Component dependency visualization Advanced filtering and search Custom themes and layouts Integration Features MSBuild Integration Seamless integration with your build process: Automatic DevTools launch during Debug builds App settings generation Conditional compilation symbol management Platform Support Current support: .NET MAUI (Windows, Android, iOS, macOS) Planned support: WPF WinUI 3 Uno Platform Avalonia UI Development Workflow IDE Integration: Works with Visual Studio Compatible with VS Code Command-line friendly Testing Integration: xUnit compatibility Visual regression testing Snapshot testing utilities Performance Features Lazy Loading Components and previews are loaded on-demand to maintain performance with large applications. Efficient Discovery Roslyn-based analysis for fast component discovery without runtime overhead. Memory Management Smart memory management to handle multiple app instances and component states efficiently."}, "docs/getting-started.html": {"href": "docs/getting-started.html", "title": "Getting Started | Hot Preview Documentation", "summary": "Getting Started This guide will help you set up HotPreview in your .NET application and create your first previews. Installation Prerequisites .NET 9.0.300 SDK or later (see global.json in the repository root) A supported .NET UI platform (currently .NET MAUI, with WPF support coming soon) Step 1: Install HotPreview DevTools Install the global DevTools application: dotnet tool install -g HotPreview.DevTools Step 2: Add Package Reference Add the HotPreview package to your application project. We recommend only including it in Debug builds: <PackageReference Condition=\"$(Configuration) == 'Debug'\" Include=\"HotPreview.App.Maui\" Version=\"...\" /> Step 3: Build and Run Build your application in Debug mode and run it: dotnet build # Run your application When you build and run your app: The DevTools application launches automatically (if not already running) Your app connects to DevTools when it starts DevTools displays a tree of your UI components and previews Auto-Generated Previews HotPreview automatically creates previews for UI components that meet these criteria: Pages Derives (directly or indirectly) from Microsoft.Maui.Controls.Page Has a parameterless constructor OR constructor parameters that can be resolved via dependency injection Controls Derives from Microsoft.Maui.Controls.View (but is not a page) Has a parameterless constructor OR constructor parameters that can be resolved via dependency injection Creating Custom Previews Custom previews give you full control over how your components are displayed. They allow you to: Support components with complex constructor requirements Provide realistic sample data Create multiple previews for different states Configure global app state for specific scenarios Basic Preview Add a static method with the [Preview] attribute to your UI component class: #if PREVIEWS [Preview] public static ConfirmAddressView Preview() => new(PreviewData.GetPreviewProducts(1), new DeliveryTypeModel(), new AddressModel() { StreetOne = \"21, Alex Davidson Avenue\", StreetTwo = \"Opposite Omegatron, Vicent Quarters\", City = \"Victoria Island\", State = \"Lagos State\" }); #endif Multiple Previews Create multiple previews to show different states: #if PREVIEWS [Preview(\"Empty State\")] public static CardView NoCards() => new(PreviewData.GetPreviewCards(0)); [Preview(\"Single Card\")] public static CardView SingleCard() => new(PreviewData.GetPreviewCards(1)); [Preview(\"Multiple Cards\")] public static CardView SixCards() => new(PreviewData.GetPreviewCards(6)); #endif Preview Guidelines Use conditional compilation: Wrap preview code in #if PREVIEWS to exclude it from release builds Provide meaningful names: Use descriptive names for multiple previews Use sample data: Create realistic test data to showcase your components Location flexibility: Preview methods can be in any class, but by convention are placed in the component class Navigation and Testing Once your app is running with DevTools: Browse Components: Use the DevTools tree view to explore your UI components Navigate Instantly: Click any component or preview to navigate directly to it in your app Test States: Use multiple previews to quickly test different data states Cross-Platform: Run your app on different platforms and compare side-by-side Best Practices Conditional Builds: Always use #if PREVIEWS for preview code Sample Data: Create dedicated preview data classes for consistent testing Descriptive Names: Use clear, descriptive names for multiple previews Edge Cases: Create previews for empty states, error conditions, and loading states Component Isolation: Ensure previews work independently of app navigation state Next Steps Learn about all available attributes Explore advanced features Check out the API reference"}, "docs/samples.html": {"href": "docs/samples.html", "title": "Sample Applications | Hot Preview Documentation", "summary": "Sample Applications HotPreview includes several sample applications that demonstrate different features and usage patterns. These samples serve as both learning resources and testing environments for the framework. Available Samples E-commerce MAUI Application Location: samples/maui/EcommerceMAUI/ Description: A complete e-commerce mobile application showcasing complex UI components and previews. Features Demonstrated Product browsing and search Shopping cart functionality User authentication flows Payment and checkout processes Order tracking Profile management Preview Examples The sample includes extensive preview definitions for various component states: Card Component Previews: #if PREVIEWS [Preview(\"No Cards\")] public static CardView NoCards() => new(PreviewData.GetPreviewCards(0)); [Preview(\"Single Card\")] public static CardView SingleCard() => new(PreviewData.GetPreviewCards(1)); [Preview(\"Two Cards\")] public static CardView TwoCards() => new(PreviewData.GetPreviewCards(2)); [Preview(\"Six Cards\")] public static CardView SixCards() => new(PreviewData.GetPreviewCards(6)); #endif Cart Component States: #if PREVIEWS [Preview(\"Empty Cart\")] public static CartView EmptyCart() => new(PreviewData.GetEmptyCart()); [Preview(\"Single Item Cart\")] public static CartView SingleItemCart() => new(PreviewData.GetCart(1)); [Preview(\"Medium Cart\")] public static CartView MediumCart() => new(PreviewData.GetCart(3)); [Preview(\"Large Cart\")] public static CartView LargeCart() => new(PreviewData.GetCart(10)); #endif Visual Regression Testing The sample includes automated visual testing with snapshot comparisons: Generated Snapshots: EcommerceMAUI.Views.AddNewCardView.png EcommerceMAUI.Views.CartView-SingleItemCart.png EcommerceMAUI.Views.ProductDetailsView.png EcommerceMAUI.Views.HomePageView.png Running the Sample cd samples/maui/EcommerceMAUI dotnet build dotnet run --project EcommerceMAUI.csproj Default Template with Content Location: samples/maui/DefaultTemplateWithContent/ Description: A project management application demonstrating data-driven UI components. Features Demonstrated Project and task management Category-based organization Data visualization with charts CRUD operations Tag-based filtering Components Included ProjectCardView - Project summary cards TaskView - Individual task components CategoryChart - Data visualization TagView - Tag display components AddButton - Action components Preview Data Management The sample shows how to create comprehensive preview data: public static class PreviewData { public static List<Project> GetPreviewProjects(int count) { return MockData.Projects.Take(count).ToList(); } public static List<ProjectTask> GetPreviewTasks(int count) { return MockData.Tasks.Take(count).ToList(); } public static CategoryChartData GetPreviewChartData() { return new CategoryChartData { Categories = MockData.Categories.Take(5).ToList(), Data = GenerateRandomData(5) }; } } Common Patterns Preview Data Organization Both samples demonstrate effective preview data management: Centralized Data Service: public static class PreviewData { // Static data for consistent previews public static readonly Product FeaturedProduct = new() { Name = \"Premium Headphones\", Price = 299.99m, Description = \"High-quality wireless headphones\", IsAvailable = true }; // Dynamic data generation public static List<Product> GetProducts(int count, bool includeFeatured = false) { var products = MockData.Products.Take(count).ToList(); if (includeFeatured) products.Insert(0, FeaturedProduct); return products; } } State Variation Patterns Loading and Error States: #if PREVIEWS [Preview(\"Normal State\")] public static ProductView Normal() => new(PreviewData.FeaturedProduct); [Preview(\"Loading State\")] public static ProductView Loading() => new(isLoading: true); [Preview(\"Error State\")] public static ProductView Error() => new(hasError: true, errorMessage: \"Failed to load product\"); [Preview(\"Empty State\")] public static ProductView Empty() => new(isEmpty: true); #endif Hierarchical Organization Grouped Previews: #if PREVIEWS // Authentication flows [Preview(\"Auth/Login Form\")] public static LoginView LoginForm() => new(); [Preview(\"Auth/Register Form\")] public static RegisterView RegisterForm() => new(); [Preview(\"Auth/Forgot Password\")] public static ForgotPasswordView ForgotPassword() => new(); // Shopping features [Preview(\"Shop/Product List\")] public static ProductListView ProductList() => new(PreviewData.GetProducts(10)); [Preview(\"Shop/Product Details\")] public static ProductDetailsView ProductDetails() => new(PreviewData.FeaturedProduct); #endif Testing Integration Visual Regression Testing Both samples include comprehensive visual testing: Test Structure: samples/maui/EcommerceMAUI/ ├── snapshots/ # Reference images ├── test-results/ # Test output └── VisualTests.cs # Test definitions Example Test: [Fact] public async Task CartView_SingleItem_MatchesSnapshot() { var component = CartView.SingleItemCart(); var screenshot = await CaptureScreenshot(component); await VisualRegressionTester.AssertMatchesSnapshot( screenshot, \"EcommerceMAUI.Views.CartView-SingleItem.png\" ); } Unit Testing Integration with xUnit for component testing: [Fact] public void ProductCard_WithValidProduct_DisplaysCorrectly() { // Arrange var product = PreviewData.FeaturedProduct; // Act var card = new ProductCard(product); // Assert Assert.Equal(product.Name, card.ProductName.Text); Assert.Equal(product.Price.ToString(\"C\"), card.Price.Text); } Development Workflow Iterative Development The samples demonstrate an efficient development workflow: Create Component: Build basic UI component Add Preview: Create initial [Preview] method Test States: Add previews for different states Refine UI: Use DevTools to iterate quickly Add Tests: Create visual regression tests Document: Update preview names and organization Best Practices from Samples Naming Conventions: Use descriptive, hierarchical names Group related previews with \"/\" delimiters Include state information in names Data Management: Centralize preview data in dedicated classes Use realistic data that represents actual usage Include edge cases and boundary conditions Component Design: Design components to be preview-friendly Support parameterless constructors where possible Use dependency injection for complex dependencies Running All Samples Build All Samples dotnet build samples/HotPreview-Samples.slnf Run Individual Samples # E-commerce sample cd samples/maui/EcommerceMAUI dotnet run # Project management sample cd samples/maui/DefaultTemplateWithContent dotnet run Visual Testing # Run visual regression tests dotnet test samples/maui/EcommerceMAUI --logger \"console;verbosity=detailed\" Learning Resources Component Examples Study the sample components to learn: Effective preview design patterns State management for previews Data binding in preview contexts Platform-specific adaptations Preview Strategies The samples showcase various preview strategies: Minimal previews for simple components Comprehensive state coverage for complex components Data-driven previews for components with varying content Interactive previews with command support Testing Approaches Learn from the sample testing strategies: Visual regression testing for UI consistency Unit testing for component logic Integration testing for component interactions Cross-platform testing for platform consistency"}, "index.html": {"href": "index.html", "title": "Hot Preview Documentation | Hot Preview Documentation", "summary": "Hot Preview Documentation Hot Preview lets you easily work on pages and controls in your app in isolation, making UI development and testing faster and more efficient for both humans and AI agents. Previews are similar to stories in Storybook for JavaScript and Previews in SwiftUI/Xcode and Jetpack Compose/Android Studio — but for .NET UI. Quick Start Get started with HotPreview in minutes: Install Hot Preview DevTools: dotnet tool install -g HotPreview.DevTools Add package reference to your app: <PackageReference Condition=\"$(Configuration) == 'Debug'\" Include=\"HotPreview.App.Maui\" Version=\"...\" /> Build and run your app in Debug mode Features 🚀 Streamlined Navigation - Jump directly to specific UI pages without complex navigation 🔄 Multi-state Testing - Visualize components with different data inputs and states 📱 Cross-platform Preview - View UI on multiple platforms simultaneously 🤖 AI Integration - Built-in MCP server for agentic AI workflows (Coming Soon) Get Started → | API Reference →"}}