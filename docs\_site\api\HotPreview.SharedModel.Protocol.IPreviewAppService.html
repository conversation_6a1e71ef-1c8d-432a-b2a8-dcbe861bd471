<!DOCTYPE html>
<html>
  <head>
    <meta charset="utf-8">
      <title>Interface IPreviewAppService | Hot Preview Documentation </title>
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <meta name="title" content="Interface IPreviewAppService | Hot Preview Documentation ">
      
      
      <link rel="icon" href="../favicon.ico">
      <link rel="stylesheet" href="../public/docfx.min.css">
      <link rel="stylesheet" href="../public/main.css">
      <meta name="docfx:navrel" content="../toc.html">
      <meta name="docfx:tocrel" content="toc.html">
      
      <meta name="docfx:rel" content="../">
      
      
      <meta name="docfx:docurl" content="https://github.com/BretJohnson/hot-preview/new/main/apiSpec/new?filename=HotPreview_SharedModel_Protocol_IPreviewAppService.md&amp;value=---%0Auid%3A%20HotPreview.SharedModel.Protocol.IPreviewAppService%0Asummary%3A%20&#39;*You%20can%20override%20summary%20for%20the%20API%20here%20using%20*MARKDOWN*%20syntax&#39;%0A---%0A%0A*Please%20type%20below%20more%20information%20about%20this%20API%3A*%0A%0A">
      <meta name="loc:inThisArticle" content="In this article">
      <meta name="loc:searchResultsCount" content="{count} results for &quot;{query}&quot;">
      <meta name="loc:searchNoResults" content="No results for &quot;{query}&quot;">
      <meta name="loc:tocFilter" content="Filter by title">
      <meta name="loc:nextArticle" content="Next">
      <meta name="loc:prevArticle" content="Previous">
      <meta name="loc:themeLight" content="Light">
      <meta name="loc:themeDark" content="Dark">
      <meta name="loc:themeAuto" content="Auto">
      <meta name="loc:changeTheme" content="Change theme">
      <meta name="loc:copy" content="Copy">
      <meta name="loc:downloadPdf" content="Download PDF">

      <script type="module" src="./../public/docfx.min.js"></script>

      <script>
        const theme = localStorage.getItem('theme') || 'auto'
        document.documentElement.setAttribute('data-bs-theme', theme === 'auto' ? (window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light') : theme)
      </script>

  </head>

  <body class="tex2jax_ignore" data-layout="" data-yaml-mime="ManagedReference">
    <header class="bg-body border-bottom">
      <nav id="autocollapse" class="navbar navbar-expand-md" role="navigation">
        <div class="container-xxl flex-nowrap">
          <a class="navbar-brand" href="../index.html">
            <img id="logo" class="svg" src="../logo.svg" alt="Hot Preview">
            Hot Preview
          </a>
          <button class="btn btn-lg d-md-none border-0" type="button" data-bs-toggle="collapse" data-bs-target="#navpanel" aria-controls="navpanel" aria-expanded="false" aria-label="Toggle navigation">
            <i class="bi bi-three-dots"></i>
          </button>
          <div class="collapse navbar-collapse" id="navpanel">
            <div id="navbar">
              <form class="search" role="search" id="search">
                <i class="bi bi-search"></i>
                <input class="form-control" id="search-query" type="search" disabled placeholder="Search" autocomplete="off" aria-label="Search">
              </form>
            </div>
          </div>
        </div>
      </nav>
    </header>

    <main class="container-xxl">
      <div class="toc-offcanvas">
        <div class="offcanvas-md offcanvas-start" tabindex="-1" id="tocOffcanvas" aria-labelledby="tocOffcanvasLabel">
          <div class="offcanvas-header">
            <h5 class="offcanvas-title" id="tocOffcanvasLabel">Table of Contents</h5>
            <button type="button" class="btn-close" data-bs-dismiss="offcanvas" data-bs-target="#tocOffcanvas" aria-label="Close"></button>
          </div>
          <div class="offcanvas-body">
            <nav class="toc" id="toc"></nav>
          </div>
        </div>
      </div>

      <div class="content">
        <div class="actionbar">
          <button class="btn btn-lg border-0 d-md-none" type="button" data-bs-toggle="offcanvas" data-bs-target="#tocOffcanvas" aria-controls="tocOffcanvas" aria-expanded="false" aria-label="Show table of contents">
            <i class="bi bi-list"></i>
          </button>

          <nav id="breadcrumb"></nav>
        </div>

        <article data-uid="HotPreview.SharedModel.Protocol.IPreviewAppService">



  <h1 id="HotPreview_SharedModel_Protocol_IPreviewAppService" data-uid="HotPreview.SharedModel.Protocol.IPreviewAppService" class="text-break">
Interface IPreviewAppService  <a class="header-action link-secondary" title="View source" href="https://github.com/BretJohnson/hot-preview/blob/main/src/HotPreview.SharedModel/Protocol/IPreviewAppService.cs/#L5"><i class="bi bi-code-slash"></i></a>
  </h1>

  <div class="facts text-secondary">
    <dl><dt>Namespace</dt><dd><a class="xref" href="HotPreview.html">HotPreview</a>.<a class="xref" href="HotPreview.SharedModel.html">SharedModel</a>.<a class="xref" href="HotPreview.SharedModel.Protocol.html">Protocol</a></dd></dl>
  <dl><dt>Assembly</dt><dd>HotPreview.SharedModel.dll</dd></dl>
  </div>

  <div class="markdown summary"></div>
  <div class="markdown conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public interface IPreviewAppService</code></pre>
  </div>













  <h2 class="section" id="methods">Methods
</h2>


  <a id="HotPreview_SharedModel_Protocol_IPreviewAppService_GetCommandsAsync_" data-uid="HotPreview.SharedModel.Protocol.IPreviewAppService.GetCommandsAsync*"></a>

  <h3 id="HotPreview_SharedModel_Protocol_IPreviewAppService_GetCommandsAsync" data-uid="HotPreview.SharedModel.Protocol.IPreviewAppService.GetCommandsAsync">
  GetCommandsAsync()
  <a class="header-action link-secondary" title="View source" href="https://github.com/BretJohnson/hot-preview/blob/main/src/HotPreview.SharedModel/Protocol/IPreviewAppService.cs/#L15"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">Task&lt;PreviewCommandInfo[]&gt; GetCommandsAsync()</code></pre>
  </div>


  <h4 class="section">Returns</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.threading.tasks.task-1">Task</a>&lt;<a class="xref" href="HotPreview.SharedModel.Protocol.PreviewCommandInfo.html">PreviewCommandInfo</a>[]&gt;</dt>
    <dd></dd>
  </dl>











  <a id="HotPreview_SharedModel_Protocol_IPreviewAppService_GetPreviewSnapshotAsync_" data-uid="HotPreview.SharedModel.Protocol.IPreviewAppService.GetPreviewSnapshotAsync*"></a>

  <h3 id="HotPreview_SharedModel_Protocol_IPreviewAppService_GetPreviewSnapshotAsync_System_String_System_String_" data-uid="HotPreview.SharedModel.Protocol.IPreviewAppService.GetPreviewSnapshotAsync(System.String,System.String)">
  GetPreviewSnapshotAsync(string, string)
  <a class="header-action link-secondary" title="View source" href="https://github.com/BretJohnson/hot-preview/blob/main/src/HotPreview.SharedModel/Protocol/IPreviewAppService.cs/#L13"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">Task&lt;byte[]&gt; GetPreviewSnapshotAsync(string uiComponentName, string previewName)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>uiComponentName</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.string">string</a></dt>
    <dd></dd>
    <dt><code>previewName</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.string">string</a></dt>
    <dd></dd>
  </dl>

  <h4 class="section">Returns</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.threading.tasks.task-1">Task</a>&lt;<a class="xref" href="https://learn.microsoft.com/dotnet/api/system.byte">byte</a>[]&gt;</dt>
    <dd></dd>
  </dl>











  <a id="HotPreview_SharedModel_Protocol_IPreviewAppService_GetUIComponentPreviewsAsync_" data-uid="HotPreview.SharedModel.Protocol.IPreviewAppService.GetUIComponentPreviewsAsync*"></a>

  <h3 id="HotPreview_SharedModel_Protocol_IPreviewAppService_GetUIComponentPreviewsAsync_System_String_" data-uid="HotPreview.SharedModel.Protocol.IPreviewAppService.GetUIComponentPreviewsAsync(System.String)">
  GetUIComponentPreviewsAsync(string)
  <a class="header-action link-secondary" title="View source" href="https://github.com/BretJohnson/hot-preview/blob/main/src/HotPreview.SharedModel/Protocol/IPreviewAppService.cs/#L9"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">Task&lt;string[]&gt; GetUIComponentPreviewsAsync(string componentName)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>componentName</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.string">string</a></dt>
    <dd></dd>
  </dl>

  <h4 class="section">Returns</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.threading.tasks.task-1">Task</a>&lt;<a class="xref" href="https://learn.microsoft.com/dotnet/api/system.string">string</a>[]&gt;</dt>
    <dd></dd>
  </dl>











  <a id="HotPreview_SharedModel_Protocol_IPreviewAppService_GetUIComponentsAsync_" data-uid="HotPreview.SharedModel.Protocol.IPreviewAppService.GetUIComponentsAsync*"></a>

  <h3 id="HotPreview_SharedModel_Protocol_IPreviewAppService_GetUIComponentsAsync" data-uid="HotPreview.SharedModel.Protocol.IPreviewAppService.GetUIComponentsAsync">
  GetUIComponentsAsync()
  <a class="header-action link-secondary" title="View source" href="https://github.com/BretJohnson/hot-preview/blob/main/src/HotPreview.SharedModel/Protocol/IPreviewAppService.cs/#L7"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">Task&lt;UIComponentInfo[]&gt; GetUIComponentsAsync()</code></pre>
  </div>


  <h4 class="section">Returns</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.threading.tasks.task-1">Task</a>&lt;<a class="xref" href="HotPreview.SharedModel.Protocol.UIComponentInfo.html">UIComponentInfo</a>[]&gt;</dt>
    <dd></dd>
  </dl>











  <a id="HotPreview_SharedModel_Protocol_IPreviewAppService_InvokeCommandAsync_" data-uid="HotPreview.SharedModel.Protocol.IPreviewAppService.InvokeCommandAsync*"></a>

  <h3 id="HotPreview_SharedModel_Protocol_IPreviewAppService_InvokeCommandAsync_System_String_" data-uid="HotPreview.SharedModel.Protocol.IPreviewAppService.InvokeCommandAsync(System.String)">
  InvokeCommandAsync(string)
  <a class="header-action link-secondary" title="View source" href="https://github.com/BretJohnson/hot-preview/blob/main/src/HotPreview.SharedModel/Protocol/IPreviewAppService.cs/#L17"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">Task InvokeCommandAsync(string commandName)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>commandName</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.string">string</a></dt>
    <dd></dd>
  </dl>

  <h4 class="section">Returns</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.threading.tasks.task">Task</a></dt>
    <dd></dd>
  </dl>











  <a id="HotPreview_SharedModel_Protocol_IPreviewAppService_NavigateToPreviewAsync_" data-uid="HotPreview.SharedModel.Protocol.IPreviewAppService.NavigateToPreviewAsync*"></a>

  <h3 id="HotPreview_SharedModel_Protocol_IPreviewAppService_NavigateToPreviewAsync_System_String_System_String_" data-uid="HotPreview.SharedModel.Protocol.IPreviewAppService.NavigateToPreviewAsync(System.String,System.String)">
  NavigateToPreviewAsync(string, string)
  <a class="header-action link-secondary" title="View source" href="https://github.com/BretJohnson/hot-preview/blob/main/src/HotPreview.SharedModel/Protocol/IPreviewAppService.cs/#L11"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">Task NavigateToPreviewAsync(string componentName, string previewName)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>componentName</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.string">string</a></dt>
    <dd></dd>
    <dt><code>previewName</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.string">string</a></dt>
    <dd></dd>
  </dl>

  <h4 class="section">Returns</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.threading.tasks.task">Task</a></dt>
    <dd></dd>
  </dl>












</article>

        <div class="contribution d-print-none">
          <a href="https://github.com/BretJohnson/hot-preview/blob/main/src/HotPreview.SharedModel/Protocol/IPreviewAppService.cs/#L5" class="edit-link">Edit this page</a>
        </div>


      </div>

      <div class="affix">
        <nav id="affix"></nav>
      </div>
    </main>

    <div class="container-xxl search-results" id="search-results"></div>

    <footer class="border-top text-secondary">
      <div class="container-xxl">
        <div class="flex-fill">
          <span>Made with <a href="https://dotnet.github.io/docfx">docfx</a></span>
        </div>
      </div>
    </footer>
  </body>
</html>
