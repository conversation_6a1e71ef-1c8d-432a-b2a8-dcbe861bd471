<!DOCTYPE html>
<html>
  <head>
    <meta charset="utf-8">
      <title>Class PreviewReflection | Hot Preview Documentation </title>
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <meta name="title" content="Class PreviewReflection | Hot Preview Documentation ">
      
      
      <link rel="icon" href="../favicon.ico">
      <link rel="stylesheet" href="../public/docfx.min.css">
      <link rel="stylesheet" href="../public/main.css">
      <meta name="docfx:navrel" content="../toc.html">
      <meta name="docfx:tocrel" content="toc.html">
      
      <meta name="docfx:rel" content="../">
      
      
      <meta name="docfx:docurl" content="https://github.com/BretJohnson/hot-preview/new/main/apiSpec/new?filename=HotPreview_SharedModel_App_PreviewReflection.md&amp;value=---%0Auid%3A%20HotPreview.SharedModel.App.PreviewReflection%0Asummary%3A%20&#39;*You%20can%20override%20summary%20for%20the%20API%20here%20using%20*MARKDOWN*%20syntax&#39;%0A---%0A%0A*Please%20type%20below%20more%20information%20about%20this%20API%3A*%0A%0A">
      <meta name="loc:inThisArticle" content="In this article">
      <meta name="loc:searchResultsCount" content="{count} results for &quot;{query}&quot;">
      <meta name="loc:searchNoResults" content="No results for &quot;{query}&quot;">
      <meta name="loc:tocFilter" content="Filter by title">
      <meta name="loc:nextArticle" content="Next">
      <meta name="loc:prevArticle" content="Previous">
      <meta name="loc:themeLight" content="Light">
      <meta name="loc:themeDark" content="Dark">
      <meta name="loc:themeAuto" content="Auto">
      <meta name="loc:changeTheme" content="Change theme">
      <meta name="loc:copy" content="Copy">
      <meta name="loc:downloadPdf" content="Download PDF">

      <script type="module" src="./../public/docfx.min.js"></script>

      <script>
        const theme = localStorage.getItem('theme') || 'auto'
        document.documentElement.setAttribute('data-bs-theme', theme === 'auto' ? (window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light') : theme)
      </script>

  </head>

  <body class="tex2jax_ignore" data-layout="" data-yaml-mime="ManagedReference">
    <header class="bg-body border-bottom">
      <nav id="autocollapse" class="navbar navbar-expand-md" role="navigation">
        <div class="container-xxl flex-nowrap">
          <a class="navbar-brand" href="../index.html">
            <img id="logo" class="svg" src="../logo.svg" alt="Hot Preview">
            Hot Preview
          </a>
          <button class="btn btn-lg d-md-none border-0" type="button" data-bs-toggle="collapse" data-bs-target="#navpanel" aria-controls="navpanel" aria-expanded="false" aria-label="Toggle navigation">
            <i class="bi bi-three-dots"></i>
          </button>
          <div class="collapse navbar-collapse" id="navpanel">
            <div id="navbar">
              <form class="search" role="search" id="search">
                <i class="bi bi-search"></i>
                <input class="form-control" id="search-query" type="search" disabled placeholder="Search" autocomplete="off" aria-label="Search">
              </form>
            </div>
          </div>
        </div>
      </nav>
    </header>

    <main class="container-xxl">
      <div class="toc-offcanvas">
        <div class="offcanvas-md offcanvas-start" tabindex="-1" id="tocOffcanvas" aria-labelledby="tocOffcanvasLabel">
          <div class="offcanvas-header">
            <h5 class="offcanvas-title" id="tocOffcanvasLabel">Table of Contents</h5>
            <button type="button" class="btn-close" data-bs-dismiss="offcanvas" data-bs-target="#tocOffcanvas" aria-label="Close"></button>
          </div>
          <div class="offcanvas-body">
            <nav class="toc" id="toc"></nav>
          </div>
        </div>
      </div>

      <div class="content">
        <div class="actionbar">
          <button class="btn btn-lg border-0 d-md-none" type="button" data-bs-toggle="offcanvas" data-bs-target="#tocOffcanvas" aria-controls="tocOffcanvas" aria-expanded="false" aria-label="Show table of contents">
            <i class="bi bi-list"></i>
          </button>

          <nav id="breadcrumb"></nav>
        </div>

        <article data-uid="HotPreview.SharedModel.App.PreviewReflection">



  <h1 id="HotPreview_SharedModel_App_PreviewReflection" data-uid="HotPreview.SharedModel.App.PreviewReflection" class="text-break">
Class PreviewReflection  <a class="header-action link-secondary" title="View source" href="https://github.com/BretJohnson/hot-preview/blob/main/src/HotPreview.SharedModel/App/PreviewReflection.cs/#L6"><i class="bi bi-code-slash"></i></a>
  </h1>

  <div class="facts text-secondary">
    <dl><dt>Namespace</dt><dd><a class="xref" href="HotPreview.html">HotPreview</a>.<a class="xref" href="HotPreview.SharedModel.html">SharedModel</a>.<a class="xref" href="HotPreview.SharedModel.App.html">App</a></dd></dl>
  <dl><dt>Assembly</dt><dd>HotPreview.SharedModel.dll</dd></dl>
  </div>

  <div class="markdown summary"></div>
  <div class="markdown conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public abstract class PreviewReflection : PreviewBase</code></pre>
  </div>




  <dl class="typelist inheritance">
    <dt>Inheritance</dt>
    <dd>
      <div><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object">object</a></div>
      <div><a class="xref" href="HotPreview.SharedModel.PreviewBase.html">PreviewBase</a></div>
      <div><span class="xref">PreviewReflection</span></div>
    </dd>
  </dl>


  <dl class="typelist derived">
    <dt>Derived</dt>
    <dd>
      <div><a class="xref" href="HotPreview.SharedModel.App.PreviewClassReflection.html">PreviewClassReflection</a></div>
      <div><a class="xref" href="HotPreview.SharedModel.App.PreviewStaticMethodReflection.html">PreviewStaticMethodReflection</a></div>
    </dd>
  </dl>

  <dl class="typelist inheritedMembers">
    <dt>Inherited Members</dt>
    <dd>
    <div>
      <a class="xref" href="HotPreview.SharedModel.PreviewBase.html#HotPreview_SharedModel_PreviewBase_IsAutoGenerated">PreviewBase.IsAutoGenerated</a>
    </div>
    <div>
      <a class="xref" href="HotPreview.SharedModel.PreviewBase.html#HotPreview_SharedModel_PreviewBase_DisplayName">PreviewBase.DisplayName</a>
    </div>
    <div>
      <a class="xref" href="HotPreview.SharedModel.PreviewBase.html#HotPreview_SharedModel_PreviewBase_DisplayNameOverride">PreviewBase.DisplayNameOverride</a>
    </div>
    <div>
      <a class="xref" href="HotPreview.SharedModel.PreviewBase.html#HotPreview_SharedModel_PreviewBase_Name">PreviewBase.Name</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)">object.Equals(object)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)">object.Equals(object, object)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object.gethashcode">object.GetHashCode()</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object.gettype">object.GetType()</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone">object.MemberwiseClone()</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object.referenceequals">object.ReferenceEquals(object, object)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object.tostring">object.ToString()</a>
    </div>
  </dd></dl>






  <h2 class="section" id="constructors">Constructors
</h2>


  <a id="HotPreview_SharedModel_App_PreviewReflection__ctor_" data-uid="HotPreview.SharedModel.App.PreviewReflection.#ctor*"></a>

  <h3 id="HotPreview_SharedModel_App_PreviewReflection__ctor_HotPreview_PreviewAttribute_" data-uid="HotPreview.SharedModel.App.PreviewReflection.#ctor(HotPreview.PreviewAttribute)">
  PreviewReflection(PreviewAttribute)
  <a class="header-action link-secondary" title="View source" href="https://github.com/BretJohnson/hot-preview/blob/main/src/HotPreview.SharedModel/App/PreviewReflection.cs/#L10"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public PreviewReflection(PreviewAttribute previewAttribute)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>previewAttribute</code> <a class="xref" href="HotPreview.PreviewAttribute.html">PreviewAttribute</a></dt>
    <dd></dd>
  </dl>












  <a id="HotPreview_SharedModel_App_PreviewReflection__ctor_" data-uid="HotPreview.SharedModel.App.PreviewReflection.#ctor*"></a>

  <h3 id="HotPreview_SharedModel_App_PreviewReflection__ctor_System_Type_" data-uid="HotPreview.SharedModel.App.PreviewReflection.#ctor(System.Type)">
  PreviewReflection(Type)
  <a class="header-action link-secondary" title="View source" href="https://github.com/BretJohnson/hot-preview/blob/main/src/HotPreview.SharedModel/App/PreviewReflection.cs/#L15"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public PreviewReflection(Type uiComponentType)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>uiComponentType</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.type">Type</a></dt>
    <dd></dd>
  </dl>












  <h2 class="section" id="properties">Properties
</h2>


  <a id="HotPreview_SharedModel_App_PreviewReflection_DefaultUIComponentType_" data-uid="HotPreview.SharedModel.App.PreviewReflection.DefaultUIComponentType*"></a>

  <h3 id="HotPreview_SharedModel_App_PreviewReflection_DefaultUIComponentType" data-uid="HotPreview.SharedModel.App.PreviewReflection.DefaultUIComponentType">
  DefaultUIComponentType
  <a class="header-action link-secondary" title="View source" href="https://github.com/BretJohnson/hot-preview/blob/main/src/HotPreview.SharedModel/App/PreviewReflection.cs/#L47"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"><p>Default component type (when there is one), e.g. based on the method return type. If there's no default
type, this will be null.</p>
</div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public abstract Type? DefaultUIComponentType { get; }</code></pre>
  </div>





  <h4 class="section">Property Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.type">Type</a></dt>
    <dd></dd>
  </dl>








  <a id="HotPreview_SharedModel_App_PreviewReflection_UIComponentType_" data-uid="HotPreview.SharedModel.App.PreviewReflection.UIComponentType*"></a>

  <h3 id="HotPreview_SharedModel_App_PreviewReflection_UIComponentType" data-uid="HotPreview.SharedModel.App.PreviewReflection.UIComponentType">
  UIComponentType
  <a class="header-action link-secondary" title="View source" href="https://github.com/BretJohnson/hot-preview/blob/main/src/HotPreview.SharedModel/App/PreviewReflection.cs/#L27"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public Type UIComponentType { get; }</code></pre>
  </div>





  <h4 class="section">Property Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.type">Type</a></dt>
    <dd></dd>
  </dl>








  <h2 class="section" id="methods">Methods
</h2>


  <a id="HotPreview_SharedModel_App_PreviewReflection_Create_" data-uid="HotPreview.SharedModel.App.PreviewReflection.Create*"></a>

  <h3 id="HotPreview_SharedModel_App_PreviewReflection_Create" data-uid="HotPreview.SharedModel.App.PreviewReflection.Create">
  Create()
  <a class="header-action link-secondary" title="View source" href="https://github.com/BretJohnson/hot-preview/blob/main/src/HotPreview.SharedModel/App/PreviewReflection.cs/#L25"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"><p>Create an instance of the preview. Normally this returns an instance of a UI framework control/page, suitable
for display.</p>
</div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public abstract object Create()</code></pre>
  </div>


  <h4 class="section">Returns</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object">object</a></dt>
    <dd><p>instantiated preview</p>
</dd>
  </dl>











  <a id="HotPreview_SharedModel_App_PreviewReflection_GetPreviewInfo_" data-uid="HotPreview.SharedModel.App.PreviewReflection.GetPreviewInfo*"></a>

  <h3 id="HotPreview_SharedModel_App_PreviewReflection_GetPreviewInfo" data-uid="HotPreview.SharedModel.App.PreviewReflection.GetPreviewInfo">
  GetPreviewInfo()
  <a class="header-action link-secondary" title="View source" href="https://github.com/BretJohnson/hot-preview/blob/main/src/HotPreview.SharedModel/App/PreviewReflection.cs/#L59"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"><p>Gets the preview information including name, display name, auto-generated status, and preview type.</p>
</div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public virtual PreviewInfo GetPreviewInfo()</code></pre>
  </div>


  <h4 class="section">Returns</h4>
  <dl class="parameters">
    <dt><a class="xref" href="HotPreview.SharedModel.Protocol.PreviewInfo.html">PreviewInfo</a></dt>
    <dd><p>A PreviewInfo record with the preview details, for use in the JSON RPC protocol</p>
</dd>
  </dl>











  <a id="HotPreview_SharedModel_App_PreviewReflection_GetPreviewTypeInfo_" data-uid="HotPreview.SharedModel.App.PreviewReflection.GetPreviewTypeInfo*"></a>

  <h3 id="HotPreview_SharedModel_App_PreviewReflection_GetPreviewTypeInfo" data-uid="HotPreview.SharedModel.App.PreviewReflection.GetPreviewTypeInfo">
  GetPreviewTypeInfo()
  <a class="header-action link-secondary" title="View source" href="https://github.com/BretJohnson/hot-preview/blob/main/src/HotPreview.SharedModel/App/PreviewReflection.cs/#L53"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"><p>Gets the preview type (e.g., &quot;Class&quot;, &quot;StaticMethod&quot;).</p>
</div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public abstract string GetPreviewTypeInfo()</code></pre>
  </div>


  <h4 class="section">Returns</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.string">string</a></dt>
    <dd><p>A string representing the preview type</p>
</dd>
  </dl>












</article>

        <div class="contribution d-print-none">
          <a href="https://github.com/BretJohnson/hot-preview/blob/main/src/HotPreview.SharedModel/App/PreviewReflection.cs/#L6" class="edit-link">Edit this page</a>
        </div>


      </div>

      <div class="affix">
        <nav id="affix"></nav>
      </div>
    </main>

    <div class="container-xxl search-results" id="search-results"></div>

    <footer class="border-top text-secondary">
      <div class="container-xxl">
        <div class="flex-fill">
          <span>Made with <a href="https://dotnet.github.io/docfx">docfx</a></span>
        </div>
      </div>
    </footer>
  </body>
</html>
