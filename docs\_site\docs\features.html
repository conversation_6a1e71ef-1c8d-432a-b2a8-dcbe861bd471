<!DOCTYPE html>
<html>
  <head>
    <meta charset="utf-8">
      <title>Features | Hot Preview Documentation </title>
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <meta name="title" content="Features | Hot Preview Documentation ">
      
      
      <link rel="icon" href="../favicon.ico">
      <link rel="stylesheet" href="../public/docfx.min.css">
      <link rel="stylesheet" href="../public/main.css">
      <meta name="docfx:navrel" content="../toc.html">
      <meta name="docfx:tocrel" content="toc.html">
      
      <meta name="docfx:rel" content="../">
      
      
      <meta name="docfx:docurl" content="https://github.com/BretJohnson/hot-preview/blob/main/docs/docs/features.md/#L1">
      <meta name="loc:inThisArticle" content="In this article">
      <meta name="loc:searchResultsCount" content="{count} results for &quot;{query}&quot;">
      <meta name="loc:searchNoResults" content="No results for &quot;{query}&quot;">
      <meta name="loc:tocFilter" content="Filter by title">
      <meta name="loc:nextArticle" content="Next">
      <meta name="loc:prevArticle" content="Previous">
      <meta name="loc:themeLight" content="Light">
      <meta name="loc:themeDark" content="Dark">
      <meta name="loc:themeAuto" content="Auto">
      <meta name="loc:changeTheme" content="Change theme">
      <meta name="loc:copy" content="Copy">
      <meta name="loc:downloadPdf" content="Download PDF">

      <script type="module" src="./../public/docfx.min.js"></script>

      <script>
        const theme = localStorage.getItem('theme') || 'auto'
        document.documentElement.setAttribute('data-bs-theme', theme === 'auto' ? (window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light') : theme)
      </script>

  </head>

  <body class="tex2jax_ignore" data-layout="" data-yaml-mime="">
    <header class="bg-body border-bottom">
      <nav id="autocollapse" class="navbar navbar-expand-md" role="navigation">
        <div class="container-xxl flex-nowrap">
          <a class="navbar-brand" href="../index.html">
            <img id="logo" class="svg" src="../logo.svg" alt="Hot Preview">
            Hot Preview
          </a>
          <button class="btn btn-lg d-md-none border-0" type="button" data-bs-toggle="collapse" data-bs-target="#navpanel" aria-controls="navpanel" aria-expanded="false" aria-label="Toggle navigation">
            <i class="bi bi-three-dots"></i>
          </button>
          <div class="collapse navbar-collapse" id="navpanel">
            <div id="navbar">
              <form class="search" role="search" id="search">
                <i class="bi bi-search"></i>
                <input class="form-control" id="search-query" type="search" disabled placeholder="Search" autocomplete="off" aria-label="Search">
              </form>
            </div>
          </div>
        </div>
      </nav>
    </header>

    <main class="container-xxl">
      <div class="toc-offcanvas">
        <div class="offcanvas-md offcanvas-start" tabindex="-1" id="tocOffcanvas" aria-labelledby="tocOffcanvasLabel">
          <div class="offcanvas-header">
            <h5 class="offcanvas-title" id="tocOffcanvasLabel">Table of Contents</h5>
            <button type="button" class="btn-close" data-bs-dismiss="offcanvas" data-bs-target="#tocOffcanvas" aria-label="Close"></button>
          </div>
          <div class="offcanvas-body">
            <nav class="toc" id="toc"></nav>
          </div>
        </div>
      </div>

      <div class="content">
        <div class="actionbar">
          <button class="btn btn-lg border-0 d-md-none" type="button" data-bs-toggle="offcanvas" data-bs-target="#tocOffcanvas" aria-controls="tocOffcanvas" aria-expanded="false" aria-label="Show table of contents">
            <i class="bi bi-list"></i>
          </button>

          <nav id="breadcrumb"></nav>
        </div>

        <article data-uid="">
<h1 id="features">Features</h1>

<p>HotPreview provides a comprehensive set of features for efficient UI development and testing in .NET applications.</p>
<h2 id="core-features">Core Features</h2>
<h3 id="-streamlined-navigation">🚀 Streamlined Navigation</h3>
<p>Jump directly to specific UI pages and components without navigating through multiple app screens.</p>
<p><strong>Benefits:</strong></p>
<ul>
<li>Dramatically reduces development time</li>
<li>Eliminates repetitive manual testing workflows</li>
<li>Enables instant access to any component state</li>
</ul>
<p><strong>How it works:</strong></p>
<ul>
<li>DevTools displays a hierarchical tree of all UI components</li>
<li>Click any component to navigate directly to it in your running app</li>
<li>No need to manually navigate through complex app flows</li>
</ul>
<h3 id="-multi-state-testing">🔄 Multi-state Testing</h3>
<p>Quickly visualize UI components with different data inputs and states, ensuring responsive and robust interfaces across all scenarios.</p>
<p><strong>Supported scenarios:</strong></p>
<ul>
<li>Empty states</li>
<li>Loading states</li>
<li>Error conditions</li>
<li>Different data volumes (single item, multiple items, large datasets)</li>
<li>Various user permissions or app states</li>
</ul>
<p><strong>Example:</strong></p>
<pre><code class="lang-csharp">#if PREVIEWS
    [Preview(&quot;Empty Cart&quot;)]
    public static CartView EmptyCart() =&gt; new(PreviewData.GetCart(0));

    [Preview(&quot;Single Item&quot;)]
    public static CartView SingleItem() =&gt; new(PreviewData.GetCart(1));

    [Preview(&quot;Full Cart&quot;)]
    public static CartView FullCart() =&gt; new(PreviewData.GetCart(10));

    [Preview(&quot;Cart with Error&quot;)]
    public static CartView ErrorCart() =&gt; new(PreviewData.GetCartWithError());
#endif
</code></pre>
<h3 id="-cross-platform-visualization">📱 Cross-Platform Visualization</h3>
<p>View your UI on multiple platforms simultaneously, enabling instant cross-platform comparison and consistency validation.</p>
<p><strong>Capabilities:</strong></p>
<ul>
<li>Run the same app on Windows, Android, iOS side-by-side</li>
<li>Navigate to the same component across platforms instantly</li>
<li>Compare visual consistency and behavior</li>
<li>Test platform-specific adaptations</li>
</ul>
<h3 id="-devtools-integration">🛠️ DevTools Integration</h3>
<p>A powerful desktop application that serves as your command center for UI development.</p>
<p><strong>DevTools features:</strong></p>
<ul>
<li><strong>Component Tree</strong>: Hierarchical view of all UI components</li>
<li><strong>Live Navigation</strong>: Click-to-navigate functionality</li>
<li><strong>Command Execution</strong>: Run preview commands for state management</li>
<li><strong>Multi-App Support</strong>: Connect multiple app instances simultaneously</li>
<li><strong>Auto-Discovery</strong>: Automatic detection of components and previews</li>
</ul>
<h2 id="auto-generation-features">Auto-Generation Features</h2>
<h3 id="intelligent-component-discovery">Intelligent Component Discovery</h3>
<p>HotPreview automatically discovers and creates previews for UI components without any configuration.</p>
<p><strong>Auto-generated for:</strong></p>
<ul>
<li><strong>Pages</strong>: Any class inheriting from platform page base types</li>
<li><strong>Controls</strong>: Any class inheriting from platform view base types</li>
<li><strong>Dependency Injection</strong>: Components with constructor parameters resolved via DI</li>
</ul>
<p><strong>Customizable discovery:</strong></p>
<ul>
<li>Configure platform-specific base types</li>
<li>Control auto-generation per component</li>
<li>Organize components into categories</li>
</ul>
<h3 id="zero-configuration-setup">Zero-Configuration Setup</h3>
<p>Get started immediately without complex configuration:</p>
<ol>
<li>Install DevTools globally</li>
<li>Add package reference to your app</li>
<li>Build and run in Debug mode</li>
</ol>
<h2 id="advanced-features">Advanced Features</h2>
<h3 id="custom-preview-commands">Custom Preview Commands</h3>
<p>Define commands that can be executed from DevTools to manipulate app state or perform testing actions.</p>
<pre><code class="lang-csharp">#if PREVIEWS
    [PreviewCommand(&quot;Reset User Session&quot;)]
    public static void ResetSession()
    {
        UserService.ClearSession();
        App.Current.MainPage = new LoginPage();
    }

    [PreviewCommand(&quot;Load Test Data&quot;)]
    public static async Task LoadTestData()
    {
        await DataService.LoadSampleDataAsync();
    }
#endif
</code></pre>
<h3 id="hierarchical-organization">Hierarchical Organization</h3>
<p>Use path-like naming to organize components and previews into logical hierarchies.</p>
<pre><code class="lang-csharp">#if PREVIEWS
    [Preview(&quot;Authentication/Login Form&quot;)]
    public static LoginView LoginForm() =&gt; new();

    [Preview(&quot;Authentication/Registration Form&quot;)]
    public static RegisterView RegisterForm() =&gt; new();

    [Preview(&quot;Shop/Product Card/Featured&quot;)]
    public static ProductCard FeaturedProduct() =&gt; new(PreviewData.GetFeaturedProduct());
#endif
</code></pre>
<h3 id="conditional-compilation">Conditional Compilation</h3>
<p>Ensure preview code is completely excluded from release builds.</p>
<pre><code class="lang-csharp">#if PREVIEWS
    // All preview code goes here
    // Excluded from Release builds automatically
#endif
</code></pre>
<h2 id="upcoming-features">Upcoming Features</h2>
<h3 id="-ai-driven-development-coming-soon">🤖 AI-Driven Development <em>(Coming Soon)</em></h3>
<p>Built-in MCP (Model Context Protocol) server for AI-assisted UI development workflows.</p>
<p><strong>Planned capabilities:</strong></p>
<ul>
<li>AI agents can generate and execute previews</li>
<li>Automatic screenshot comparison and feedback</li>
<li>Visual regression testing with AI analysis</li>
<li>Intelligent component generation based on visual requirements</li>
</ul>
<h3 id="-visual-testing-utils">📊 Visual Testing Utils</h3>
<p>Advanced utilities for visual regression testing and comparison.</p>
<p><strong>Current capabilities:</strong></p>
<ul>
<li>Image snapshot generation</li>
<li>Visual difference detection</li>
<li>Automated screenshot comparison</li>
<li>Integration with testing frameworks</li>
</ul>
<h3 id="-enhanced-devtools">🔧 Enhanced DevTools</h3>
<p>Continuous improvements to the DevTools experience:</p>
<ul>
<li>Performance profiling</li>
<li>Component dependency visualization</li>
<li>Advanced filtering and search</li>
<li>Custom themes and layouts</li>
</ul>
<h2 id="integration-features">Integration Features</h2>
<h3 id="msbuild-integration">MSBuild Integration</h3>
<p>Seamless integration with your build process:</p>
<ul>
<li>Automatic DevTools launch during Debug builds</li>
<li>App settings generation</li>
<li>Conditional compilation symbol management</li>
</ul>
<h3 id="platform-support">Platform Support</h3>
<p><strong>Current support:</strong></p>
<ul>
<li>.NET MAUI (Windows, Android, iOS, macOS)</li>
</ul>
<p><strong>Planned support:</strong></p>
<ul>
<li>WPF</li>
<li>WinUI 3</li>
<li>Uno Platform</li>
<li>Avalonia UI</li>
</ul>
<h3 id="development-workflow">Development Workflow</h3>
<p><strong>IDE Integration:</strong></p>
<ul>
<li>Works with Visual Studio</li>
<li>Compatible with VS Code</li>
<li>Command-line friendly</li>
</ul>
<p><strong>Testing Integration:</strong></p>
<ul>
<li>xUnit compatibility</li>
<li>Visual regression testing</li>
<li>Snapshot testing utilities</li>
</ul>
<h2 id="performance-features">Performance Features</h2>
<h3 id="lazy-loading">Lazy Loading</h3>
<p>Components and previews are loaded on-demand to maintain performance with large applications.</p>
<h3 id="efficient-discovery">Efficient Discovery</h3>
<p>Roslyn-based analysis for fast component discovery without runtime overhead.</p>
<h3 id="memory-management">Memory Management</h3>
<p>Smart memory management to handle multiple app instances and component states efficiently.</p>

</article>

        <div class="contribution d-print-none">
          <a href="https://github.com/BretJohnson/hot-preview/blob/main/docs/docs/features.md/#L1" class="edit-link">Edit this page</a>
        </div>

        <div class="next-article d-print-none border-top" id="nextArticle"></div>

      </div>

      <div class="affix">
        <nav id="affix"></nav>
      </div>
    </main>

    <div class="container-xxl search-results" id="search-results"></div>

    <footer class="border-top text-secondary">
      <div class="container-xxl">
        <div class="flex-fill">
          <span>Made with <a href="https://dotnet.github.io/docfx">docfx</a></span>
        </div>
      </div>
    </footer>
  </body>
</html>
