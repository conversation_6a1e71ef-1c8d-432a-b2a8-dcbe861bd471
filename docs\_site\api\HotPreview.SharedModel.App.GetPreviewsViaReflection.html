<!DOCTYPE html>
<html>
  <head>
    <meta charset="utf-8">
      <title>Class GetPreviewsViaReflection | Hot Preview Documentation </title>
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <meta name="title" content="Class GetPreviewsViaReflection | Hot Preview Documentation ">
      
      
      <link rel="icon" href="../favicon.ico">
      <link rel="stylesheet" href="../public/docfx.min.css">
      <link rel="stylesheet" href="../public/main.css">
      <meta name="docfx:navrel" content="../toc.html">
      <meta name="docfx:tocrel" content="toc.html">
      
      <meta name="docfx:rel" content="../">
      
      
      <meta name="docfx:docurl" content="https://github.com/BretJohnson/hot-preview/new/main/apiSpec/new?filename=HotPreview_SharedModel_App_GetPreviewsViaReflection.md&amp;value=---%0Auid%3A%20HotPreview.SharedModel.App.GetPreviewsViaReflection%0Asummary%3A%20&#39;*You%20can%20override%20summary%20for%20the%20API%20here%20using%20*MARKDOWN*%20syntax&#39;%0A---%0A%0A*Please%20type%20below%20more%20information%20about%20this%20API%3A*%0A%0A">
      <meta name="loc:inThisArticle" content="In this article">
      <meta name="loc:searchResultsCount" content="{count} results for &quot;{query}&quot;">
      <meta name="loc:searchNoResults" content="No results for &quot;{query}&quot;">
      <meta name="loc:tocFilter" content="Filter by title">
      <meta name="loc:nextArticle" content="Next">
      <meta name="loc:prevArticle" content="Previous">
      <meta name="loc:themeLight" content="Light">
      <meta name="loc:themeDark" content="Dark">
      <meta name="loc:themeAuto" content="Auto">
      <meta name="loc:changeTheme" content="Change theme">
      <meta name="loc:copy" content="Copy">
      <meta name="loc:downloadPdf" content="Download PDF">

      <script type="module" src="./../public/docfx.min.js"></script>

      <script>
        const theme = localStorage.getItem('theme') || 'auto'
        document.documentElement.setAttribute('data-bs-theme', theme === 'auto' ? (window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light') : theme)
      </script>

  </head>

  <body class="tex2jax_ignore" data-layout="" data-yaml-mime="ManagedReference">
    <header class="bg-body border-bottom">
      <nav id="autocollapse" class="navbar navbar-expand-md" role="navigation">
        <div class="container-xxl flex-nowrap">
          <a class="navbar-brand" href="../index.html">
            <img id="logo" class="svg" src="../logo.svg" alt="Hot Preview">
            Hot Preview
          </a>
          <button class="btn btn-lg d-md-none border-0" type="button" data-bs-toggle="collapse" data-bs-target="#navpanel" aria-controls="navpanel" aria-expanded="false" aria-label="Toggle navigation">
            <i class="bi bi-three-dots"></i>
          </button>
          <div class="collapse navbar-collapse" id="navpanel">
            <div id="navbar">
              <form class="search" role="search" id="search">
                <i class="bi bi-search"></i>
                <input class="form-control" id="search-query" type="search" disabled placeholder="Search" autocomplete="off" aria-label="Search">
              </form>
            </div>
          </div>
        </div>
      </nav>
    </header>

    <main class="container-xxl">
      <div class="toc-offcanvas">
        <div class="offcanvas-md offcanvas-start" tabindex="-1" id="tocOffcanvas" aria-labelledby="tocOffcanvasLabel">
          <div class="offcanvas-header">
            <h5 class="offcanvas-title" id="tocOffcanvasLabel">Table of Contents</h5>
            <button type="button" class="btn-close" data-bs-dismiss="offcanvas" data-bs-target="#tocOffcanvas" aria-label="Close"></button>
          </div>
          <div class="offcanvas-body">
            <nav class="toc" id="toc"></nav>
          </div>
        </div>
      </div>

      <div class="content">
        <div class="actionbar">
          <button class="btn btn-lg border-0 d-md-none" type="button" data-bs-toggle="offcanvas" data-bs-target="#tocOffcanvas" aria-controls="tocOffcanvas" aria-expanded="false" aria-label="Show table of contents">
            <i class="bi bi-list"></i>
          </button>

          <nav id="breadcrumb"></nav>
        </div>

        <article data-uid="HotPreview.SharedModel.App.GetPreviewsViaReflection">



  <h1 id="HotPreview_SharedModel_App_GetPreviewsViaReflection" data-uid="HotPreview.SharedModel.App.GetPreviewsViaReflection" class="text-break">
Class GetPreviewsViaReflection  <a class="header-action link-secondary" title="View source" href="https://github.com/BretJohnson/hot-preview/blob/main/src/HotPreview.SharedModel/App/GetPreviewsViaReflection.cs/#L8"><i class="bi bi-code-slash"></i></a>
  </h1>

  <div class="facts text-secondary">
    <dl><dt>Namespace</dt><dd><a class="xref" href="HotPreview.html">HotPreview</a>.<a class="xref" href="HotPreview.SharedModel.html">SharedModel</a>.<a class="xref" href="HotPreview.SharedModel.App.html">App</a></dd></dl>
  <dl><dt>Assembly</dt><dd>HotPreview.SharedModel.dll</dd></dl>
  </div>

  <div class="markdown summary"></div>
  <div class="markdown conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public class GetPreviewsViaReflection : PreviewsManagerBuilderBase&lt;UIComponentReflection, PreviewReflection, PreviewCommandReflection&gt;</code></pre>
  </div>




  <dl class="typelist inheritance">
    <dt>Inheritance</dt>
    <dd>
      <div><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object">object</a></div>
      <div><a class="xref" href="HotPreview.SharedModel.PreviewsManagerBuilderBase-3.html">PreviewsManagerBuilderBase</a>&lt;<a class="xref" href="HotPreview.SharedModel.App.UIComponentReflection.html">UIComponentReflection</a>, <a class="xref" href="HotPreview.SharedModel.App.PreviewReflection.html">PreviewReflection</a>, <a class="xref" href="HotPreview.SharedModel.App.PreviewCommandReflection.html">PreviewCommandReflection</a>&gt;</div>
      <div><span class="xref">GetPreviewsViaReflection</span></div>
    </dd>
  </dl>



  <dl class="typelist inheritedMembers">
    <dt>Inherited Members</dt>
    <dd>
    <div>
      <a class="xref" href="HotPreview.SharedModel.PreviewsManagerBuilderBase-3.html#HotPreview_SharedModel_PreviewsManagerBuilderBase_3__uiComponentsByName">PreviewsManagerBuilderBase&lt;UIComponentReflection, PreviewReflection, PreviewCommandReflection&gt;._uiComponentsByName</a>
    </div>
    <div>
      <a class="xref" href="HotPreview.SharedModel.PreviewsManagerBuilderBase-3.html#HotPreview_SharedModel_PreviewsManagerBuilderBase_3__categories">PreviewsManagerBuilderBase&lt;UIComponentReflection, PreviewReflection, PreviewCommandReflection&gt;._categories</a>
    </div>
    <div>
      <a class="xref" href="HotPreview.SharedModel.PreviewsManagerBuilderBase-3.html#HotPreview_SharedModel_PreviewsManagerBuilderBase_3__baseTypes">PreviewsManagerBuilderBase&lt;UIComponentReflection, PreviewReflection, PreviewCommandReflection&gt;._baseTypes</a>
    </div>
    <div>
      <a class="xref" href="HotPreview.SharedModel.PreviewsManagerBuilderBase-3.html#HotPreview_SharedModel_PreviewsManagerBuilderBase_3__commandsByName">PreviewsManagerBuilderBase&lt;UIComponentReflection, PreviewReflection, PreviewCommandReflection&gt;._commandsByName</a>
    </div>
    <div>
      <a class="xref" href="HotPreview.SharedModel.PreviewsManagerBuilderBase-3.html#HotPreview_SharedModel_PreviewsManagerBuilderBase_3_UIComponentsByName">PreviewsManagerBuilderBase&lt;UIComponentReflection, PreviewReflection, PreviewCommandReflection&gt;.UIComponentsByName</a>
    </div>
    <div>
      <a class="xref" href="HotPreview.SharedModel.PreviewsManagerBuilderBase-3.html#HotPreview_SharedModel_PreviewsManagerBuilderBase_3_Categories">PreviewsManagerBuilderBase&lt;UIComponentReflection, PreviewReflection, PreviewCommandReflection&gt;.Categories</a>
    </div>
    <div>
      <a class="xref" href="HotPreview.SharedModel.PreviewsManagerBuilderBase-3.html#HotPreview_SharedModel_PreviewsManagerBuilderBase_3_CommandsByName">PreviewsManagerBuilderBase&lt;UIComponentReflection, PreviewReflection, PreviewCommandReflection&gt;.CommandsByName</a>
    </div>
    <div>
      <a class="xref" href="HotPreview.SharedModel.PreviewsManagerBuilderBase-3.html#HotPreview_SharedModel_PreviewsManagerBuilderBase_3_AddUIComponentBaseType_HotPreview_SharedModel_UIComponentKind_System_String_System_String_">PreviewsManagerBuilderBase&lt;UIComponentReflection, PreviewReflection, PreviewCommandReflection&gt;.AddUIComponentBaseType(UIComponentKind, string, string)</a>
    </div>
    <div>
      <a class="xref" href="HotPreview.SharedModel.PreviewsManagerBuilderBase-3.html#HotPreview_SharedModel_PreviewsManagerBuilderBase_3_AddUIComponent__0_">PreviewsManagerBuilderBase&lt;UIComponentReflection, PreviewReflection, PreviewCommandReflection&gt;.AddUIComponent(UIComponentReflection)</a>
    </div>
    <div>
      <a class="xref" href="HotPreview.SharedModel.PreviewsManagerBuilderBase-3.html#HotPreview_SharedModel_PreviewsManagerBuilderBase_3_AddOrUpdateUIComponent__0_">PreviewsManagerBuilderBase&lt;UIComponentReflection, PreviewReflection, PreviewCommandReflection&gt;.AddOrUpdateUIComponent(UIComponentReflection)</a>
    </div>
    <div>
      <a class="xref" href="HotPreview.SharedModel.PreviewsManagerBuilderBase-3.html#HotPreview_SharedModel_PreviewsManagerBuilderBase_3_AddCommand__2_">PreviewsManagerBuilderBase&lt;UIComponentReflection, PreviewReflection, PreviewCommandReflection&gt;.AddCommand(PreviewCommandReflection)</a>
    </div>
    <div>
      <a class="xref" href="HotPreview.SharedModel.PreviewsManagerBuilderBase-3.html#HotPreview_SharedModel_PreviewsManagerBuilderBase_3_AddOrUpdateCommand__2_">PreviewsManagerBuilderBase&lt;UIComponentReflection, PreviewReflection, PreviewCommandReflection&gt;.AddOrUpdateCommand(PreviewCommandReflection)</a>
    </div>
    <div>
      <a class="xref" href="HotPreview.SharedModel.PreviewsManagerBuilderBase-3.html#HotPreview_SharedModel_PreviewsManagerBuilderBase_3_AddCategory_HotPreview_SharedModel_UIComponentCategory_">PreviewsManagerBuilderBase&lt;UIComponentReflection, PreviewReflection, PreviewCommandReflection&gt;.AddCategory(UIComponentCategory)</a>
    </div>
    <div>
      <a class="xref" href="HotPreview.SharedModel.PreviewsManagerBuilderBase-3.html#HotPreview_SharedModel_PreviewsManagerBuilderBase_3_AddOrUpdateCategory_System_String_System_Collections_Generic_IReadOnlyList_System_String__">PreviewsManagerBuilderBase&lt;UIComponentReflection, PreviewReflection, PreviewCommandReflection&gt;.AddOrUpdateCategory(string, IReadOnlyList&lt;string&gt;)</a>
    </div>
    <div>
      <a class="xref" href="HotPreview.SharedModel.PreviewsManagerBuilderBase-3.html#HotPreview_SharedModel_PreviewsManagerBuilderBase_3_GetUIComponent_System_String_">PreviewsManagerBuilderBase&lt;UIComponentReflection, PreviewReflection, PreviewCommandReflection&gt;.GetUIComponent(string)</a>
    </div>
    <div>
      <a class="xref" href="HotPreview.SharedModel.PreviewsManagerBuilderBase-3.html#HotPreview_SharedModel_PreviewsManagerBuilderBase_3_GetCommand_System_String_">PreviewsManagerBuilderBase&lt;UIComponentReflection, PreviewReflection, PreviewCommandReflection&gt;.GetCommand(string)</a>
    </div>
    <div>
      <a class="xref" href="HotPreview.SharedModel.PreviewsManagerBuilderBase-3.html#HotPreview_SharedModel_PreviewsManagerBuilderBase_3_IsUIComponentBaseType_System_String_HotPreview_SharedModel_UIComponentKind__">PreviewsManagerBuilderBase&lt;UIComponentReflection, PreviewReflection, PreviewCommandReflection&gt;.IsUIComponentBaseType(string, out UIComponentKind)</a>
    </div>
    <div>
      <a class="xref" href="HotPreview.SharedModel.PreviewsManagerBuilderBase-3.html#HotPreview_SharedModel_PreviewsManagerBuilderBase_3_Validate">PreviewsManagerBuilderBase&lt;UIComponentReflection, PreviewReflection, PreviewCommandReflection&gt;.Validate()</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)">object.Equals(object)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)">object.Equals(object, object)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object.gethashcode">object.GetHashCode()</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object.gettype">object.GetType()</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone">object.MemberwiseClone()</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object.referenceequals">object.ReferenceEquals(object, object)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object.tostring">object.ToString()</a>
    </div>
  </dd></dl>






  <h2 class="section" id="constructors">Constructors
</h2>


  <a id="HotPreview_SharedModel_App_GetPreviewsViaReflection__ctor_" data-uid="HotPreview.SharedModel.App.GetPreviewsViaReflection.#ctor*"></a>

  <h3 id="HotPreview_SharedModel_App_GetPreviewsViaReflection__ctor_System_IServiceProvider_System_Reflection_Assembly_System_Collections_Generic_IEnumerable_System_String__HotPreview_SharedModel_IUIComponentExclusionFilter_" data-uid="HotPreview.SharedModel.App.GetPreviewsViaReflection.#ctor(System.IServiceProvider,System.Reflection.Assembly,System.Collections.Generic.IEnumerable{System.String},HotPreview.SharedModel.IUIComponentExclusionFilter)">
  GetPreviewsViaReflection(IServiceProvider?, Assembly?, IEnumerable&lt;string&gt;, IUIComponentExclusionFilter?)
  <a class="header-action link-secondary" title="View source" href="https://github.com/BretJohnson/hot-preview/blob/main/src/HotPreview.SharedModel/App/GetPreviewsViaReflection.cs/#L19"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"><p>Initializes a new instance of GetPreviewsViaReflection and processes assemblies to discover UI components via reflection.</p>
</div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public GetPreviewsViaReflection(IServiceProvider? serviceProvider, Assembly? mainAssembly, IEnumerable&lt;string&gt; additionalAppAssemblies, IUIComponentExclusionFilter? exclusionFilter)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>serviceProvider</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.iserviceprovider">IServiceProvider</a></dt>
    <dd><p>An optional IServiceProvider instance for dependency injection</p>
</dd>
    <dt><code>mainAssembly</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.reflection.assembly">Assembly</a></dt>
    <dd></dd>
    <dt><code>additionalAppAssemblies</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.collections.generic.ienumerable-1">IEnumerable</a>&lt;<a class="xref" href="https://learn.microsoft.com/dotnet/api/system.string">string</a>&gt;</dt>
    <dd><p>Additional app assembly names to scan</p>
</dd>
    <dt><code>exclusionFilter</code> <a class="xref" href="HotPreview.SharedModel.IUIComponentExclusionFilter.html">IUIComponentExclusionFilter</a></dt>
    <dd><p>Optional filter to exclude certain types</p>
</dd>
  </dl>












  <h2 class="section" id="methods">Methods
</h2>


  <a id="HotPreview_SharedModel_App_GetPreviewsViaReflection_ToImmutable_" data-uid="HotPreview.SharedModel.App.GetPreviewsViaReflection.ToImmutable*"></a>

  <h3 id="HotPreview_SharedModel_App_GetPreviewsViaReflection_ToImmutable" data-uid="HotPreview.SharedModel.App.GetPreviewsViaReflection.ToImmutable">
  ToImmutable()
  <a class="header-action link-secondary" title="View source" href="https://github.com/BretJohnson/hot-preview/blob/main/src/HotPreview.SharedModel/App/GetPreviewsViaReflection.cs/#L52"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"><p>Creates an immutable PreviewsManagerReflection from the builder's current state.</p>
</div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public PreviewsManagerReflection ToImmutable()</code></pre>
  </div>


  <h4 class="section">Returns</h4>
  <dl class="parameters">
    <dt><a class="xref" href="HotPreview.SharedModel.App.PreviewsManagerReflection.html">PreviewsManagerReflection</a></dt>
    <dd><p>An immutable PreviewsManagerReflection containing all the builder's data</p>
</dd>
  </dl>












</article>

        <div class="contribution d-print-none">
          <a href="https://github.com/BretJohnson/hot-preview/blob/main/src/HotPreview.SharedModel/App/GetPreviewsViaReflection.cs/#L8" class="edit-link">Edit this page</a>
        </div>


      </div>

      <div class="affix">
        <nav id="affix"></nav>
      </div>
    </main>

    <div class="container-xxl search-results" id="search-results"></div>

    <footer class="border-top text-secondary">
      <div class="container-xxl">
        <div class="flex-fill">
          <span>Made with <a href="https://dotnet.github.io/docfx">docfx</a></span>
        </div>
      </div>
    </footer>
  </body>
</html>
