<!DOCTYPE html>
<html>
  <head>
    <meta charset="utf-8">
      <title>HotPreview Attributes Reference | Hot Preview Documentation </title>
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <meta name="title" content="HotPreview Attributes Reference | Hot Preview Documentation ">
      
      
      <link rel="icon" href="../favicon.ico">
      <link rel="stylesheet" href="../public/docfx.min.css">
      <link rel="stylesheet" href="../public/main.css">
      <meta name="docfx:navrel" content="../toc.html">
      <meta name="docfx:tocrel" content="toc.html">
      
      <meta name="docfx:rel" content="../">
      
      
      <meta name="docfx:docurl" content="https://github.com/BretJohnson/hot-preview/blob/main/docs/docs/attributes.md/#L1">
      <meta name="loc:inThisArticle" content="In this article">
      <meta name="loc:searchResultsCount" content="{count} results for &quot;{query}&quot;">
      <meta name="loc:searchNoResults" content="No results for &quot;{query}&quot;">
      <meta name="loc:tocFilter" content="Filter by title">
      <meta name="loc:nextArticle" content="Next">
      <meta name="loc:prevArticle" content="Previous">
      <meta name="loc:themeLight" content="Light">
      <meta name="loc:themeDark" content="Dark">
      <meta name="loc:themeAuto" content="Auto">
      <meta name="loc:changeTheme" content="Change theme">
      <meta name="loc:copy" content="Copy">
      <meta name="loc:downloadPdf" content="Download PDF">

      <script type="module" src="./../public/docfx.min.js"></script>

      <script>
        const theme = localStorage.getItem('theme') || 'auto'
        document.documentElement.setAttribute('data-bs-theme', theme === 'auto' ? (window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light') : theme)
      </script>

  </head>

  <body class="tex2jax_ignore" data-layout="" data-yaml-mime="">
    <header class="bg-body border-bottom">
      <nav id="autocollapse" class="navbar navbar-expand-md" role="navigation">
        <div class="container-xxl flex-nowrap">
          <a class="navbar-brand" href="../index.html">
            <img id="logo" class="svg" src="../logo.svg" alt="Hot Preview">
            Hot Preview
          </a>
          <button class="btn btn-lg d-md-none border-0" type="button" data-bs-toggle="collapse" data-bs-target="#navpanel" aria-controls="navpanel" aria-expanded="false" aria-label="Toggle navigation">
            <i class="bi bi-three-dots"></i>
          </button>
          <div class="collapse navbar-collapse" id="navpanel">
            <div id="navbar">
              <form class="search" role="search" id="search">
                <i class="bi bi-search"></i>
                <input class="form-control" id="search-query" type="search" disabled placeholder="Search" autocomplete="off" aria-label="Search">
              </form>
            </div>
          </div>
        </div>
      </nav>
    </header>

    <main class="container-xxl">
      <div class="toc-offcanvas">
        <div class="offcanvas-md offcanvas-start" tabindex="-1" id="tocOffcanvas" aria-labelledby="tocOffcanvasLabel">
          <div class="offcanvas-header">
            <h5 class="offcanvas-title" id="tocOffcanvasLabel">Table of Contents</h5>
            <button type="button" class="btn-close" data-bs-dismiss="offcanvas" data-bs-target="#tocOffcanvas" aria-label="Close"></button>
          </div>
          <div class="offcanvas-body">
            <nav class="toc" id="toc"></nav>
          </div>
        </div>
      </div>

      <div class="content">
        <div class="actionbar">
          <button class="btn btn-lg border-0 d-md-none" type="button" data-bs-toggle="offcanvas" data-bs-target="#tocOffcanvas" aria-controls="tocOffcanvas" aria-expanded="false" aria-label="Show table of contents">
            <i class="bi bi-list"></i>
          </button>

          <nav id="breadcrumb"></nav>
        </div>

        <article data-uid="">
<h1 id="hotpreview-attributes-reference">HotPreview Attributes Reference</h1>

<p>HotPreview provides several attributes to control how your UI components are discovered, displayed, and organized in the DevTools interface. This reference covers all available attributes and their usage.</p>
<h2 id="core-attributes">Core Attributes</h2>
<h3 id="previewattribute">PreviewAttribute</h3>
<p>The <code>[Preview]</code> attribute is the primary way to define custom previews for your UI components.</p>
<p><strong>Target:</strong> Methods and Classes<br>
<strong>Namespace:</strong> <code>HotPreview</code></p>
<h4 id="parameters">Parameters</h4>
<table>
<thead>
<tr>
<th>Parameter</th>
<th>Type</th>
<th>Description</th>
</tr>
</thead>
<tbody>
<tr>
<td><code>displayName</code></td>
<td><code>string?</code></td>
<td>Optional display name for the preview. Use &quot;/&quot; delimiters for hierarchy (e.g., &quot;Cards/Empty State&quot;)</td>
</tr>
<tr>
<td><code>uiComponent</code></td>
<td><code>Type?</code></td>
<td>Optional UI component type this preview represents</td>
</tr>
</tbody>
</table>
<h4 id="constructors">Constructors</h4>
<pre><code class="lang-csharp">[Preview()]                                    // Basic preview
[Preview(&quot;Display Name&quot;)]                      // Named preview
[Preview(typeof(MyComponent))]                 // Explicit component type
[Preview(&quot;Display Name&quot;, typeof(MyComponent))] // Named with explicit type
</code></pre>
<h4 id="usage-examples">Usage Examples</h4>
<pre><code class="lang-csharp">#if PREVIEWS
    // Basic preview
    [Preview]
    public static CardView Preview() =&gt; new(PreviewData.GetCards(3));

    // Multiple named previews
    [Preview(&quot;Empty State&quot;)]
    public static CardView NoCards() =&gt; new(PreviewData.GetCards(0));

    [Preview(&quot;Cards/Single Card&quot;)]
    public static CardView SingleCard() =&gt; new(PreviewData.GetCards(1));

    [Preview(&quot;Cards/Multiple Cards&quot;)]
    public static CardView MultipleCards() =&gt; new(PreviewData.GetCards(6));

    // Preview in different class with explicit type
    [Preview(&quot;Custom Layout&quot;, typeof(ProductView))]
    public static ProductView CustomProductLayout() =&gt; new(PreviewData.GetProduct());
#endif
</code></pre>
<h3 id="uicomponentattribute">UIComponentAttribute</h3>
<p>The <code>[UIComponent]</code> attribute allows you to explicitly mark classes as UI components and provide custom display names.</p>
<p><strong>Target:</strong> Classes<br>
<strong>Namespace:</strong> <code>HotPreview</code></p>
<h4 id="parameters-1">Parameters</h4>
<table>
<thead>
<tr>
<th>Parameter</th>
<th>Type</th>
<th>Description</th>
</tr>
</thead>
<tbody>
<tr>
<td><code>displayName</code></td>
<td><code>string?</code></td>
<td>Optional custom display name for the UI component</td>
</tr>
</tbody>
</table>
<h4 id="usage-examples-1">Usage Examples</h4>
<pre><code class="lang-csharp">[UIComponent(&quot;Shopping Cart&quot;)]
public partial class CartView : ContentView
{
    // Component implementation
}

[UIComponent] // Uses class name as display name
public partial class ProductCard : ContentView
{
    // Component implementation
}
</code></pre>
<h3 id="previewcommandattribute">PreviewCommandAttribute</h3>
<p>The <code>[PreviewCommand]</code> attribute defines commands that can be executed from the DevTools interface.</p>
<p><strong>Target:</strong> Methods<br>
<strong>Namespace:</strong> <code>HotPreview</code></p>
<h4 id="parameters-2">Parameters</h4>
<table>
<thead>
<tr>
<th>Parameter</th>
<th>Type</th>
<th>Description</th>
</tr>
</thead>
<tbody>
<tr>
<td><code>displayName</code></td>
<td><code>string?</code></td>
<td>Optional display name for the command. Use &quot;/&quot; delimiters for hierarchy</td>
</tr>
</tbody>
</table>
<h4 id="usage-examples-2">Usage Examples</h4>
<pre><code class="lang-csharp">#if PREVIEWS
    [PreviewCommand(&quot;Reset App State&quot;)]
    public static void ResetAppState()
    {
        // Reset global state for testing
        App.Current.MainPage = new AppShell();
    }

    [PreviewCommand(&quot;Data/Load Sample Data&quot;)]
    public static void LoadSampleData()
    {
        // Load test data
        DataService.LoadSampleData();
    }
#endif
</code></pre>
<h2 id="configuration-attributes">Configuration Attributes</h2>
<h3 id="autogeneratepreviewattribute">AutoGeneratePreviewAttribute</h3>
<p>Controls whether auto-generated previews should be created for a UI component.</p>
<p><strong>Target:</strong> Classes<br>
<strong>Namespace:</strong> <code>HotPreview</code></p>
<h4 id="parameters-3">Parameters</h4>
<table>
<thead>
<tr>
<th>Parameter</th>
<th>Type</th>
<th>Description</th>
</tr>
</thead>
<tbody>
<tr>
<td><code>autoGenerate</code></td>
<td><code>bool</code></td>
<td><code>true</code> to enable auto-generation, <code>false</code> to disable</td>
</tr>
</tbody>
</table>
<h4 id="usage-examples-3">Usage Examples</h4>
<pre><code class="lang-csharp">// Disable auto-generated previews for this component
[AutoGeneratePreview(false)]
public partial class ComplexView : ContentView
{
    // This component won't get auto-generated previews
    // You must define custom [Preview] methods
}

// Explicitly enable (though this is the default)
[AutoGeneratePreview(true)]
public partial class SimpleView : ContentView
{
    // Auto-generated previews will be created
}
</code></pre>
<h2 id="assembly-level-attributes">Assembly-Level Attributes</h2>
<h3 id="uicomponentcategoryattribute">UIComponentCategoryAttribute</h3>
<p>Defines categories for organizing UI components in the DevTools interface.</p>
<p><strong>Target:</strong> Assembly<br>
<strong>Namespace:</strong> <code>HotPreview</code></p>
<h4 id="parameters-4">Parameters</h4>
<table>
<thead>
<tr>
<th>Parameter</th>
<th>Type</th>
<th>Description</th>
</tr>
</thead>
<tbody>
<tr>
<td><code>name</code></td>
<td><code>string</code></td>
<td>Category name</td>
</tr>
<tr>
<td><code>uiComponents</code></td>
<td><code>Type[]</code></td>
<td>Array of UI component types in this category</td>
</tr>
</tbody>
</table>
<h4 id="usage-examples-4">Usage Examples</h4>
<pre><code class="lang-csharp">// In AssemblyInfo.cs or any source file
using HotPreview;

[assembly: UIComponentCategory(&quot;Navigation&quot;, typeof(HeaderView), typeof(FooterView), typeof(TabBar))]
[assembly: UIComponentCategory(&quot;Cards&quot;, typeof(ProductCard), typeof(CategoryCard), typeof(InfoCard))]
[assembly: UIComponentCategory(&quot;Forms&quot;, typeof(LoginForm), typeof(RegisterForm), typeof(ContactForm))]
</code></pre>
<h3 id="controluicomponentbasetypeattribute">ControlUIComponentBaseTypeAttribute</h3>
<p>Defines platform-specific base types for control components.</p>
<p><strong>Target:</strong> Assembly<br>
<strong>Namespace:</strong> <code>HotPreview</code></p>
<h4 id="parameters-5">Parameters</h4>
<table>
<thead>
<tr>
<th>Parameter</th>
<th>Type</th>
<th>Description</th>
</tr>
</thead>
<tbody>
<tr>
<td><code>platform</code></td>
<td><code>string</code></td>
<td>Platform identifier (e.g., &quot;MAUI&quot;, &quot;WPF&quot;)</td>
</tr>
<tr>
<td><code>baseType</code></td>
<td><code>string</code></td>
<td>Fully qualified base type name</td>
</tr>
</tbody>
</table>
<h4 id="usage-examples-5">Usage Examples</h4>
<pre><code class="lang-csharp">// In AssemblyInfo.cs
[assembly: ControlUIComponentBaseType(&quot;MAUI&quot;, &quot;Microsoft.Maui.Controls.View&quot;)]
[assembly: ControlUIComponentBaseType(&quot;WPF&quot;, &quot;System.Windows.Controls.Control&quot;)]
</code></pre>
<h3 id="pageuicomponentbasetypeattribute">PageUIComponentBaseTypeAttribute</h3>
<p>Defines platform-specific base types for page components.</p>
<p><strong>Target:</strong> Assembly<br>
<strong>Namespace:</strong> <code>HotPreview</code></p>
<h4 id="parameters-6">Parameters</h4>
<table>
<thead>
<tr>
<th>Parameter</th>
<th>Type</th>
<th>Description</th>
</tr>
</thead>
<tbody>
<tr>
<td><code>platform</code></td>
<td><code>string</code></td>
<td>Platform identifier (e.g., &quot;MAUI&quot;, &quot;WPF&quot;)</td>
</tr>
<tr>
<td><code>baseType</code></td>
<td><code>string</code></td>
<td>Fully qualified base type name</td>
</tr>
</tbody>
</table>
<h4 id="usage-examples-6">Usage Examples</h4>
<pre><code class="lang-csharp">// In AssemblyInfo.cs
[assembly: PageUIComponentBaseType(&quot;MAUI&quot;, &quot;Microsoft.Maui.Controls.Page&quot;)]
[assembly: PageUIComponentBaseType(&quot;WPF&quot;, &quot;System.Windows.Window&quot;)]
</code></pre>
<h2 id="best-practices">Best Practices</h2>
<h3 id="naming-conventions">Naming Conventions</h3>
<ul>
<li>Use descriptive names that clearly indicate the preview state or variant</li>
<li>Use &quot;/&quot; delimiters to create hierarchical organization in DevTools</li>
<li>Keep names concise but meaningful</li>
</ul>
<h3 id="preview-organization">Preview Organization</h3>
<pre><code class="lang-csharp">#if PREVIEWS
    // Group related previews using hierarchy
    [Preview(&quot;States/Loading&quot;)]
    public static ProductView LoadingState() =&gt; new(isLoading: true);

    [Preview(&quot;States/Error&quot;)]
    public static ProductView ErrorState() =&gt; new(hasError: true);

    [Preview(&quot;States/Empty&quot;)]
    public static ProductView EmptyState() =&gt; new(isEmpty: true);

    // Organize by data variations
    [Preview(&quot;Data/Single Item&quot;)]
    public static CartView SingleItem() =&gt; new(PreviewData.GetCart(1));

    [Preview(&quot;Data/Multiple Items&quot;)]
    public static CartView MultipleItems() =&gt; new(PreviewData.GetCart(5));
#endif
</code></pre>
<h3 id="conditional-compilation">Conditional Compilation</h3>
<p>Always wrap preview code in conditional compilation directives:</p>
<pre><code class="lang-csharp">#if PREVIEWS
    [Preview]
    public static MyView Preview() =&gt; new(PreviewData.GetSampleData());

    [PreviewCommand(&quot;Reset State&quot;)]
    public static void ResetState() =&gt; AppState.Reset();
#endif
</code></pre>
<h3 id="assembly-configuration">Assembly Configuration</h3>
<p>Set up assembly-level attributes to customize component discovery:</p>
<pre><code class="lang-csharp">// Configure platform-specific base types
[assembly: ControlUIComponentBaseType(&quot;MAUI&quot;, &quot;Microsoft.Maui.Controls.View&quot;)]
[assembly: PageUIComponentBaseType(&quot;MAUI&quot;, &quot;Microsoft.Maui.Controls.Page&quot;)]

// Organize components into logical categories
[assembly: UIComponentCategory(&quot;Layout&quot;, typeof(HeaderView), typeof(SidebarView))]
[assembly: UIComponentCategory(&quot;Data Display&quot;, typeof(ProductCard), typeof(UserProfile))]
</code></pre>
<h2 id="troubleshooting">Troubleshooting</h2>
<ul>
<li><strong>Previews not appearing</strong>: Ensure you're building in Debug mode and the <code>PREVIEWS</code> symbol is defined</li>
<li><strong>Categories not working</strong>: Check that assembly-level attributes are properly declared</li>
<li><strong>Auto-generation issues</strong>: Use <code>[AutoGeneratePreview(false)]</code> to disable automatic preview creation for complex components</li>
</ul>

</article>

        <div class="contribution d-print-none">
          <a href="https://github.com/BretJohnson/hot-preview/blob/main/docs/docs/attributes.md/#L1" class="edit-link">Edit this page</a>
        </div>

        <div class="next-article d-print-none border-top" id="nextArticle"></div>

      </div>

      <div class="affix">
        <nav id="affix"></nav>
      </div>
    </main>

    <div class="container-xxl search-results" id="search-results"></div>

    <footer class="border-top text-secondary">
      <div class="container-xxl">
        <div class="flex-fill">
          <span>Made with <a href="https://dotnet.github.io/docfx">docfx</a></span>
        </div>
      </div>
    </footer>
  </body>
</html>
