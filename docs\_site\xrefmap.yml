### YamlMime:XRefMap
sorted: true
references:
- uid: HotPreview
  name: HotPreview
  href: api/HotPreview.html
  commentId: N:HotPreview
  fullName: HotPreview
  nameWithType: HotPreview
- uid: HotPreview.AutoGeneratePreviewAttribute
  name: AutoGeneratePreviewAttribute
  href: api/HotPreview.AutoGeneratePreviewAttribute.html
  commentId: T:HotPreview.AutoGeneratePreviewAttribute
  fullName: HotPreview.AutoGeneratePreviewAttribute
  nameWithType: AutoGeneratePreviewAttribute
- uid: HotPreview.AutoGeneratePreviewAttribute.#ctor(System.Boolean)
  name: AutoGeneratePreviewAttribute(bool)
  href: api/HotPreview.AutoGeneratePreviewAttribute.html#HotPreview_AutoGeneratePreviewAttribute__ctor_System_Boolean_
  commentId: M:HotPreview.AutoGeneratePreviewAttribute.#ctor(System.Boolean)
  name.vb: New(Boolean)
  fullName: HotPreview.AutoGeneratePreviewAttribute.AutoGeneratePreviewAttribute(bool)
  fullName.vb: HotPreview.AutoGeneratePreviewAttribute.New(Boolean)
  nameWithType: AutoGeneratePreviewAttribute.AutoGeneratePreviewAttribute(bool)
  nameWithType.vb: AutoGeneratePreviewAttribute.New(Boolean)
- uid: HotPreview.AutoGeneratePreviewAttribute.#ctor*
  name: AutoGeneratePreviewAttribute
  href: api/HotPreview.AutoGeneratePreviewAttribute.html#HotPreview_AutoGeneratePreviewAttribute__ctor_
  commentId: Overload:HotPreview.AutoGeneratePreviewAttribute.#ctor
  isSpec: "True"
  name.vb: New
  fullName: HotPreview.AutoGeneratePreviewAttribute.AutoGeneratePreviewAttribute
  fullName.vb: HotPreview.AutoGeneratePreviewAttribute.New
  nameWithType: AutoGeneratePreviewAttribute.AutoGeneratePreviewAttribute
  nameWithType.vb: AutoGeneratePreviewAttribute.New
- uid: HotPreview.AutoGeneratePreviewAttribute.AutoGenerate
  name: AutoGenerate
  href: api/HotPreview.AutoGeneratePreviewAttribute.html#HotPreview_AutoGeneratePreviewAttribute_AutoGenerate
  commentId: P:HotPreview.AutoGeneratePreviewAttribute.AutoGenerate
  fullName: HotPreview.AutoGeneratePreviewAttribute.AutoGenerate
  nameWithType: AutoGeneratePreviewAttribute.AutoGenerate
- uid: HotPreview.AutoGeneratePreviewAttribute.AutoGenerate*
  name: AutoGenerate
  href: api/HotPreview.AutoGeneratePreviewAttribute.html#HotPreview_AutoGeneratePreviewAttribute_AutoGenerate_
  commentId: Overload:HotPreview.AutoGeneratePreviewAttribute.AutoGenerate
  isSpec: "True"
  fullName: HotPreview.AutoGeneratePreviewAttribute.AutoGenerate
  nameWithType: AutoGeneratePreviewAttribute.AutoGenerate
- uid: HotPreview.ControlUIComponentBaseTypeAttribute
  name: ControlUIComponentBaseTypeAttribute
  href: api/HotPreview.ControlUIComponentBaseTypeAttribute.html
  commentId: T:HotPreview.ControlUIComponentBaseTypeAttribute
  fullName: HotPreview.ControlUIComponentBaseTypeAttribute
  nameWithType: ControlUIComponentBaseTypeAttribute
- uid: HotPreview.ControlUIComponentBaseTypeAttribute.#ctor(System.String,System.String)
  name: ControlUIComponentBaseTypeAttribute(string, string)
  href: api/HotPreview.ControlUIComponentBaseTypeAttribute.html#HotPreview_ControlUIComponentBaseTypeAttribute__ctor_System_String_System_String_
  commentId: M:HotPreview.ControlUIComponentBaseTypeAttribute.#ctor(System.String,System.String)
  name.vb: New(String, String)
  fullName: HotPreview.ControlUIComponentBaseTypeAttribute.ControlUIComponentBaseTypeAttribute(string, string)
  fullName.vb: HotPreview.ControlUIComponentBaseTypeAttribute.New(String, String)
  nameWithType: ControlUIComponentBaseTypeAttribute.ControlUIComponentBaseTypeAttribute(string, string)
  nameWithType.vb: ControlUIComponentBaseTypeAttribute.New(String, String)
- uid: HotPreview.ControlUIComponentBaseTypeAttribute.#ctor*
  name: ControlUIComponentBaseTypeAttribute
  href: api/HotPreview.ControlUIComponentBaseTypeAttribute.html#HotPreview_ControlUIComponentBaseTypeAttribute__ctor_
  commentId: Overload:HotPreview.ControlUIComponentBaseTypeAttribute.#ctor
  isSpec: "True"
  name.vb: New
  fullName: HotPreview.ControlUIComponentBaseTypeAttribute.ControlUIComponentBaseTypeAttribute
  fullName.vb: HotPreview.ControlUIComponentBaseTypeAttribute.New
  nameWithType: ControlUIComponentBaseTypeAttribute.ControlUIComponentBaseTypeAttribute
  nameWithType.vb: ControlUIComponentBaseTypeAttribute.New
- uid: HotPreview.ControlUIComponentBaseTypeAttribute.BaseType
  name: BaseType
  href: api/HotPreview.ControlUIComponentBaseTypeAttribute.html#HotPreview_ControlUIComponentBaseTypeAttribute_BaseType
  commentId: P:HotPreview.ControlUIComponentBaseTypeAttribute.BaseType
  fullName: HotPreview.ControlUIComponentBaseTypeAttribute.BaseType
  nameWithType: ControlUIComponentBaseTypeAttribute.BaseType
- uid: HotPreview.ControlUIComponentBaseTypeAttribute.BaseType*
  name: BaseType
  href: api/HotPreview.ControlUIComponentBaseTypeAttribute.html#HotPreview_ControlUIComponentBaseTypeAttribute_BaseType_
  commentId: Overload:HotPreview.ControlUIComponentBaseTypeAttribute.BaseType
  isSpec: "True"
  fullName: HotPreview.ControlUIComponentBaseTypeAttribute.BaseType
  nameWithType: ControlUIComponentBaseTypeAttribute.BaseType
- uid: HotPreview.ControlUIComponentBaseTypeAttribute.Platform
  name: Platform
  href: api/HotPreview.ControlUIComponentBaseTypeAttribute.html#HotPreview_ControlUIComponentBaseTypeAttribute_Platform
  commentId: P:HotPreview.ControlUIComponentBaseTypeAttribute.Platform
  fullName: HotPreview.ControlUIComponentBaseTypeAttribute.Platform
  nameWithType: ControlUIComponentBaseTypeAttribute.Platform
- uid: HotPreview.ControlUIComponentBaseTypeAttribute.Platform*
  name: Platform
  href: api/HotPreview.ControlUIComponentBaseTypeAttribute.html#HotPreview_ControlUIComponentBaseTypeAttribute_Platform_
  commentId: Overload:HotPreview.ControlUIComponentBaseTypeAttribute.Platform
  isSpec: "True"
  fullName: HotPreview.ControlUIComponentBaseTypeAttribute.Platform
  nameWithType: ControlUIComponentBaseTypeAttribute.Platform
- uid: HotPreview.NameUtilities
  name: NameUtilities
  href: api/HotPreview.NameUtilities.html
  commentId: T:HotPreview.NameUtilities
  fullName: HotPreview.NameUtilities
  nameWithType: NameUtilities
- uid: HotPreview.NameUtilities.GetUnqualifiedName(System.String)
  name: GetUnqualifiedName(string)
  href: api/HotPreview.NameUtilities.html#HotPreview_NameUtilities_GetUnqualifiedName_System_String_
  commentId: M:HotPreview.NameUtilities.GetUnqualifiedName(System.String)
  name.vb: GetUnqualifiedName(String)
  fullName: HotPreview.NameUtilities.GetUnqualifiedName(string)
  fullName.vb: HotPreview.NameUtilities.GetUnqualifiedName(String)
  nameWithType: NameUtilities.GetUnqualifiedName(string)
  nameWithType.vb: NameUtilities.GetUnqualifiedName(String)
- uid: HotPreview.NameUtilities.GetUnqualifiedName*
  name: GetUnqualifiedName
  href: api/HotPreview.NameUtilities.html#HotPreview_NameUtilities_GetUnqualifiedName_
  commentId: Overload:HotPreview.NameUtilities.GetUnqualifiedName
  isSpec: "True"
  fullName: HotPreview.NameUtilities.GetUnqualifiedName
  nameWithType: NameUtilities.GetUnqualifiedName
- uid: HotPreview.NameUtilities.NormalizeTypeFullName(System.Type)
  name: NormalizeTypeFullName(Type)
  href: api/HotPreview.NameUtilities.html#HotPreview_NameUtilities_NormalizeTypeFullName_System_Type_
  commentId: M:HotPreview.NameUtilities.NormalizeTypeFullName(System.Type)
  fullName: HotPreview.NameUtilities.NormalizeTypeFullName(System.Type)
  nameWithType: NameUtilities.NormalizeTypeFullName(Type)
- uid: HotPreview.NameUtilities.NormalizeTypeFullName*
  name: NormalizeTypeFullName
  href: api/HotPreview.NameUtilities.html#HotPreview_NameUtilities_NormalizeTypeFullName_
  commentId: Overload:HotPreview.NameUtilities.NormalizeTypeFullName
  isSpec: "True"
  fullName: HotPreview.NameUtilities.NormalizeTypeFullName
  nameWithType: NameUtilities.NormalizeTypeFullName
- uid: HotPreview.PageUIComponentBaseTypeAttribute
  name: PageUIComponentBaseTypeAttribute
  href: api/HotPreview.PageUIComponentBaseTypeAttribute.html
  commentId: T:HotPreview.PageUIComponentBaseTypeAttribute
  fullName: HotPreview.PageUIComponentBaseTypeAttribute
  nameWithType: PageUIComponentBaseTypeAttribute
- uid: HotPreview.PageUIComponentBaseTypeAttribute.#ctor(System.String,System.String)
  name: PageUIComponentBaseTypeAttribute(string, string)
  href: api/HotPreview.PageUIComponentBaseTypeAttribute.html#HotPreview_PageUIComponentBaseTypeAttribute__ctor_System_String_System_String_
  commentId: M:HotPreview.PageUIComponentBaseTypeAttribute.#ctor(System.String,System.String)
  name.vb: New(String, String)
  fullName: HotPreview.PageUIComponentBaseTypeAttribute.PageUIComponentBaseTypeAttribute(string, string)
  fullName.vb: HotPreview.PageUIComponentBaseTypeAttribute.New(String, String)
  nameWithType: PageUIComponentBaseTypeAttribute.PageUIComponentBaseTypeAttribute(string, string)
  nameWithType.vb: PageUIComponentBaseTypeAttribute.New(String, String)
- uid: HotPreview.PageUIComponentBaseTypeAttribute.#ctor*
  name: PageUIComponentBaseTypeAttribute
  href: api/HotPreview.PageUIComponentBaseTypeAttribute.html#HotPreview_PageUIComponentBaseTypeAttribute__ctor_
  commentId: Overload:HotPreview.PageUIComponentBaseTypeAttribute.#ctor
  isSpec: "True"
  name.vb: New
  fullName: HotPreview.PageUIComponentBaseTypeAttribute.PageUIComponentBaseTypeAttribute
  fullName.vb: HotPreview.PageUIComponentBaseTypeAttribute.New
  nameWithType: PageUIComponentBaseTypeAttribute.PageUIComponentBaseTypeAttribute
  nameWithType.vb: PageUIComponentBaseTypeAttribute.New
- uid: HotPreview.PageUIComponentBaseTypeAttribute.BaseType
  name: BaseType
  href: api/HotPreview.PageUIComponentBaseTypeAttribute.html#HotPreview_PageUIComponentBaseTypeAttribute_BaseType
  commentId: P:HotPreview.PageUIComponentBaseTypeAttribute.BaseType
  fullName: HotPreview.PageUIComponentBaseTypeAttribute.BaseType
  nameWithType: PageUIComponentBaseTypeAttribute.BaseType
- uid: HotPreview.PageUIComponentBaseTypeAttribute.BaseType*
  name: BaseType
  href: api/HotPreview.PageUIComponentBaseTypeAttribute.html#HotPreview_PageUIComponentBaseTypeAttribute_BaseType_
  commentId: Overload:HotPreview.PageUIComponentBaseTypeAttribute.BaseType
  isSpec: "True"
  fullName: HotPreview.PageUIComponentBaseTypeAttribute.BaseType
  nameWithType: PageUIComponentBaseTypeAttribute.BaseType
- uid: HotPreview.PageUIComponentBaseTypeAttribute.Platform
  name: Platform
  href: api/HotPreview.PageUIComponentBaseTypeAttribute.html#HotPreview_PageUIComponentBaseTypeAttribute_Platform
  commentId: P:HotPreview.PageUIComponentBaseTypeAttribute.Platform
  fullName: HotPreview.PageUIComponentBaseTypeAttribute.Platform
  nameWithType: PageUIComponentBaseTypeAttribute.Platform
- uid: HotPreview.PageUIComponentBaseTypeAttribute.Platform*
  name: Platform
  href: api/HotPreview.PageUIComponentBaseTypeAttribute.html#HotPreview_PageUIComponentBaseTypeAttribute_Platform_
  commentId: Overload:HotPreview.PageUIComponentBaseTypeAttribute.Platform
  isSpec: "True"
  fullName: HotPreview.PageUIComponentBaseTypeAttribute.Platform
  nameWithType: PageUIComponentBaseTypeAttribute.Platform
- uid: HotPreview.PreviewAttribute
  name: PreviewAttribute
  href: api/HotPreview.PreviewAttribute.html
  commentId: T:HotPreview.PreviewAttribute
  fullName: HotPreview.PreviewAttribute
  nameWithType: PreviewAttribute
- uid: HotPreview.PreviewAttribute.#ctor(System.String)
  name: PreviewAttribute(string?)
  href: api/HotPreview.PreviewAttribute.html#HotPreview_PreviewAttribute__ctor_System_String_
  commentId: M:HotPreview.PreviewAttribute.#ctor(System.String)
  name.vb: New(String)
  fullName: HotPreview.PreviewAttribute.PreviewAttribute(string?)
  fullName.vb: HotPreview.PreviewAttribute.New(String)
  nameWithType: PreviewAttribute.PreviewAttribute(string?)
  nameWithType.vb: PreviewAttribute.New(String)
- uid: HotPreview.PreviewAttribute.#ctor(System.String,System.Type)
  name: PreviewAttribute(string?, Type?)
  href: api/HotPreview.PreviewAttribute.html#HotPreview_PreviewAttribute__ctor_System_String_System_Type_
  commentId: M:HotPreview.PreviewAttribute.#ctor(System.String,System.Type)
  name.vb: New(String, Type)
  fullName: HotPreview.PreviewAttribute.PreviewAttribute(string?, System.Type?)
  fullName.vb: HotPreview.PreviewAttribute.New(String, System.Type)
  nameWithType: PreviewAttribute.PreviewAttribute(string?, Type?)
  nameWithType.vb: PreviewAttribute.New(String, Type)
- uid: HotPreview.PreviewAttribute.#ctor*
  name: PreviewAttribute
  href: api/HotPreview.PreviewAttribute.html#HotPreview_PreviewAttribute__ctor_
  commentId: Overload:HotPreview.PreviewAttribute.#ctor
  isSpec: "True"
  name.vb: New
  fullName: HotPreview.PreviewAttribute.PreviewAttribute
  fullName.vb: HotPreview.PreviewAttribute.New
  nameWithType: PreviewAttribute.PreviewAttribute
  nameWithType.vb: PreviewAttribute.New
- uid: HotPreview.PreviewAttribute.DisplayName
  name: DisplayName
  href: api/HotPreview.PreviewAttribute.html#HotPreview_PreviewAttribute_DisplayName
  commentId: P:HotPreview.PreviewAttribute.DisplayName
  fullName: HotPreview.PreviewAttribute.DisplayName
  nameWithType: PreviewAttribute.DisplayName
- uid: HotPreview.PreviewAttribute.DisplayName*
  name: DisplayName
  href: api/HotPreview.PreviewAttribute.html#HotPreview_PreviewAttribute_DisplayName_
  commentId: Overload:HotPreview.PreviewAttribute.DisplayName
  isSpec: "True"
  fullName: HotPreview.PreviewAttribute.DisplayName
  nameWithType: PreviewAttribute.DisplayName
- uid: HotPreview.PreviewAttribute.UIComponentType
  name: UIComponentType
  href: api/HotPreview.PreviewAttribute.html#HotPreview_PreviewAttribute_UIComponentType
  commentId: P:HotPreview.PreviewAttribute.UIComponentType
  fullName: HotPreview.PreviewAttribute.UIComponentType
  nameWithType: PreviewAttribute.UIComponentType
- uid: HotPreview.PreviewAttribute.UIComponentType*
  name: UIComponentType
  href: api/HotPreview.PreviewAttribute.html#HotPreview_PreviewAttribute_UIComponentType_
  commentId: Overload:HotPreview.PreviewAttribute.UIComponentType
  isSpec: "True"
  fullName: HotPreview.PreviewAttribute.UIComponentType
  nameWithType: PreviewAttribute.UIComponentType
- uid: HotPreview.PreviewAttribute`1
  name: PreviewAttribute<TUIComponent>
  href: api/HotPreview.PreviewAttribute-1.html
  commentId: T:HotPreview.PreviewAttribute`1
  name.vb: PreviewAttribute(Of TUIComponent)
  fullName: HotPreview.PreviewAttribute<TUIComponent>
  fullName.vb: HotPreview.PreviewAttribute(Of TUIComponent)
  nameWithType: PreviewAttribute<TUIComponent>
  nameWithType.vb: PreviewAttribute(Of TUIComponent)
- uid: HotPreview.PreviewAttribute`1.#ctor(System.String)
  name: PreviewAttribute(string?)
  href: api/HotPreview.PreviewAttribute-1.html#HotPreview_PreviewAttribute_1__ctor_System_String_
  commentId: M:HotPreview.PreviewAttribute`1.#ctor(System.String)
  name.vb: New(String)
  fullName: HotPreview.PreviewAttribute<TUIComponent>.PreviewAttribute(string?)
  fullName.vb: HotPreview.PreviewAttribute(Of TUIComponent).New(String)
  nameWithType: PreviewAttribute<TUIComponent>.PreviewAttribute(string?)
  nameWithType.vb: PreviewAttribute(Of TUIComponent).New(String)
- uid: HotPreview.PreviewAttribute`1.#ctor*
  name: PreviewAttribute
  href: api/HotPreview.PreviewAttribute-1.html#HotPreview_PreviewAttribute_1__ctor_
  commentId: Overload:HotPreview.PreviewAttribute`1.#ctor
  isSpec: "True"
  name.vb: New
  fullName: HotPreview.PreviewAttribute<TUIComponent>.PreviewAttribute
  fullName.vb: HotPreview.PreviewAttribute(Of TUIComponent).New
  nameWithType: PreviewAttribute<TUIComponent>.PreviewAttribute
  nameWithType.vb: PreviewAttribute(Of TUIComponent).New
- uid: HotPreview.PreviewCommandAttribute
  name: PreviewCommandAttribute
  href: api/HotPreview.PreviewCommandAttribute.html
  commentId: T:HotPreview.PreviewCommandAttribute
  fullName: HotPreview.PreviewCommandAttribute
  nameWithType: PreviewCommandAttribute
- uid: HotPreview.PreviewCommandAttribute.#ctor(System.String)
  name: PreviewCommandAttribute(string?)
  href: api/HotPreview.PreviewCommandAttribute.html#HotPreview_PreviewCommandAttribute__ctor_System_String_
  commentId: M:HotPreview.PreviewCommandAttribute.#ctor(System.String)
  name.vb: New(String)
  fullName: HotPreview.PreviewCommandAttribute.PreviewCommandAttribute(string?)
  fullName.vb: HotPreview.PreviewCommandAttribute.New(String)
  nameWithType: PreviewCommandAttribute.PreviewCommandAttribute(string?)
  nameWithType.vb: PreviewCommandAttribute.New(String)
- uid: HotPreview.PreviewCommandAttribute.#ctor*
  name: PreviewCommandAttribute
  href: api/HotPreview.PreviewCommandAttribute.html#HotPreview_PreviewCommandAttribute__ctor_
  commentId: Overload:HotPreview.PreviewCommandAttribute.#ctor
  isSpec: "True"
  name.vb: New
  fullName: HotPreview.PreviewCommandAttribute.PreviewCommandAttribute
  fullName.vb: HotPreview.PreviewCommandAttribute.New
  nameWithType: PreviewCommandAttribute.PreviewCommandAttribute
  nameWithType.vb: PreviewCommandAttribute.New
- uid: HotPreview.PreviewCommandAttribute.DisplayName
  name: DisplayName
  href: api/HotPreview.PreviewCommandAttribute.html#HotPreview_PreviewCommandAttribute_DisplayName
  commentId: P:HotPreview.PreviewCommandAttribute.DisplayName
  fullName: HotPreview.PreviewCommandAttribute.DisplayName
  nameWithType: PreviewCommandAttribute.DisplayName
- uid: HotPreview.PreviewCommandAttribute.DisplayName*
  name: DisplayName
  href: api/HotPreview.PreviewCommandAttribute.html#HotPreview_PreviewCommandAttribute_DisplayName_
  commentId: Overload:HotPreview.PreviewCommandAttribute.DisplayName
  isSpec: "True"
  fullName: HotPreview.PreviewCommandAttribute.DisplayName
  nameWithType: PreviewCommandAttribute.DisplayName
- uid: HotPreview.PreviewMode
  name: PreviewMode
  href: api/HotPreview.PreviewMode.html
  commentId: T:HotPreview.PreviewMode
  fullName: HotPreview.PreviewMode
  nameWithType: PreviewMode
- uid: HotPreview.PreviewMode.Gallery
  name: Gallery
  href: api/HotPreview.PreviewMode.html#HotPreview_PreviewMode_Gallery
  commentId: F:HotPreview.PreviewMode.Gallery
  fullName: HotPreview.PreviewMode.Gallery
  nameWithType: PreviewMode.Gallery
- uid: HotPreview.PreviewMode.None
  name: None
  href: api/HotPreview.PreviewMode.html#HotPreview_PreviewMode_None
  commentId: F:HotPreview.PreviewMode.None
  fullName: HotPreview.PreviewMode.None
  nameWithType: PreviewMode.None
- uid: HotPreview.PreviewMode.RemoteControl
  name: RemoteControl
  href: api/HotPreview.PreviewMode.html#HotPreview_PreviewMode_RemoteControl
  commentId: F:HotPreview.PreviewMode.RemoteControl
  fullName: HotPreview.PreviewMode.RemoteControl
  nameWithType: PreviewMode.RemoteControl
- uid: HotPreview.PreviewssAttribute
  name: PreviewssAttribute
  href: api/HotPreview.PreviewssAttribute.html
  commentId: T:HotPreview.PreviewssAttribute
  fullName: HotPreview.PreviewssAttribute
  nameWithType: PreviewssAttribute
- uid: HotPreview.PreviewssAttribute.#ctor
  name: PreviewssAttribute()
  href: api/HotPreview.PreviewssAttribute.html#HotPreview_PreviewssAttribute__ctor
  commentId: M:HotPreview.PreviewssAttribute.#ctor
  name.vb: New()
  fullName: HotPreview.PreviewssAttribute.PreviewssAttribute()
  fullName.vb: HotPreview.PreviewssAttribute.New()
  nameWithType: PreviewssAttribute.PreviewssAttribute()
  nameWithType.vb: PreviewssAttribute.New()
- uid: HotPreview.PreviewssAttribute.#ctor(System.String)
  name: PreviewssAttribute(string)
  href: api/HotPreview.PreviewssAttribute.html#HotPreview_PreviewssAttribute__ctor_System_String_
  commentId: M:HotPreview.PreviewssAttribute.#ctor(System.String)
  name.vb: New(String)
  fullName: HotPreview.PreviewssAttribute.PreviewssAttribute(string)
  fullName.vb: HotPreview.PreviewssAttribute.New(String)
  nameWithType: PreviewssAttribute.PreviewssAttribute(string)
  nameWithType.vb: PreviewssAttribute.New(String)
- uid: HotPreview.PreviewssAttribute.#ctor*
  name: PreviewssAttribute
  href: api/HotPreview.PreviewssAttribute.html#HotPreview_PreviewssAttribute__ctor_
  commentId: Overload:HotPreview.PreviewssAttribute.#ctor
  isSpec: "True"
  name.vb: New
  fullName: HotPreview.PreviewssAttribute.PreviewssAttribute
  fullName.vb: HotPreview.PreviewssAttribute.New
  nameWithType: PreviewssAttribute.PreviewssAttribute
  nameWithType.vb: PreviewssAttribute.New
- uid: HotPreview.PreviewssAttribute.Title
  name: Title
  href: api/HotPreview.PreviewssAttribute.html#HotPreview_PreviewssAttribute_Title
  commentId: P:HotPreview.PreviewssAttribute.Title
  fullName: HotPreview.PreviewssAttribute.Title
  nameWithType: PreviewssAttribute.Title
- uid: HotPreview.PreviewssAttribute.Title*
  name: Title
  href: api/HotPreview.PreviewssAttribute.html#HotPreview_PreviewssAttribute_Title_
  commentId: Overload:HotPreview.PreviewssAttribute.Title
  isSpec: "True"
  fullName: HotPreview.PreviewssAttribute.Title
  nameWithType: PreviewssAttribute.Title
- uid: HotPreview.RoutePreview
  name: RoutePreview
  href: api/HotPreview.RoutePreview.html
  commentId: T:HotPreview.RoutePreview
  fullName: HotPreview.RoutePreview
  nameWithType: RoutePreview
- uid: HotPreview.RoutePreview.#ctor(System.String)
  name: RoutePreview(string)
  href: api/HotPreview.RoutePreview.html#HotPreview_RoutePreview__ctor_System_String_
  commentId: M:HotPreview.RoutePreview.#ctor(System.String)
  name.vb: New(String)
  fullName: HotPreview.RoutePreview.RoutePreview(string)
  fullName.vb: HotPreview.RoutePreview.New(String)
  nameWithType: RoutePreview.RoutePreview(string)
  nameWithType.vb: RoutePreview.New(String)
- uid: HotPreview.RoutePreview.#ctor*
  name: RoutePreview
  href: api/HotPreview.RoutePreview.html#HotPreview_RoutePreview__ctor_
  commentId: Overload:HotPreview.RoutePreview.#ctor
  isSpec: "True"
  name.vb: New
  fullName: HotPreview.RoutePreview.RoutePreview
  fullName.vb: HotPreview.RoutePreview.New
  nameWithType: RoutePreview.RoutePreview
  nameWithType.vb: RoutePreview.New
- uid: HotPreview.RoutePreview.Route
  name: Route
  href: api/HotPreview.RoutePreview.html#HotPreview_RoutePreview_Route
  commentId: P:HotPreview.RoutePreview.Route
  fullName: HotPreview.RoutePreview.Route
  nameWithType: RoutePreview.Route
- uid: HotPreview.RoutePreview.Route*
  name: Route
  href: api/HotPreview.RoutePreview.html#HotPreview_RoutePreview_Route_
  commentId: Overload:HotPreview.RoutePreview.Route
  isSpec: "True"
  fullName: HotPreview.RoutePreview.Route
  nameWithType: RoutePreview.Route
- uid: HotPreview.RoutePreview`1
  name: RoutePreview<T>
  href: api/HotPreview.RoutePreview-1.html
  commentId: T:HotPreview.RoutePreview`1
  name.vb: RoutePreview(Of T)
  fullName: HotPreview.RoutePreview<T>
  fullName.vb: HotPreview.RoutePreview(Of T)
  nameWithType: RoutePreview<T>
  nameWithType.vb: RoutePreview(Of T)
- uid: HotPreview.RoutePreview`1.#ctor(System.String)
  name: RoutePreview(string)
  href: api/HotPreview.RoutePreview-1.html#HotPreview_RoutePreview_1__ctor_System_String_
  commentId: M:HotPreview.RoutePreview`1.#ctor(System.String)
  name.vb: New(String)
  fullName: HotPreview.RoutePreview<T>.RoutePreview(string)
  fullName.vb: HotPreview.RoutePreview(Of T).New(String)
  nameWithType: RoutePreview<T>.RoutePreview(string)
  nameWithType.vb: RoutePreview(Of T).New(String)
- uid: HotPreview.RoutePreview`1.#ctor*
  name: RoutePreview
  href: api/HotPreview.RoutePreview-1.html#HotPreview_RoutePreview_1__ctor_
  commentId: Overload:HotPreview.RoutePreview`1.#ctor
  isSpec: "True"
  name.vb: New
  fullName: HotPreview.RoutePreview<T>.RoutePreview
  fullName.vb: HotPreview.RoutePreview(Of T).New
  nameWithType: RoutePreview<T>.RoutePreview
  nameWithType.vb: RoutePreview(Of T).New
- uid: HotPreview.SharedModel
  name: HotPreview.SharedModel
  href: api/HotPreview.SharedModel.html
  commentId: N:HotPreview.SharedModel
  fullName: HotPreview.SharedModel
  nameWithType: HotPreview.SharedModel
- uid: HotPreview.SharedModel.App
  name: HotPreview.SharedModel.App
  href: api/HotPreview.SharedModel.App.html
  commentId: N:HotPreview.SharedModel.App
  fullName: HotPreview.SharedModel.App
  nameWithType: HotPreview.SharedModel.App
- uid: HotPreview.SharedModel.App.GetPreviewsViaReflection
  name: GetPreviewsViaReflection
  href: api/HotPreview.SharedModel.App.GetPreviewsViaReflection.html
  commentId: T:HotPreview.SharedModel.App.GetPreviewsViaReflection
  fullName: HotPreview.SharedModel.App.GetPreviewsViaReflection
  nameWithType: GetPreviewsViaReflection
- uid: HotPreview.SharedModel.App.GetPreviewsViaReflection.#ctor(System.IServiceProvider,System.Reflection.Assembly,System.Collections.Generic.IEnumerable{System.String},HotPreview.SharedModel.IUIComponentExclusionFilter)
  name: GetPreviewsViaReflection(IServiceProvider?, Assembly?, IEnumerable<string>, IUIComponentExclusionFilter?)
  href: api/HotPreview.SharedModel.App.GetPreviewsViaReflection.html#HotPreview_SharedModel_App_GetPreviewsViaReflection__ctor_System_IServiceProvider_System_Reflection_Assembly_System_Collections_Generic_IEnumerable_System_String__HotPreview_SharedModel_IUIComponentExclusionFilter_
  commentId: M:HotPreview.SharedModel.App.GetPreviewsViaReflection.#ctor(System.IServiceProvider,System.Reflection.Assembly,System.Collections.Generic.IEnumerable{System.String},HotPreview.SharedModel.IUIComponentExclusionFilter)
  name.vb: New(IServiceProvider, Assembly, IEnumerable(Of String), IUIComponentExclusionFilter)
  fullName: HotPreview.SharedModel.App.GetPreviewsViaReflection.GetPreviewsViaReflection(System.IServiceProvider?, System.Reflection.Assembly?, System.Collections.Generic.IEnumerable<string>, HotPreview.SharedModel.IUIComponentExclusionFilter?)
  fullName.vb: HotPreview.SharedModel.App.GetPreviewsViaReflection.New(System.IServiceProvider, System.Reflection.Assembly, System.Collections.Generic.IEnumerable(Of String), HotPreview.SharedModel.IUIComponentExclusionFilter)
  nameWithType: GetPreviewsViaReflection.GetPreviewsViaReflection(IServiceProvider?, Assembly?, IEnumerable<string>, IUIComponentExclusionFilter?)
  nameWithType.vb: GetPreviewsViaReflection.New(IServiceProvider, Assembly, IEnumerable(Of String), IUIComponentExclusionFilter)
- uid: HotPreview.SharedModel.App.GetPreviewsViaReflection.#ctor*
  name: GetPreviewsViaReflection
  href: api/HotPreview.SharedModel.App.GetPreviewsViaReflection.html#HotPreview_SharedModel_App_GetPreviewsViaReflection__ctor_
  commentId: Overload:HotPreview.SharedModel.App.GetPreviewsViaReflection.#ctor
  isSpec: "True"
  name.vb: New
  fullName: HotPreview.SharedModel.App.GetPreviewsViaReflection.GetPreviewsViaReflection
  fullName.vb: HotPreview.SharedModel.App.GetPreviewsViaReflection.New
  nameWithType: GetPreviewsViaReflection.GetPreviewsViaReflection
  nameWithType.vb: GetPreviewsViaReflection.New
- uid: HotPreview.SharedModel.App.GetPreviewsViaReflection.ToImmutable
  name: ToImmutable()
  href: api/HotPreview.SharedModel.App.GetPreviewsViaReflection.html#HotPreview_SharedModel_App_GetPreviewsViaReflection_ToImmutable
  commentId: M:HotPreview.SharedModel.App.GetPreviewsViaReflection.ToImmutable
  fullName: HotPreview.SharedModel.App.GetPreviewsViaReflection.ToImmutable()
  nameWithType: GetPreviewsViaReflection.ToImmutable()
- uid: HotPreview.SharedModel.App.GetPreviewsViaReflection.ToImmutable*
  name: ToImmutable
  href: api/HotPreview.SharedModel.App.GetPreviewsViaReflection.html#HotPreview_SharedModel_App_GetPreviewsViaReflection_ToImmutable_
  commentId: Overload:HotPreview.SharedModel.App.GetPreviewsViaReflection.ToImmutable
  isSpec: "True"
  fullName: HotPreview.SharedModel.App.GetPreviewsViaReflection.ToImmutable
  nameWithType: GetPreviewsViaReflection.ToImmutable
- uid: HotPreview.SharedModel.App.IPreviewNavigator
  name: IPreviewNavigator
  href: api/HotPreview.SharedModel.App.IPreviewNavigator.html
  commentId: T:HotPreview.SharedModel.App.IPreviewNavigator
  fullName: HotPreview.SharedModel.App.IPreviewNavigator
  nameWithType: IPreviewNavigator
- uid: HotPreview.SharedModel.App.IPreviewNavigator.GetPreviewSnapshotAsync(HotPreview.SharedModel.App.UIComponentReflection,HotPreview.SharedModel.App.PreviewReflection)
  name: GetPreviewSnapshotAsync(UIComponentReflection, PreviewReflection)
  href: api/HotPreview.SharedModel.App.IPreviewNavigator.html#HotPreview_SharedModel_App_IPreviewNavigator_GetPreviewSnapshotAsync_HotPreview_SharedModel_App_UIComponentReflection_HotPreview_SharedModel_App_PreviewReflection_
  commentId: M:HotPreview.SharedModel.App.IPreviewNavigator.GetPreviewSnapshotAsync(HotPreview.SharedModel.App.UIComponentReflection,HotPreview.SharedModel.App.PreviewReflection)
  fullName: HotPreview.SharedModel.App.IPreviewNavigator.GetPreviewSnapshotAsync(HotPreview.SharedModel.App.UIComponentReflection, HotPreview.SharedModel.App.PreviewReflection)
  nameWithType: IPreviewNavigator.GetPreviewSnapshotAsync(UIComponentReflection, PreviewReflection)
- uid: HotPreview.SharedModel.App.IPreviewNavigator.GetPreviewSnapshotAsync*
  name: GetPreviewSnapshotAsync
  href: api/HotPreview.SharedModel.App.IPreviewNavigator.html#HotPreview_SharedModel_App_IPreviewNavigator_GetPreviewSnapshotAsync_
  commentId: Overload:HotPreview.SharedModel.App.IPreviewNavigator.GetPreviewSnapshotAsync
  isSpec: "True"
  fullName: HotPreview.SharedModel.App.IPreviewNavigator.GetPreviewSnapshotAsync
  nameWithType: IPreviewNavigator.GetPreviewSnapshotAsync
- uid: HotPreview.SharedModel.App.IPreviewNavigator.NavigateToPreviewAsync(HotPreview.SharedModel.App.UIComponentReflection,HotPreview.SharedModel.App.PreviewReflection)
  name: NavigateToPreviewAsync(UIComponentReflection, PreviewReflection)
  href: api/HotPreview.SharedModel.App.IPreviewNavigator.html#HotPreview_SharedModel_App_IPreviewNavigator_NavigateToPreviewAsync_HotPreview_SharedModel_App_UIComponentReflection_HotPreview_SharedModel_App_PreviewReflection_
  commentId: M:HotPreview.SharedModel.App.IPreviewNavigator.NavigateToPreviewAsync(HotPreview.SharedModel.App.UIComponentReflection,HotPreview.SharedModel.App.PreviewReflection)
  fullName: HotPreview.SharedModel.App.IPreviewNavigator.NavigateToPreviewAsync(HotPreview.SharedModel.App.UIComponentReflection, HotPreview.SharedModel.App.PreviewReflection)
  nameWithType: IPreviewNavigator.NavigateToPreviewAsync(UIComponentReflection, PreviewReflection)
- uid: HotPreview.SharedModel.App.IPreviewNavigator.NavigateToPreviewAsync*
  name: NavigateToPreviewAsync
  href: api/HotPreview.SharedModel.App.IPreviewNavigator.html#HotPreview_SharedModel_App_IPreviewNavigator_NavigateToPreviewAsync_
  commentId: Overload:HotPreview.SharedModel.App.IPreviewNavigator.NavigateToPreviewAsync
  isSpec: "True"
  fullName: HotPreview.SharedModel.App.IPreviewNavigator.NavigateToPreviewAsync
  nameWithType: IPreviewNavigator.NavigateToPreviewAsync
- uid: HotPreview.SharedModel.App.PreviewAppService
  name: PreviewAppService
  href: api/HotPreview.SharedModel.App.PreviewAppService.html
  commentId: T:HotPreview.SharedModel.App.PreviewAppService
  fullName: HotPreview.SharedModel.App.PreviewAppService
  nameWithType: PreviewAppService
- uid: HotPreview.SharedModel.App.PreviewAppService.#ctor(HotPreview.SharedModel.App.PreviewApplication)
  name: PreviewAppService(PreviewApplication)
  href: api/HotPreview.SharedModel.App.PreviewAppService.html#HotPreview_SharedModel_App_PreviewAppService__ctor_HotPreview_SharedModel_App_PreviewApplication_
  commentId: M:HotPreview.SharedModel.App.PreviewAppService.#ctor(HotPreview.SharedModel.App.PreviewApplication)
  name.vb: New(PreviewApplication)
  fullName: HotPreview.SharedModel.App.PreviewAppService.PreviewAppService(HotPreview.SharedModel.App.PreviewApplication)
  fullName.vb: HotPreview.SharedModel.App.PreviewAppService.New(HotPreview.SharedModel.App.PreviewApplication)
  nameWithType: PreviewAppService.PreviewAppService(PreviewApplication)
  nameWithType.vb: PreviewAppService.New(PreviewApplication)
- uid: HotPreview.SharedModel.App.PreviewAppService.#ctor*
  name: PreviewAppService
  href: api/HotPreview.SharedModel.App.PreviewAppService.html#HotPreview_SharedModel_App_PreviewAppService__ctor_
  commentId: Overload:HotPreview.SharedModel.App.PreviewAppService.#ctor
  isSpec: "True"
  name.vb: New
  fullName: HotPreview.SharedModel.App.PreviewAppService.PreviewAppService
  fullName.vb: HotPreview.SharedModel.App.PreviewAppService.New
  nameWithType: PreviewAppService.PreviewAppService
  nameWithType.vb: PreviewAppService.New
- uid: HotPreview.SharedModel.App.PreviewAppService.GetCommand(System.String)
  name: GetCommand(string)
  href: api/HotPreview.SharedModel.App.PreviewAppService.html#HotPreview_SharedModel_App_PreviewAppService_GetCommand_System_String_
  commentId: M:HotPreview.SharedModel.App.PreviewAppService.GetCommand(System.String)
  name.vb: GetCommand(String)
  fullName: HotPreview.SharedModel.App.PreviewAppService.GetCommand(string)
  fullName.vb: HotPreview.SharedModel.App.PreviewAppService.GetCommand(String)
  nameWithType: PreviewAppService.GetCommand(string)
  nameWithType.vb: PreviewAppService.GetCommand(String)
- uid: HotPreview.SharedModel.App.PreviewAppService.GetCommand*
  name: GetCommand
  href: api/HotPreview.SharedModel.App.PreviewAppService.html#HotPreview_SharedModel_App_PreviewAppService_GetCommand_
  commentId: Overload:HotPreview.SharedModel.App.PreviewAppService.GetCommand
  isSpec: "True"
  fullName: HotPreview.SharedModel.App.PreviewAppService.GetCommand
  nameWithType: PreviewAppService.GetCommand
- uid: HotPreview.SharedModel.App.PreviewAppService.GetCommandIfExists(System.String)
  name: GetCommandIfExists(string)
  href: api/HotPreview.SharedModel.App.PreviewAppService.html#HotPreview_SharedModel_App_PreviewAppService_GetCommandIfExists_System_String_
  commentId: M:HotPreview.SharedModel.App.PreviewAppService.GetCommandIfExists(System.String)
  name.vb: GetCommandIfExists(String)
  fullName: HotPreview.SharedModel.App.PreviewAppService.GetCommandIfExists(string)
  fullName.vb: HotPreview.SharedModel.App.PreviewAppService.GetCommandIfExists(String)
  nameWithType: PreviewAppService.GetCommandIfExists(string)
  nameWithType.vb: PreviewAppService.GetCommandIfExists(String)
- uid: HotPreview.SharedModel.App.PreviewAppService.GetCommandIfExists*
  name: GetCommandIfExists
  href: api/HotPreview.SharedModel.App.PreviewAppService.html#HotPreview_SharedModel_App_PreviewAppService_GetCommandIfExists_
  commentId: Overload:HotPreview.SharedModel.App.PreviewAppService.GetCommandIfExists
  isSpec: "True"
  fullName: HotPreview.SharedModel.App.PreviewAppService.GetCommandIfExists
  nameWithType: PreviewAppService.GetCommandIfExists
- uid: HotPreview.SharedModel.App.PreviewAppService.GetCommandsAsync
  name: GetCommandsAsync()
  href: api/HotPreview.SharedModel.App.PreviewAppService.html#HotPreview_SharedModel_App_PreviewAppService_GetCommandsAsync
  commentId: M:HotPreview.SharedModel.App.PreviewAppService.GetCommandsAsync
  fullName: HotPreview.SharedModel.App.PreviewAppService.GetCommandsAsync()
  nameWithType: PreviewAppService.GetCommandsAsync()
- uid: HotPreview.SharedModel.App.PreviewAppService.GetCommandsAsync*
  name: GetCommandsAsync
  href: api/HotPreview.SharedModel.App.PreviewAppService.html#HotPreview_SharedModel_App_PreviewAppService_GetCommandsAsync_
  commentId: Overload:HotPreview.SharedModel.App.PreviewAppService.GetCommandsAsync
  isSpec: "True"
  fullName: HotPreview.SharedModel.App.PreviewAppService.GetCommandsAsync
  nameWithType: PreviewAppService.GetCommandsAsync
- uid: HotPreview.SharedModel.App.PreviewAppService.GetPreviewSnapshotAsync(System.String,System.String)
  name: GetPreviewSnapshotAsync(string, string)
  href: api/HotPreview.SharedModel.App.PreviewAppService.html#HotPreview_SharedModel_App_PreviewAppService_GetPreviewSnapshotAsync_System_String_System_String_
  commentId: M:HotPreview.SharedModel.App.PreviewAppService.GetPreviewSnapshotAsync(System.String,System.String)
  name.vb: GetPreviewSnapshotAsync(String, String)
  fullName: HotPreview.SharedModel.App.PreviewAppService.GetPreviewSnapshotAsync(string, string)
  fullName.vb: HotPreview.SharedModel.App.PreviewAppService.GetPreviewSnapshotAsync(String, String)
  nameWithType: PreviewAppService.GetPreviewSnapshotAsync(string, string)
  nameWithType.vb: PreviewAppService.GetPreviewSnapshotAsync(String, String)
- uid: HotPreview.SharedModel.App.PreviewAppService.GetPreviewSnapshotAsync*
  name: GetPreviewSnapshotAsync
  href: api/HotPreview.SharedModel.App.PreviewAppService.html#HotPreview_SharedModel_App_PreviewAppService_GetPreviewSnapshotAsync_
  commentId: Overload:HotPreview.SharedModel.App.PreviewAppService.GetPreviewSnapshotAsync
  isSpec: "True"
  fullName: HotPreview.SharedModel.App.PreviewAppService.GetPreviewSnapshotAsync
  nameWithType: PreviewAppService.GetPreviewSnapshotAsync
- uid: HotPreview.SharedModel.App.PreviewAppService.GetUIComponent(System.String)
  name: GetUIComponent(string)
  href: api/HotPreview.SharedModel.App.PreviewAppService.html#HotPreview_SharedModel_App_PreviewAppService_GetUIComponent_System_String_
  commentId: M:HotPreview.SharedModel.App.PreviewAppService.GetUIComponent(System.String)
  name.vb: GetUIComponent(String)
  fullName: HotPreview.SharedModel.App.PreviewAppService.GetUIComponent(string)
  fullName.vb: HotPreview.SharedModel.App.PreviewAppService.GetUIComponent(String)
  nameWithType: PreviewAppService.GetUIComponent(string)
  nameWithType.vb: PreviewAppService.GetUIComponent(String)
- uid: HotPreview.SharedModel.App.PreviewAppService.GetUIComponent*
  name: GetUIComponent
  href: api/HotPreview.SharedModel.App.PreviewAppService.html#HotPreview_SharedModel_App_PreviewAppService_GetUIComponent_
  commentId: Overload:HotPreview.SharedModel.App.PreviewAppService.GetUIComponent
  isSpec: "True"
  fullName: HotPreview.SharedModel.App.PreviewAppService.GetUIComponent
  nameWithType: PreviewAppService.GetUIComponent
- uid: HotPreview.SharedModel.App.PreviewAppService.GetUIComponentIfExists(System.String)
  name: GetUIComponentIfExists(string)
  href: api/HotPreview.SharedModel.App.PreviewAppService.html#HotPreview_SharedModel_App_PreviewAppService_GetUIComponentIfExists_System_String_
  commentId: M:HotPreview.SharedModel.App.PreviewAppService.GetUIComponentIfExists(System.String)
  name.vb: GetUIComponentIfExists(String)
  fullName: HotPreview.SharedModel.App.PreviewAppService.GetUIComponentIfExists(string)
  fullName.vb: HotPreview.SharedModel.App.PreviewAppService.GetUIComponentIfExists(String)
  nameWithType: PreviewAppService.GetUIComponentIfExists(string)
  nameWithType.vb: PreviewAppService.GetUIComponentIfExists(String)
- uid: HotPreview.SharedModel.App.PreviewAppService.GetUIComponentIfExists*
  name: GetUIComponentIfExists
  href: api/HotPreview.SharedModel.App.PreviewAppService.html#HotPreview_SharedModel_App_PreviewAppService_GetUIComponentIfExists_
  commentId: Overload:HotPreview.SharedModel.App.PreviewAppService.GetUIComponentIfExists
  isSpec: "True"
  fullName: HotPreview.SharedModel.App.PreviewAppService.GetUIComponentIfExists
  nameWithType: PreviewAppService.GetUIComponentIfExists
- uid: HotPreview.SharedModel.App.PreviewAppService.GetUIComponentPreviewPair(System.String,System.String)
  name: GetUIComponentPreviewPair(string, string)
  href: api/HotPreview.SharedModel.App.PreviewAppService.html#HotPreview_SharedModel_App_PreviewAppService_GetUIComponentPreviewPair_System_String_System_String_
  commentId: M:HotPreview.SharedModel.App.PreviewAppService.GetUIComponentPreviewPair(System.String,System.String)
  name.vb: GetUIComponentPreviewPair(String, String)
  fullName: HotPreview.SharedModel.App.PreviewAppService.GetUIComponentPreviewPair(string, string)
  fullName.vb: HotPreview.SharedModel.App.PreviewAppService.GetUIComponentPreviewPair(String, String)
  nameWithType: PreviewAppService.GetUIComponentPreviewPair(string, string)
  nameWithType.vb: PreviewAppService.GetUIComponentPreviewPair(String, String)
- uid: HotPreview.SharedModel.App.PreviewAppService.GetUIComponentPreviewPair*
  name: GetUIComponentPreviewPair
  href: api/HotPreview.SharedModel.App.PreviewAppService.html#HotPreview_SharedModel_App_PreviewAppService_GetUIComponentPreviewPair_
  commentId: Overload:HotPreview.SharedModel.App.PreviewAppService.GetUIComponentPreviewPair
  isSpec: "True"
  fullName: HotPreview.SharedModel.App.PreviewAppService.GetUIComponentPreviewPair
  nameWithType: PreviewAppService.GetUIComponentPreviewPair
- uid: HotPreview.SharedModel.App.PreviewAppService.GetUIComponentPreviewsAsync(System.String)
  name: GetUIComponentPreviewsAsync(string)
  href: api/HotPreview.SharedModel.App.PreviewAppService.html#HotPreview_SharedModel_App_PreviewAppService_GetUIComponentPreviewsAsync_System_String_
  commentId: M:HotPreview.SharedModel.App.PreviewAppService.GetUIComponentPreviewsAsync(System.String)
  name.vb: GetUIComponentPreviewsAsync(String)
  fullName: HotPreview.SharedModel.App.PreviewAppService.GetUIComponentPreviewsAsync(string)
  fullName.vb: HotPreview.SharedModel.App.PreviewAppService.GetUIComponentPreviewsAsync(String)
  nameWithType: PreviewAppService.GetUIComponentPreviewsAsync(string)
  nameWithType.vb: PreviewAppService.GetUIComponentPreviewsAsync(String)
- uid: HotPreview.SharedModel.App.PreviewAppService.GetUIComponentPreviewsAsync*
  name: GetUIComponentPreviewsAsync
  href: api/HotPreview.SharedModel.App.PreviewAppService.html#HotPreview_SharedModel_App_PreviewAppService_GetUIComponentPreviewsAsync_
  commentId: Overload:HotPreview.SharedModel.App.PreviewAppService.GetUIComponentPreviewsAsync
  isSpec: "True"
  fullName: HotPreview.SharedModel.App.PreviewAppService.GetUIComponentPreviewsAsync
  nameWithType: PreviewAppService.GetUIComponentPreviewsAsync
- uid: HotPreview.SharedModel.App.PreviewAppService.GetUIComponentsAsync
  name: GetUIComponentsAsync()
  href: api/HotPreview.SharedModel.App.PreviewAppService.html#HotPreview_SharedModel_App_PreviewAppService_GetUIComponentsAsync
  commentId: M:HotPreview.SharedModel.App.PreviewAppService.GetUIComponentsAsync
  fullName: HotPreview.SharedModel.App.PreviewAppService.GetUIComponentsAsync()
  nameWithType: PreviewAppService.GetUIComponentsAsync()
- uid: HotPreview.SharedModel.App.PreviewAppService.GetUIComponentsAsync*
  name: GetUIComponentsAsync
  href: api/HotPreview.SharedModel.App.PreviewAppService.html#HotPreview_SharedModel_App_PreviewAppService_GetUIComponentsAsync_
  commentId: Overload:HotPreview.SharedModel.App.PreviewAppService.GetUIComponentsAsync
  isSpec: "True"
  fullName: HotPreview.SharedModel.App.PreviewAppService.GetUIComponentsAsync
  nameWithType: PreviewAppService.GetUIComponentsAsync
- uid: HotPreview.SharedModel.App.PreviewAppService.InvokeCommandAsync(System.String)
  name: InvokeCommandAsync(string)
  href: api/HotPreview.SharedModel.App.PreviewAppService.html#HotPreview_SharedModel_App_PreviewAppService_InvokeCommandAsync_System_String_
  commentId: M:HotPreview.SharedModel.App.PreviewAppService.InvokeCommandAsync(System.String)
  name.vb: InvokeCommandAsync(String)
  fullName: HotPreview.SharedModel.App.PreviewAppService.InvokeCommandAsync(string)
  fullName.vb: HotPreview.SharedModel.App.PreviewAppService.InvokeCommandAsync(String)
  nameWithType: PreviewAppService.InvokeCommandAsync(string)
  nameWithType.vb: PreviewAppService.InvokeCommandAsync(String)
- uid: HotPreview.SharedModel.App.PreviewAppService.InvokeCommandAsync*
  name: InvokeCommandAsync
  href: api/HotPreview.SharedModel.App.PreviewAppService.html#HotPreview_SharedModel_App_PreviewAppService_InvokeCommandAsync_
  commentId: Overload:HotPreview.SharedModel.App.PreviewAppService.InvokeCommandAsync
  isSpec: "True"
  fullName: HotPreview.SharedModel.App.PreviewAppService.InvokeCommandAsync
  nameWithType: PreviewAppService.InvokeCommandAsync
- uid: HotPreview.SharedModel.App.PreviewAppService.NavigateToPreviewAsync(System.String,System.String)
  name: NavigateToPreviewAsync(string, string)
  href: api/HotPreview.SharedModel.App.PreviewAppService.html#HotPreview_SharedModel_App_PreviewAppService_NavigateToPreviewAsync_System_String_System_String_
  commentId: M:HotPreview.SharedModel.App.PreviewAppService.NavigateToPreviewAsync(System.String,System.String)
  name.vb: NavigateToPreviewAsync(String, String)
  fullName: HotPreview.SharedModel.App.PreviewAppService.NavigateToPreviewAsync(string, string)
  fullName.vb: HotPreview.SharedModel.App.PreviewAppService.NavigateToPreviewAsync(String, String)
  nameWithType: PreviewAppService.NavigateToPreviewAsync(string, string)
  nameWithType.vb: PreviewAppService.NavigateToPreviewAsync(String, String)
- uid: HotPreview.SharedModel.App.PreviewAppService.NavigateToPreviewAsync*
  name: NavigateToPreviewAsync
  href: api/HotPreview.SharedModel.App.PreviewAppService.html#HotPreview_SharedModel_App_PreviewAppService_NavigateToPreviewAsync_
  commentId: Overload:HotPreview.SharedModel.App.PreviewAppService.NavigateToPreviewAsync
  isSpec: "True"
  fullName: HotPreview.SharedModel.App.PreviewAppService.NavigateToPreviewAsync
  nameWithType: PreviewAppService.NavigateToPreviewAsync
- uid: HotPreview.SharedModel.App.PreviewAppService.PreviewApplication
  name: PreviewApplication
  href: api/HotPreview.SharedModel.App.PreviewAppService.html#HotPreview_SharedModel_App_PreviewAppService_PreviewApplication
  commentId: P:HotPreview.SharedModel.App.PreviewAppService.PreviewApplication
  fullName: HotPreview.SharedModel.App.PreviewAppService.PreviewApplication
  nameWithType: PreviewAppService.PreviewApplication
- uid: HotPreview.SharedModel.App.PreviewAppService.PreviewApplication*
  name: PreviewApplication
  href: api/HotPreview.SharedModel.App.PreviewAppService.html#HotPreview_SharedModel_App_PreviewAppService_PreviewApplication_
  commentId: Overload:HotPreview.SharedModel.App.PreviewAppService.PreviewApplication
  isSpec: "True"
  fullName: HotPreview.SharedModel.App.PreviewAppService.PreviewApplication
  nameWithType: PreviewAppService.PreviewApplication
- uid: HotPreview.SharedModel.App.PreviewApplication
  name: PreviewApplication
  href: api/HotPreview.SharedModel.App.PreviewApplication.html
  commentId: T:HotPreview.SharedModel.App.PreviewApplication
  fullName: HotPreview.SharedModel.App.PreviewApplication
  nameWithType: PreviewApplication
- uid: HotPreview.SharedModel.App.PreviewApplication.AddAdditionalAppAssembly(System.String)
  name: AddAdditionalAppAssembly(string)
  href: api/HotPreview.SharedModel.App.PreviewApplication.html#HotPreview_SharedModel_App_PreviewApplication_AddAdditionalAppAssembly_System_String_
  commentId: M:HotPreview.SharedModel.App.PreviewApplication.AddAdditionalAppAssembly(System.String)
  name.vb: AddAdditionalAppAssembly(String)
  fullName: HotPreview.SharedModel.App.PreviewApplication.AddAdditionalAppAssembly(string)
  fullName.vb: HotPreview.SharedModel.App.PreviewApplication.AddAdditionalAppAssembly(String)
  nameWithType: PreviewApplication.AddAdditionalAppAssembly(string)
  nameWithType.vb: PreviewApplication.AddAdditionalAppAssembly(String)
- uid: HotPreview.SharedModel.App.PreviewApplication.AddAdditionalAppAssembly*
  name: AddAdditionalAppAssembly
  href: api/HotPreview.SharedModel.App.PreviewApplication.html#HotPreview_SharedModel_App_PreviewApplication_AddAdditionalAppAssembly_
  commentId: Overload:HotPreview.SharedModel.App.PreviewApplication.AddAdditionalAppAssembly
  isSpec: "True"
  fullName: HotPreview.SharedModel.App.PreviewApplication.AddAdditionalAppAssembly
  nameWithType: PreviewApplication.AddAdditionalAppAssembly
- uid: HotPreview.SharedModel.App.PreviewApplication.AdditionalAppAssemblies
  name: AdditionalAppAssemblies
  href: api/HotPreview.SharedModel.App.PreviewApplication.html#HotPreview_SharedModel_App_PreviewApplication_AdditionalAppAssemblies
  commentId: P:HotPreview.SharedModel.App.PreviewApplication.AdditionalAppAssemblies
  fullName: HotPreview.SharedModel.App.PreviewApplication.AdditionalAppAssemblies
  nameWithType: PreviewApplication.AdditionalAppAssemblies
- uid: HotPreview.SharedModel.App.PreviewApplication.AdditionalAppAssemblies*
  name: AdditionalAppAssemblies
  href: api/HotPreview.SharedModel.App.PreviewApplication.html#HotPreview_SharedModel_App_PreviewApplication_AdditionalAppAssemblies_
  commentId: Overload:HotPreview.SharedModel.App.PreviewApplication.AdditionalAppAssemblies
  isSpec: "True"
  fullName: HotPreview.SharedModel.App.PreviewApplication.AdditionalAppAssemblies
  nameWithType: PreviewApplication.AdditionalAppAssemblies
- uid: HotPreview.SharedModel.App.PreviewApplication.Dispose
  name: Dispose()
  href: api/HotPreview.SharedModel.App.PreviewApplication.html#HotPreview_SharedModel_App_PreviewApplication_Dispose
  commentId: M:HotPreview.SharedModel.App.PreviewApplication.Dispose
  fullName: HotPreview.SharedModel.App.PreviewApplication.Dispose()
  nameWithType: PreviewApplication.Dispose()
- uid: HotPreview.SharedModel.App.PreviewApplication.Dispose(System.Boolean)
  name: Dispose(bool)
  href: api/HotPreview.SharedModel.App.PreviewApplication.html#HotPreview_SharedModel_App_PreviewApplication_Dispose_System_Boolean_
  commentId: M:HotPreview.SharedModel.App.PreviewApplication.Dispose(System.Boolean)
  name.vb: Dispose(Boolean)
  fullName: HotPreview.SharedModel.App.PreviewApplication.Dispose(bool)
  fullName.vb: HotPreview.SharedModel.App.PreviewApplication.Dispose(Boolean)
  nameWithType: PreviewApplication.Dispose(bool)
  nameWithType.vb: PreviewApplication.Dispose(Boolean)
- uid: HotPreview.SharedModel.App.PreviewApplication.Dispose*
  name: Dispose
  href: api/HotPreview.SharedModel.App.PreviewApplication.html#HotPreview_SharedModel_App_PreviewApplication_Dispose_
  commentId: Overload:HotPreview.SharedModel.App.PreviewApplication.Dispose
  isSpec: "True"
  fullName: HotPreview.SharedModel.App.PreviewApplication.Dispose
  nameWithType: PreviewApplication.Dispose
- uid: HotPreview.SharedModel.App.PreviewApplication.EnableJsonRpcTracing
  name: EnableJsonRpcTracing
  href: api/HotPreview.SharedModel.App.PreviewApplication.html#HotPreview_SharedModel_App_PreviewApplication_EnableJsonRpcTracing
  commentId: P:HotPreview.SharedModel.App.PreviewApplication.EnableJsonRpcTracing
  fullName: HotPreview.SharedModel.App.PreviewApplication.EnableJsonRpcTracing
  nameWithType: PreviewApplication.EnableJsonRpcTracing
- uid: HotPreview.SharedModel.App.PreviewApplication.EnableJsonRpcTracing*
  name: EnableJsonRpcTracing
  href: api/HotPreview.SharedModel.App.PreviewApplication.html#HotPreview_SharedModel_App_PreviewApplication_EnableJsonRpcTracing_
  commentId: Overload:HotPreview.SharedModel.App.PreviewApplication.EnableJsonRpcTracing
  isSpec: "True"
  fullName: HotPreview.SharedModel.App.PreviewApplication.EnableJsonRpcTracing
  nameWithType: PreviewApplication.EnableJsonRpcTracing
- uid: HotPreview.SharedModel.App.PreviewApplication.GetInstance
  name: GetInstance()
  href: api/HotPreview.SharedModel.App.PreviewApplication.html#HotPreview_SharedModel_App_PreviewApplication_GetInstance
  commentId: M:HotPreview.SharedModel.App.PreviewApplication.GetInstance
  fullName: HotPreview.SharedModel.App.PreviewApplication.GetInstance()
  nameWithType: PreviewApplication.GetInstance()
- uid: HotPreview.SharedModel.App.PreviewApplication.GetInstance*
  name: GetInstance
  href: api/HotPreview.SharedModel.App.PreviewApplication.html#HotPreview_SharedModel_App_PreviewApplication_GetInstance_
  commentId: Overload:HotPreview.SharedModel.App.PreviewApplication.GetInstance
  isSpec: "True"
  fullName: HotPreview.SharedModel.App.PreviewApplication.GetInstance
  nameWithType: PreviewApplication.GetInstance
- uid: HotPreview.SharedModel.App.PreviewApplication.GetPreviewAppService
  name: GetPreviewAppService()
  href: api/HotPreview.SharedModel.App.PreviewApplication.html#HotPreview_SharedModel_App_PreviewApplication_GetPreviewAppService
  commentId: M:HotPreview.SharedModel.App.PreviewApplication.GetPreviewAppService
  fullName: HotPreview.SharedModel.App.PreviewApplication.GetPreviewAppService()
  nameWithType: PreviewApplication.GetPreviewAppService()
- uid: HotPreview.SharedModel.App.PreviewApplication.GetPreviewAppService*
  name: GetPreviewAppService
  href: api/HotPreview.SharedModel.App.PreviewApplication.html#HotPreview_SharedModel_App_PreviewApplication_GetPreviewAppService_
  commentId: Overload:HotPreview.SharedModel.App.PreviewApplication.GetPreviewAppService
  isSpec: "True"
  fullName: HotPreview.SharedModel.App.PreviewApplication.GetPreviewAppService
  nameWithType: PreviewApplication.GetPreviewAppService
- uid: HotPreview.SharedModel.App.PreviewApplication.GetPreviewNavigator
  name: GetPreviewNavigator()
  href: api/HotPreview.SharedModel.App.PreviewApplication.html#HotPreview_SharedModel_App_PreviewApplication_GetPreviewNavigator
  commentId: M:HotPreview.SharedModel.App.PreviewApplication.GetPreviewNavigator
  fullName: HotPreview.SharedModel.App.PreviewApplication.GetPreviewNavigator()
  nameWithType: PreviewApplication.GetPreviewNavigator()
- uid: HotPreview.SharedModel.App.PreviewApplication.GetPreviewNavigator*
  name: GetPreviewNavigator
  href: api/HotPreview.SharedModel.App.PreviewApplication.html#HotPreview_SharedModel_App_PreviewApplication_GetPreviewNavigator_
  commentId: Overload:HotPreview.SharedModel.App.PreviewApplication.GetPreviewNavigator
  isSpec: "True"
  fullName: HotPreview.SharedModel.App.PreviewApplication.GetPreviewNavigator
  nameWithType: PreviewApplication.GetPreviewNavigator
- uid: HotPreview.SharedModel.App.PreviewApplication.GetPreviewsManager
  name: GetPreviewsManager()
  href: api/HotPreview.SharedModel.App.PreviewApplication.html#HotPreview_SharedModel_App_PreviewApplication_GetPreviewsManager
  commentId: M:HotPreview.SharedModel.App.PreviewApplication.GetPreviewsManager
  fullName: HotPreview.SharedModel.App.PreviewApplication.GetPreviewsManager()
  nameWithType: PreviewApplication.GetPreviewsManager()
- uid: HotPreview.SharedModel.App.PreviewApplication.GetPreviewsManager*
  name: GetPreviewsManager
  href: api/HotPreview.SharedModel.App.PreviewApplication.html#HotPreview_SharedModel_App_PreviewApplication_GetPreviewsManager_
  commentId: Overload:HotPreview.SharedModel.App.PreviewApplication.GetPreviewsManager
  isSpec: "True"
  fullName: HotPreview.SharedModel.App.PreviewApplication.GetPreviewsManager
  nameWithType: PreviewApplication.GetPreviewsManager
- uid: HotPreview.SharedModel.App.PreviewApplication.GetRequiredService*
  name: GetRequiredService
  href: api/HotPreview.SharedModel.App.PreviewApplication.html#HotPreview_SharedModel_App_PreviewApplication_GetRequiredService_
  commentId: Overload:HotPreview.SharedModel.App.PreviewApplication.GetRequiredService
  isSpec: "True"
  fullName: HotPreview.SharedModel.App.PreviewApplication.GetRequiredService
  nameWithType: PreviewApplication.GetRequiredService
- uid: HotPreview.SharedModel.App.PreviewApplication.GetRequiredService``1
  name: GetRequiredService<TService>()
  href: api/HotPreview.SharedModel.App.PreviewApplication.html#HotPreview_SharedModel_App_PreviewApplication_GetRequiredService__1
  commentId: M:HotPreview.SharedModel.App.PreviewApplication.GetRequiredService``1
  name.vb: GetRequiredService(Of TService)()
  fullName: HotPreview.SharedModel.App.PreviewApplication.GetRequiredService<TService>()
  fullName.vb: HotPreview.SharedModel.App.PreviewApplication.GetRequiredService(Of TService)()
  nameWithType: PreviewApplication.GetRequiredService<TService>()
  nameWithType.vb: PreviewApplication.GetRequiredService(Of TService)()
- uid: HotPreview.SharedModel.App.PreviewApplication.InitInstance(HotPreview.SharedModel.App.PreviewApplication)
  name: InitInstance(PreviewApplication)
  href: api/HotPreview.SharedModel.App.PreviewApplication.html#HotPreview_SharedModel_App_PreviewApplication_InitInstance_HotPreview_SharedModel_App_PreviewApplication_
  commentId: M:HotPreview.SharedModel.App.PreviewApplication.InitInstance(HotPreview.SharedModel.App.PreviewApplication)
  fullName: HotPreview.SharedModel.App.PreviewApplication.InitInstance(HotPreview.SharedModel.App.PreviewApplication)
  nameWithType: PreviewApplication.InitInstance(PreviewApplication)
- uid: HotPreview.SharedModel.App.PreviewApplication.InitInstance*
  name: InitInstance
  href: api/HotPreview.SharedModel.App.PreviewApplication.html#HotPreview_SharedModel_App_PreviewApplication_InitInstance_
  commentId: Overload:HotPreview.SharedModel.App.PreviewApplication.InitInstance
  isSpec: "True"
  fullName: HotPreview.SharedModel.App.PreviewApplication.InitInstance
  nameWithType: PreviewApplication.InitInstance
- uid: HotPreview.SharedModel.App.PreviewApplication.MainAssembly
  name: MainAssembly
  href: api/HotPreview.SharedModel.App.PreviewApplication.html#HotPreview_SharedModel_App_PreviewApplication_MainAssembly
  commentId: P:HotPreview.SharedModel.App.PreviewApplication.MainAssembly
  fullName: HotPreview.SharedModel.App.PreviewApplication.MainAssembly
  nameWithType: PreviewApplication.MainAssembly
- uid: HotPreview.SharedModel.App.PreviewApplication.MainAssembly*
  name: MainAssembly
  href: api/HotPreview.SharedModel.App.PreviewApplication.html#HotPreview_SharedModel_App_PreviewApplication_MainAssembly_
  commentId: Overload:HotPreview.SharedModel.App.PreviewApplication.MainAssembly
  isSpec: "True"
  fullName: HotPreview.SharedModel.App.PreviewApplication.MainAssembly
  nameWithType: PreviewApplication.MainAssembly
- uid: HotPreview.SharedModel.App.PreviewApplication.PlatformName
  name: PlatformName
  href: api/HotPreview.SharedModel.App.PreviewApplication.html#HotPreview_SharedModel_App_PreviewApplication_PlatformName
  commentId: P:HotPreview.SharedModel.App.PreviewApplication.PlatformName
  fullName: HotPreview.SharedModel.App.PreviewApplication.PlatformName
  nameWithType: PreviewApplication.PlatformName
- uid: HotPreview.SharedModel.App.PreviewApplication.PlatformName*
  name: PlatformName
  href: api/HotPreview.SharedModel.App.PreviewApplication.html#HotPreview_SharedModel_App_PreviewApplication_PlatformName_
  commentId: Overload:HotPreview.SharedModel.App.PreviewApplication.PlatformName
  isSpec: "True"
  fullName: HotPreview.SharedModel.App.PreviewApplication.PlatformName
  nameWithType: PreviewApplication.PlatformName
- uid: HotPreview.SharedModel.App.PreviewApplication.ProjectPath
  name: ProjectPath
  href: api/HotPreview.SharedModel.App.PreviewApplication.html#HotPreview_SharedModel_App_PreviewApplication_ProjectPath
  commentId: P:HotPreview.SharedModel.App.PreviewApplication.ProjectPath
  fullName: HotPreview.SharedModel.App.PreviewApplication.ProjectPath
  nameWithType: PreviewApplication.ProjectPath
- uid: HotPreview.SharedModel.App.PreviewApplication.ProjectPath*
  name: ProjectPath
  href: api/HotPreview.SharedModel.App.PreviewApplication.html#HotPreview_SharedModel_App_PreviewApplication_ProjectPath_
  commentId: Overload:HotPreview.SharedModel.App.PreviewApplication.ProjectPath
  isSpec: "True"
  fullName: HotPreview.SharedModel.App.PreviewApplication.ProjectPath
  nameWithType: PreviewApplication.ProjectPath
- uid: HotPreview.SharedModel.App.PreviewApplication.ServiceProvider
  name: ServiceProvider
  href: api/HotPreview.SharedModel.App.PreviewApplication.html#HotPreview_SharedModel_App_PreviewApplication_ServiceProvider
  commentId: P:HotPreview.SharedModel.App.PreviewApplication.ServiceProvider
  fullName: HotPreview.SharedModel.App.PreviewApplication.ServiceProvider
  nameWithType: PreviewApplication.ServiceProvider
- uid: HotPreview.SharedModel.App.PreviewApplication.ServiceProvider*
  name: ServiceProvider
  href: api/HotPreview.SharedModel.App.PreviewApplication.html#HotPreview_SharedModel_App_PreviewApplication_ServiceProvider_
  commentId: Overload:HotPreview.SharedModel.App.PreviewApplication.ServiceProvider
  isSpec: "True"
  fullName: HotPreview.SharedModel.App.PreviewApplication.ServiceProvider
  nameWithType: PreviewApplication.ServiceProvider
- uid: HotPreview.SharedModel.App.PreviewApplication.StartToolingConnection
  name: StartToolingConnection()
  href: api/HotPreview.SharedModel.App.PreviewApplication.html#HotPreview_SharedModel_App_PreviewApplication_StartToolingConnection
  commentId: M:HotPreview.SharedModel.App.PreviewApplication.StartToolingConnection
  fullName: HotPreview.SharedModel.App.PreviewApplication.StartToolingConnection()
  nameWithType: PreviewApplication.StartToolingConnection()
- uid: HotPreview.SharedModel.App.PreviewApplication.StartToolingConnection*
  name: StartToolingConnection
  href: api/HotPreview.SharedModel.App.PreviewApplication.html#HotPreview_SharedModel_App_PreviewApplication_StartToolingConnection_
  commentId: Overload:HotPreview.SharedModel.App.PreviewApplication.StartToolingConnection
  isSpec: "True"
  fullName: HotPreview.SharedModel.App.PreviewApplication.StartToolingConnection
  nameWithType: PreviewApplication.StartToolingConnection
- uid: HotPreview.SharedModel.App.PreviewApplication.StopToolingConnection
  name: StopToolingConnection()
  href: api/HotPreview.SharedModel.App.PreviewApplication.html#HotPreview_SharedModel_App_PreviewApplication_StopToolingConnection
  commentId: M:HotPreview.SharedModel.App.PreviewApplication.StopToolingConnection
  fullName: HotPreview.SharedModel.App.PreviewApplication.StopToolingConnection()
  nameWithType: PreviewApplication.StopToolingConnection()
- uid: HotPreview.SharedModel.App.PreviewApplication.StopToolingConnection*
  name: StopToolingConnection
  href: api/HotPreview.SharedModel.App.PreviewApplication.html#HotPreview_SharedModel_App_PreviewApplication_StopToolingConnection_
  commentId: Overload:HotPreview.SharedModel.App.PreviewApplication.StopToolingConnection
  isSpec: "True"
  fullName: HotPreview.SharedModel.App.PreviewApplication.StopToolingConnection
  nameWithType: PreviewApplication.StopToolingConnection
- uid: HotPreview.SharedModel.App.PreviewApplication.ToolingConnectionString
  name: ToolingConnectionString
  href: api/HotPreview.SharedModel.App.PreviewApplication.html#HotPreview_SharedModel_App_PreviewApplication_ToolingConnectionString
  commentId: P:HotPreview.SharedModel.App.PreviewApplication.ToolingConnectionString
  fullName: HotPreview.SharedModel.App.PreviewApplication.ToolingConnectionString
  nameWithType: PreviewApplication.ToolingConnectionString
- uid: HotPreview.SharedModel.App.PreviewApplication.ToolingConnectionString*
  name: ToolingConnectionString
  href: api/HotPreview.SharedModel.App.PreviewApplication.html#HotPreview_SharedModel_App_PreviewApplication_ToolingConnectionString_
  commentId: Overload:HotPreview.SharedModel.App.PreviewApplication.ToolingConnectionString
  isSpec: "True"
  fullName: HotPreview.SharedModel.App.PreviewApplication.ToolingConnectionString
  nameWithType: PreviewApplication.ToolingConnectionString
- uid: HotPreview.SharedModel.App.PreviewApplication.TransformConnectionStringForPlatform(System.String)
  name: TransformConnectionStringForPlatform(string)
  href: api/HotPreview.SharedModel.App.PreviewApplication.html#HotPreview_SharedModel_App_PreviewApplication_TransformConnectionStringForPlatform_System_String_
  commentId: M:HotPreview.SharedModel.App.PreviewApplication.TransformConnectionStringForPlatform(System.String)
  name.vb: TransformConnectionStringForPlatform(String)
  fullName: HotPreview.SharedModel.App.PreviewApplication.TransformConnectionStringForPlatform(string)
  fullName.vb: HotPreview.SharedModel.App.PreviewApplication.TransformConnectionStringForPlatform(String)
  nameWithType: PreviewApplication.TransformConnectionStringForPlatform(string)
  nameWithType.vb: PreviewApplication.TransformConnectionStringForPlatform(String)
- uid: HotPreview.SharedModel.App.PreviewApplication.TransformConnectionStringForPlatform*
  name: TransformConnectionStringForPlatform
  href: api/HotPreview.SharedModel.App.PreviewApplication.html#HotPreview_SharedModel_App_PreviewApplication_TransformConnectionStringForPlatform_
  commentId: Overload:HotPreview.SharedModel.App.PreviewApplication.TransformConnectionStringForPlatform
  isSpec: "True"
  fullName: HotPreview.SharedModel.App.PreviewApplication.TransformConnectionStringForPlatform
  nameWithType: PreviewApplication.TransformConnectionStringForPlatform
- uid: HotPreview.SharedModel.App.PreviewClassReflection
  name: PreviewClassReflection
  href: api/HotPreview.SharedModel.App.PreviewClassReflection.html
  commentId: T:HotPreview.SharedModel.App.PreviewClassReflection
  fullName: HotPreview.SharedModel.App.PreviewClassReflection
  nameWithType: PreviewClassReflection
- uid: HotPreview.SharedModel.App.PreviewClassReflection.#ctor(HotPreview.PreviewAttribute,System.Type)
  name: PreviewClassReflection(PreviewAttribute, Type)
  href: api/HotPreview.SharedModel.App.PreviewClassReflection.html#HotPreview_SharedModel_App_PreviewClassReflection__ctor_HotPreview_PreviewAttribute_System_Type_
  commentId: M:HotPreview.SharedModel.App.PreviewClassReflection.#ctor(HotPreview.PreviewAttribute,System.Type)
  name.vb: New(PreviewAttribute, Type)
  fullName: HotPreview.SharedModel.App.PreviewClassReflection.PreviewClassReflection(HotPreview.PreviewAttribute, System.Type)
  fullName.vb: HotPreview.SharedModel.App.PreviewClassReflection.New(HotPreview.PreviewAttribute, System.Type)
  nameWithType: PreviewClassReflection.PreviewClassReflection(PreviewAttribute, Type)
  nameWithType.vb: PreviewClassReflection.New(PreviewAttribute, Type)
- uid: HotPreview.SharedModel.App.PreviewClassReflection.#ctor(System.Type,System.Boolean)
  name: PreviewClassReflection(Type, bool)
  href: api/HotPreview.SharedModel.App.PreviewClassReflection.html#HotPreview_SharedModel_App_PreviewClassReflection__ctor_System_Type_System_Boolean_
  commentId: M:HotPreview.SharedModel.App.PreviewClassReflection.#ctor(System.Type,System.Boolean)
  name.vb: New(Type, Boolean)
  fullName: HotPreview.SharedModel.App.PreviewClassReflection.PreviewClassReflection(System.Type, bool)
  fullName.vb: HotPreview.SharedModel.App.PreviewClassReflection.New(System.Type, Boolean)
  nameWithType: PreviewClassReflection.PreviewClassReflection(Type, bool)
  nameWithType.vb: PreviewClassReflection.New(Type, Boolean)
- uid: HotPreview.SharedModel.App.PreviewClassReflection.#ctor*
  name: PreviewClassReflection
  href: api/HotPreview.SharedModel.App.PreviewClassReflection.html#HotPreview_SharedModel_App_PreviewClassReflection__ctor_
  commentId: Overload:HotPreview.SharedModel.App.PreviewClassReflection.#ctor
  isSpec: "True"
  name.vb: New
  fullName: HotPreview.SharedModel.App.PreviewClassReflection.PreviewClassReflection
  fullName.vb: HotPreview.SharedModel.App.PreviewClassReflection.New
  nameWithType: PreviewClassReflection.PreviewClassReflection
  nameWithType.vb: PreviewClassReflection.New
- uid: HotPreview.SharedModel.App.PreviewClassReflection.Create
  name: Create()
  href: api/HotPreview.SharedModel.App.PreviewClassReflection.html#HotPreview_SharedModel_App_PreviewClassReflection_Create
  commentId: M:HotPreview.SharedModel.App.PreviewClassReflection.Create
  fullName: HotPreview.SharedModel.App.PreviewClassReflection.Create()
  nameWithType: PreviewClassReflection.Create()
- uid: HotPreview.SharedModel.App.PreviewClassReflection.Create*
  name: Create
  href: api/HotPreview.SharedModel.App.PreviewClassReflection.html#HotPreview_SharedModel_App_PreviewClassReflection_Create_
  commentId: Overload:HotPreview.SharedModel.App.PreviewClassReflection.Create
  isSpec: "True"
  fullName: HotPreview.SharedModel.App.PreviewClassReflection.Create
  nameWithType: PreviewClassReflection.Create
- uid: HotPreview.SharedModel.App.PreviewClassReflection.DefaultUIComponentType
  name: DefaultUIComponentType
  href: api/HotPreview.SharedModel.App.PreviewClassReflection.html#HotPreview_SharedModel_App_PreviewClassReflection_DefaultUIComponentType
  commentId: P:HotPreview.SharedModel.App.PreviewClassReflection.DefaultUIComponentType
  fullName: HotPreview.SharedModel.App.PreviewClassReflection.DefaultUIComponentType
  nameWithType: PreviewClassReflection.DefaultUIComponentType
- uid: HotPreview.SharedModel.App.PreviewClassReflection.DefaultUIComponentType*
  name: DefaultUIComponentType
  href: api/HotPreview.SharedModel.App.PreviewClassReflection.html#HotPreview_SharedModel_App_PreviewClassReflection_DefaultUIComponentType_
  commentId: Overload:HotPreview.SharedModel.App.PreviewClassReflection.DefaultUIComponentType
  isSpec: "True"
  fullName: HotPreview.SharedModel.App.PreviewClassReflection.DefaultUIComponentType
  nameWithType: PreviewClassReflection.DefaultUIComponentType
- uid: HotPreview.SharedModel.App.PreviewClassReflection.GetPreviewTypeInfo
  name: GetPreviewTypeInfo()
  href: api/HotPreview.SharedModel.App.PreviewClassReflection.html#HotPreview_SharedModel_App_PreviewClassReflection_GetPreviewTypeInfo
  commentId: M:HotPreview.SharedModel.App.PreviewClassReflection.GetPreviewTypeInfo
  fullName: HotPreview.SharedModel.App.PreviewClassReflection.GetPreviewTypeInfo()
  nameWithType: PreviewClassReflection.GetPreviewTypeInfo()
- uid: HotPreview.SharedModel.App.PreviewClassReflection.GetPreviewTypeInfo*
  name: GetPreviewTypeInfo
  href: api/HotPreview.SharedModel.App.PreviewClassReflection.html#HotPreview_SharedModel_App_PreviewClassReflection_GetPreviewTypeInfo_
  commentId: Overload:HotPreview.SharedModel.App.PreviewClassReflection.GetPreviewTypeInfo
  isSpec: "True"
  fullName: HotPreview.SharedModel.App.PreviewClassReflection.GetPreviewTypeInfo
  nameWithType: PreviewClassReflection.GetPreviewTypeInfo
- uid: HotPreview.SharedModel.App.PreviewClassReflection.IsAutoGenerated
  name: IsAutoGenerated
  href: api/HotPreview.SharedModel.App.PreviewClassReflection.html#HotPreview_SharedModel_App_PreviewClassReflection_IsAutoGenerated
  commentId: P:HotPreview.SharedModel.App.PreviewClassReflection.IsAutoGenerated
  fullName: HotPreview.SharedModel.App.PreviewClassReflection.IsAutoGenerated
  nameWithType: PreviewClassReflection.IsAutoGenerated
- uid: HotPreview.SharedModel.App.PreviewClassReflection.IsAutoGenerated*
  name: IsAutoGenerated
  href: api/HotPreview.SharedModel.App.PreviewClassReflection.html#HotPreview_SharedModel_App_PreviewClassReflection_IsAutoGenerated_
  commentId: Overload:HotPreview.SharedModel.App.PreviewClassReflection.IsAutoGenerated
  isSpec: "True"
  fullName: HotPreview.SharedModel.App.PreviewClassReflection.IsAutoGenerated
  nameWithType: PreviewClassReflection.IsAutoGenerated
- uid: HotPreview.SharedModel.App.PreviewClassReflection.Name
  name: Name
  href: api/HotPreview.SharedModel.App.PreviewClassReflection.html#HotPreview_SharedModel_App_PreviewClassReflection_Name
  commentId: P:HotPreview.SharedModel.App.PreviewClassReflection.Name
  fullName: HotPreview.SharedModel.App.PreviewClassReflection.Name
  nameWithType: PreviewClassReflection.Name
- uid: HotPreview.SharedModel.App.PreviewClassReflection.Name*
  name: Name
  href: api/HotPreview.SharedModel.App.PreviewClassReflection.html#HotPreview_SharedModel_App_PreviewClassReflection_Name_
  commentId: Overload:HotPreview.SharedModel.App.PreviewClassReflection.Name
  isSpec: "True"
  fullName: HotPreview.SharedModel.App.PreviewClassReflection.Name
  nameWithType: PreviewClassReflection.Name
- uid: HotPreview.SharedModel.App.PreviewClassReflection.Type
  name: Type
  href: api/HotPreview.SharedModel.App.PreviewClassReflection.html#HotPreview_SharedModel_App_PreviewClassReflection_Type
  commentId: P:HotPreview.SharedModel.App.PreviewClassReflection.Type
  fullName: HotPreview.SharedModel.App.PreviewClassReflection.Type
  nameWithType: PreviewClassReflection.Type
- uid: HotPreview.SharedModel.App.PreviewClassReflection.Type*
  name: Type
  href: api/HotPreview.SharedModel.App.PreviewClassReflection.html#HotPreview_SharedModel_App_PreviewClassReflection_Type_
  commentId: Overload:HotPreview.SharedModel.App.PreviewClassReflection.Type
  isSpec: "True"
  fullName: HotPreview.SharedModel.App.PreviewClassReflection.Type
  nameWithType: PreviewClassReflection.Type
- uid: HotPreview.SharedModel.App.PreviewCommandReflection
  name: PreviewCommandReflection
  href: api/HotPreview.SharedModel.App.PreviewCommandReflection.html
  commentId: T:HotPreview.SharedModel.App.PreviewCommandReflection
  fullName: HotPreview.SharedModel.App.PreviewCommandReflection
  nameWithType: PreviewCommandReflection
- uid: HotPreview.SharedModel.App.PreviewCommandReflection.#ctor(HotPreview.PreviewCommandAttribute,System.Reflection.MethodInfo)
  name: PreviewCommandReflection(PreviewCommandAttribute, MethodInfo)
  href: api/HotPreview.SharedModel.App.PreviewCommandReflection.html#HotPreview_SharedModel_App_PreviewCommandReflection__ctor_HotPreview_PreviewCommandAttribute_System_Reflection_MethodInfo_
  commentId: M:HotPreview.SharedModel.App.PreviewCommandReflection.#ctor(HotPreview.PreviewCommandAttribute,System.Reflection.MethodInfo)
  name.vb: New(PreviewCommandAttribute, MethodInfo)
  fullName: HotPreview.SharedModel.App.PreviewCommandReflection.PreviewCommandReflection(HotPreview.PreviewCommandAttribute, System.Reflection.MethodInfo)
  fullName.vb: HotPreview.SharedModel.App.PreviewCommandReflection.New(HotPreview.PreviewCommandAttribute, System.Reflection.MethodInfo)
  nameWithType: PreviewCommandReflection.PreviewCommandReflection(PreviewCommandAttribute, MethodInfo)
  nameWithType.vb: PreviewCommandReflection.New(PreviewCommandAttribute, MethodInfo)
- uid: HotPreview.SharedModel.App.PreviewCommandReflection.#ctor*
  name: PreviewCommandReflection
  href: api/HotPreview.SharedModel.App.PreviewCommandReflection.html#HotPreview_SharedModel_App_PreviewCommandReflection__ctor_
  commentId: Overload:HotPreview.SharedModel.App.PreviewCommandReflection.#ctor
  isSpec: "True"
  name.vb: New
  fullName: HotPreview.SharedModel.App.PreviewCommandReflection.PreviewCommandReflection
  fullName.vb: HotPreview.SharedModel.App.PreviewCommandReflection.New
  nameWithType: PreviewCommandReflection.PreviewCommandReflection
  nameWithType.vb: PreviewCommandReflection.New
- uid: HotPreview.SharedModel.App.PreviewCommandReflection.Execute
  name: Execute()
  href: api/HotPreview.SharedModel.App.PreviewCommandReflection.html#HotPreview_SharedModel_App_PreviewCommandReflection_Execute
  commentId: M:HotPreview.SharedModel.App.PreviewCommandReflection.Execute
  fullName: HotPreview.SharedModel.App.PreviewCommandReflection.Execute()
  nameWithType: PreviewCommandReflection.Execute()
- uid: HotPreview.SharedModel.App.PreviewCommandReflection.Execute*
  name: Execute
  href: api/HotPreview.SharedModel.App.PreviewCommandReflection.html#HotPreview_SharedModel_App_PreviewCommandReflection_Execute_
  commentId: Overload:HotPreview.SharedModel.App.PreviewCommandReflection.Execute
  isSpec: "True"
  fullName: HotPreview.SharedModel.App.PreviewCommandReflection.Execute
  nameWithType: PreviewCommandReflection.Execute
- uid: HotPreview.SharedModel.App.PreviewCommandReflection.MethodInfo
  name: MethodInfo
  href: api/HotPreview.SharedModel.App.PreviewCommandReflection.html#HotPreview_SharedModel_App_PreviewCommandReflection_MethodInfo
  commentId: P:HotPreview.SharedModel.App.PreviewCommandReflection.MethodInfo
  fullName: HotPreview.SharedModel.App.PreviewCommandReflection.MethodInfo
  nameWithType: PreviewCommandReflection.MethodInfo
- uid: HotPreview.SharedModel.App.PreviewCommandReflection.MethodInfo*
  name: MethodInfo
  href: api/HotPreview.SharedModel.App.PreviewCommandReflection.html#HotPreview_SharedModel_App_PreviewCommandReflection_MethodInfo_
  commentId: Overload:HotPreview.SharedModel.App.PreviewCommandReflection.MethodInfo
  isSpec: "True"
  fullName: HotPreview.SharedModel.App.PreviewCommandReflection.MethodInfo
  nameWithType: PreviewCommandReflection.MethodInfo
- uid: HotPreview.SharedModel.App.PreviewCommandReflection.Name
  name: Name
  href: api/HotPreview.SharedModel.App.PreviewCommandReflection.html#HotPreview_SharedModel_App_PreviewCommandReflection_Name
  commentId: P:HotPreview.SharedModel.App.PreviewCommandReflection.Name
  fullName: HotPreview.SharedModel.App.PreviewCommandReflection.Name
  nameWithType: PreviewCommandReflection.Name
- uid: HotPreview.SharedModel.App.PreviewCommandReflection.Name*
  name: Name
  href: api/HotPreview.SharedModel.App.PreviewCommandReflection.html#HotPreview_SharedModel_App_PreviewCommandReflection_Name_
  commentId: Overload:HotPreview.SharedModel.App.PreviewCommandReflection.Name
  isSpec: "True"
  fullName: HotPreview.SharedModel.App.PreviewCommandReflection.Name
  nameWithType: PreviewCommandReflection.Name
- uid: HotPreview.SharedModel.App.PreviewReflection
  name: PreviewReflection
  href: api/HotPreview.SharedModel.App.PreviewReflection.html
  commentId: T:HotPreview.SharedModel.App.PreviewReflection
  fullName: HotPreview.SharedModel.App.PreviewReflection
  nameWithType: PreviewReflection
- uid: HotPreview.SharedModel.App.PreviewReflection.#ctor(HotPreview.PreviewAttribute)
  name: PreviewReflection(PreviewAttribute)
  href: api/HotPreview.SharedModel.App.PreviewReflection.html#HotPreview_SharedModel_App_PreviewReflection__ctor_HotPreview_PreviewAttribute_
  commentId: M:HotPreview.SharedModel.App.PreviewReflection.#ctor(HotPreview.PreviewAttribute)
  name.vb: New(PreviewAttribute)
  fullName: HotPreview.SharedModel.App.PreviewReflection.PreviewReflection(HotPreview.PreviewAttribute)
  fullName.vb: HotPreview.SharedModel.App.PreviewReflection.New(HotPreview.PreviewAttribute)
  nameWithType: PreviewReflection.PreviewReflection(PreviewAttribute)
  nameWithType.vb: PreviewReflection.New(PreviewAttribute)
- uid: HotPreview.SharedModel.App.PreviewReflection.#ctor(System.Type)
  name: PreviewReflection(Type)
  href: api/HotPreview.SharedModel.App.PreviewReflection.html#HotPreview_SharedModel_App_PreviewReflection__ctor_System_Type_
  commentId: M:HotPreview.SharedModel.App.PreviewReflection.#ctor(System.Type)
  name.vb: New(Type)
  fullName: HotPreview.SharedModel.App.PreviewReflection.PreviewReflection(System.Type)
  fullName.vb: HotPreview.SharedModel.App.PreviewReflection.New(System.Type)
  nameWithType: PreviewReflection.PreviewReflection(Type)
  nameWithType.vb: PreviewReflection.New(Type)
- uid: HotPreview.SharedModel.App.PreviewReflection.#ctor*
  name: PreviewReflection
  href: api/HotPreview.SharedModel.App.PreviewReflection.html#HotPreview_SharedModel_App_PreviewReflection__ctor_
  commentId: Overload:HotPreview.SharedModel.App.PreviewReflection.#ctor
  isSpec: "True"
  name.vb: New
  fullName: HotPreview.SharedModel.App.PreviewReflection.PreviewReflection
  fullName.vb: HotPreview.SharedModel.App.PreviewReflection.New
  nameWithType: PreviewReflection.PreviewReflection
  nameWithType.vb: PreviewReflection.New
- uid: HotPreview.SharedModel.App.PreviewReflection.Create
  name: Create()
  href: api/HotPreview.SharedModel.App.PreviewReflection.html#HotPreview_SharedModel_App_PreviewReflection_Create
  commentId: M:HotPreview.SharedModel.App.PreviewReflection.Create
  fullName: HotPreview.SharedModel.App.PreviewReflection.Create()
  nameWithType: PreviewReflection.Create()
- uid: HotPreview.SharedModel.App.PreviewReflection.Create*
  name: Create
  href: api/HotPreview.SharedModel.App.PreviewReflection.html#HotPreview_SharedModel_App_PreviewReflection_Create_
  commentId: Overload:HotPreview.SharedModel.App.PreviewReflection.Create
  isSpec: "True"
  fullName: HotPreview.SharedModel.App.PreviewReflection.Create
  nameWithType: PreviewReflection.Create
- uid: HotPreview.SharedModel.App.PreviewReflection.DefaultUIComponentType
  name: DefaultUIComponentType
  href: api/HotPreview.SharedModel.App.PreviewReflection.html#HotPreview_SharedModel_App_PreviewReflection_DefaultUIComponentType
  commentId: P:HotPreview.SharedModel.App.PreviewReflection.DefaultUIComponentType
  fullName: HotPreview.SharedModel.App.PreviewReflection.DefaultUIComponentType
  nameWithType: PreviewReflection.DefaultUIComponentType
- uid: HotPreview.SharedModel.App.PreviewReflection.DefaultUIComponentType*
  name: DefaultUIComponentType
  href: api/HotPreview.SharedModel.App.PreviewReflection.html#HotPreview_SharedModel_App_PreviewReflection_DefaultUIComponentType_
  commentId: Overload:HotPreview.SharedModel.App.PreviewReflection.DefaultUIComponentType
  isSpec: "True"
  fullName: HotPreview.SharedModel.App.PreviewReflection.DefaultUIComponentType
  nameWithType: PreviewReflection.DefaultUIComponentType
- uid: HotPreview.SharedModel.App.PreviewReflection.GetPreviewInfo
  name: GetPreviewInfo()
  href: api/HotPreview.SharedModel.App.PreviewReflection.html#HotPreview_SharedModel_App_PreviewReflection_GetPreviewInfo
  commentId: M:HotPreview.SharedModel.App.PreviewReflection.GetPreviewInfo
  fullName: HotPreview.SharedModel.App.PreviewReflection.GetPreviewInfo()
  nameWithType: PreviewReflection.GetPreviewInfo()
- uid: HotPreview.SharedModel.App.PreviewReflection.GetPreviewInfo*
  name: GetPreviewInfo
  href: api/HotPreview.SharedModel.App.PreviewReflection.html#HotPreview_SharedModel_App_PreviewReflection_GetPreviewInfo_
  commentId: Overload:HotPreview.SharedModel.App.PreviewReflection.GetPreviewInfo
  isSpec: "True"
  fullName: HotPreview.SharedModel.App.PreviewReflection.GetPreviewInfo
  nameWithType: PreviewReflection.GetPreviewInfo
- uid: HotPreview.SharedModel.App.PreviewReflection.GetPreviewTypeInfo
  name: GetPreviewTypeInfo()
  href: api/HotPreview.SharedModel.App.PreviewReflection.html#HotPreview_SharedModel_App_PreviewReflection_GetPreviewTypeInfo
  commentId: M:HotPreview.SharedModel.App.PreviewReflection.GetPreviewTypeInfo
  fullName: HotPreview.SharedModel.App.PreviewReflection.GetPreviewTypeInfo()
  nameWithType: PreviewReflection.GetPreviewTypeInfo()
- uid: HotPreview.SharedModel.App.PreviewReflection.GetPreviewTypeInfo*
  name: GetPreviewTypeInfo
  href: api/HotPreview.SharedModel.App.PreviewReflection.html#HotPreview_SharedModel_App_PreviewReflection_GetPreviewTypeInfo_
  commentId: Overload:HotPreview.SharedModel.App.PreviewReflection.GetPreviewTypeInfo
  isSpec: "True"
  fullName: HotPreview.SharedModel.App.PreviewReflection.GetPreviewTypeInfo
  nameWithType: PreviewReflection.GetPreviewTypeInfo
- uid: HotPreview.SharedModel.App.PreviewReflection.UIComponentType
  name: UIComponentType
  href: api/HotPreview.SharedModel.App.PreviewReflection.html#HotPreview_SharedModel_App_PreviewReflection_UIComponentType
  commentId: P:HotPreview.SharedModel.App.PreviewReflection.UIComponentType
  fullName: HotPreview.SharedModel.App.PreviewReflection.UIComponentType
  nameWithType: PreviewReflection.UIComponentType
- uid: HotPreview.SharedModel.App.PreviewReflection.UIComponentType*
  name: UIComponentType
  href: api/HotPreview.SharedModel.App.PreviewReflection.html#HotPreview_SharedModel_App_PreviewReflection_UIComponentType_
  commentId: Overload:HotPreview.SharedModel.App.PreviewReflection.UIComponentType
  isSpec: "True"
  fullName: HotPreview.SharedModel.App.PreviewReflection.UIComponentType
  nameWithType: PreviewReflection.UIComponentType
- uid: HotPreview.SharedModel.App.PreviewStaticMethodReflection
  name: PreviewStaticMethodReflection
  href: api/HotPreview.SharedModel.App.PreviewStaticMethodReflection.html
  commentId: T:HotPreview.SharedModel.App.PreviewStaticMethodReflection
  fullName: HotPreview.SharedModel.App.PreviewStaticMethodReflection
  nameWithType: PreviewStaticMethodReflection
- uid: HotPreview.SharedModel.App.PreviewStaticMethodReflection.#ctor(HotPreview.PreviewAttribute,System.Reflection.MethodInfo)
  name: PreviewStaticMethodReflection(PreviewAttribute, MethodInfo)
  href: api/HotPreview.SharedModel.App.PreviewStaticMethodReflection.html#HotPreview_SharedModel_App_PreviewStaticMethodReflection__ctor_HotPreview_PreviewAttribute_System_Reflection_MethodInfo_
  commentId: M:HotPreview.SharedModel.App.PreviewStaticMethodReflection.#ctor(HotPreview.PreviewAttribute,System.Reflection.MethodInfo)
  name.vb: New(PreviewAttribute, MethodInfo)
  fullName: HotPreview.SharedModel.App.PreviewStaticMethodReflection.PreviewStaticMethodReflection(HotPreview.PreviewAttribute, System.Reflection.MethodInfo)
  fullName.vb: HotPreview.SharedModel.App.PreviewStaticMethodReflection.New(HotPreview.PreviewAttribute, System.Reflection.MethodInfo)
  nameWithType: PreviewStaticMethodReflection.PreviewStaticMethodReflection(PreviewAttribute, MethodInfo)
  nameWithType.vb: PreviewStaticMethodReflection.New(PreviewAttribute, MethodInfo)
- uid: HotPreview.SharedModel.App.PreviewStaticMethodReflection.#ctor*
  name: PreviewStaticMethodReflection
  href: api/HotPreview.SharedModel.App.PreviewStaticMethodReflection.html#HotPreview_SharedModel_App_PreviewStaticMethodReflection__ctor_
  commentId: Overload:HotPreview.SharedModel.App.PreviewStaticMethodReflection.#ctor
  isSpec: "True"
  name.vb: New
  fullName: HotPreview.SharedModel.App.PreviewStaticMethodReflection.PreviewStaticMethodReflection
  fullName.vb: HotPreview.SharedModel.App.PreviewStaticMethodReflection.New
  nameWithType: PreviewStaticMethodReflection.PreviewStaticMethodReflection
  nameWithType.vb: PreviewStaticMethodReflection.New
- uid: HotPreview.SharedModel.App.PreviewStaticMethodReflection.Create
  name: Create()
  href: api/HotPreview.SharedModel.App.PreviewStaticMethodReflection.html#HotPreview_SharedModel_App_PreviewStaticMethodReflection_Create
  commentId: M:HotPreview.SharedModel.App.PreviewStaticMethodReflection.Create
  fullName: HotPreview.SharedModel.App.PreviewStaticMethodReflection.Create()
  nameWithType: PreviewStaticMethodReflection.Create()
- uid: HotPreview.SharedModel.App.PreviewStaticMethodReflection.Create*
  name: Create
  href: api/HotPreview.SharedModel.App.PreviewStaticMethodReflection.html#HotPreview_SharedModel_App_PreviewStaticMethodReflection_Create_
  commentId: Overload:HotPreview.SharedModel.App.PreviewStaticMethodReflection.Create
  isSpec: "True"
  fullName: HotPreview.SharedModel.App.PreviewStaticMethodReflection.Create
  nameWithType: PreviewStaticMethodReflection.Create
- uid: HotPreview.SharedModel.App.PreviewStaticMethodReflection.DefaultUIComponentType
  name: DefaultUIComponentType
  href: api/HotPreview.SharedModel.App.PreviewStaticMethodReflection.html#HotPreview_SharedModel_App_PreviewStaticMethodReflection_DefaultUIComponentType
  commentId: P:HotPreview.SharedModel.App.PreviewStaticMethodReflection.DefaultUIComponentType
  fullName: HotPreview.SharedModel.App.PreviewStaticMethodReflection.DefaultUIComponentType
  nameWithType: PreviewStaticMethodReflection.DefaultUIComponentType
- uid: HotPreview.SharedModel.App.PreviewStaticMethodReflection.DefaultUIComponentType*
  name: DefaultUIComponentType
  href: api/HotPreview.SharedModel.App.PreviewStaticMethodReflection.html#HotPreview_SharedModel_App_PreviewStaticMethodReflection_DefaultUIComponentType_
  commentId: Overload:HotPreview.SharedModel.App.PreviewStaticMethodReflection.DefaultUIComponentType
  isSpec: "True"
  fullName: HotPreview.SharedModel.App.PreviewStaticMethodReflection.DefaultUIComponentType
  nameWithType: PreviewStaticMethodReflection.DefaultUIComponentType
- uid: HotPreview.SharedModel.App.PreviewStaticMethodReflection.GetPreviewTypeInfo
  name: GetPreviewTypeInfo()
  href: api/HotPreview.SharedModel.App.PreviewStaticMethodReflection.html#HotPreview_SharedModel_App_PreviewStaticMethodReflection_GetPreviewTypeInfo
  commentId: M:HotPreview.SharedModel.App.PreviewStaticMethodReflection.GetPreviewTypeInfo
  fullName: HotPreview.SharedModel.App.PreviewStaticMethodReflection.GetPreviewTypeInfo()
  nameWithType: PreviewStaticMethodReflection.GetPreviewTypeInfo()
- uid: HotPreview.SharedModel.App.PreviewStaticMethodReflection.GetPreviewTypeInfo*
  name: GetPreviewTypeInfo
  href: api/HotPreview.SharedModel.App.PreviewStaticMethodReflection.html#HotPreview_SharedModel_App_PreviewStaticMethodReflection_GetPreviewTypeInfo_
  commentId: Overload:HotPreview.SharedModel.App.PreviewStaticMethodReflection.GetPreviewTypeInfo
  isSpec: "True"
  fullName: HotPreview.SharedModel.App.PreviewStaticMethodReflection.GetPreviewTypeInfo
  nameWithType: PreviewStaticMethodReflection.GetPreviewTypeInfo
- uid: HotPreview.SharedModel.App.PreviewStaticMethodReflection.MethodInfo
  name: MethodInfo
  href: api/HotPreview.SharedModel.App.PreviewStaticMethodReflection.html#HotPreview_SharedModel_App_PreviewStaticMethodReflection_MethodInfo
  commentId: P:HotPreview.SharedModel.App.PreviewStaticMethodReflection.MethodInfo
  fullName: HotPreview.SharedModel.App.PreviewStaticMethodReflection.MethodInfo
  nameWithType: PreviewStaticMethodReflection.MethodInfo
- uid: HotPreview.SharedModel.App.PreviewStaticMethodReflection.MethodInfo*
  name: MethodInfo
  href: api/HotPreview.SharedModel.App.PreviewStaticMethodReflection.html#HotPreview_SharedModel_App_PreviewStaticMethodReflection_MethodInfo_
  commentId: Overload:HotPreview.SharedModel.App.PreviewStaticMethodReflection.MethodInfo
  isSpec: "True"
  fullName: HotPreview.SharedModel.App.PreviewStaticMethodReflection.MethodInfo
  nameWithType: PreviewStaticMethodReflection.MethodInfo
- uid: HotPreview.SharedModel.App.PreviewStaticMethodReflection.Name
  name: Name
  href: api/HotPreview.SharedModel.App.PreviewStaticMethodReflection.html#HotPreview_SharedModel_App_PreviewStaticMethodReflection_Name
  commentId: P:HotPreview.SharedModel.App.PreviewStaticMethodReflection.Name
  fullName: HotPreview.SharedModel.App.PreviewStaticMethodReflection.Name
  nameWithType: PreviewStaticMethodReflection.Name
- uid: HotPreview.SharedModel.App.PreviewStaticMethodReflection.Name*
  name: Name
  href: api/HotPreview.SharedModel.App.PreviewStaticMethodReflection.html#HotPreview_SharedModel_App_PreviewStaticMethodReflection_Name_
  commentId: Overload:HotPreview.SharedModel.App.PreviewStaticMethodReflection.Name
  isSpec: "True"
  fullName: HotPreview.SharedModel.App.PreviewStaticMethodReflection.Name
  nameWithType: PreviewStaticMethodReflection.Name
- uid: HotPreview.SharedModel.App.PreviewsManagerReflection
  name: PreviewsManagerReflection
  href: api/HotPreview.SharedModel.App.PreviewsManagerReflection.html
  commentId: T:HotPreview.SharedModel.App.PreviewsManagerReflection
  fullName: HotPreview.SharedModel.App.PreviewsManagerReflection
  nameWithType: PreviewsManagerReflection
- uid: HotPreview.SharedModel.App.PreviewsManagerReflection.#ctor(System.Collections.Generic.IReadOnlyDictionary{System.String,HotPreview.SharedModel.App.UIComponentReflection},System.Collections.Generic.IReadOnlyDictionary{System.String,HotPreview.SharedModel.UIComponentCategory},System.Collections.Generic.IReadOnlyDictionary{System.String,HotPreview.SharedModel.App.PreviewCommandReflection})
  name: PreviewsManagerReflection(IReadOnlyDictionary<string, UIComponentReflection>, IReadOnlyDictionary<string, UIComponentCategory>, IReadOnlyDictionary<string, PreviewCommandReflection>)
  href: api/HotPreview.SharedModel.App.PreviewsManagerReflection.html#HotPreview_SharedModel_App_PreviewsManagerReflection__ctor_System_Collections_Generic_IReadOnlyDictionary_System_String_HotPreview_SharedModel_App_UIComponentReflection__System_Collections_Generic_IReadOnlyDictionary_System_String_HotPreview_SharedModel_UIComponentCategory__System_Collections_Generic_IReadOnlyDictionary_System_String_HotPreview_SharedModel_App_PreviewCommandReflection__
  commentId: M:HotPreview.SharedModel.App.PreviewsManagerReflection.#ctor(System.Collections.Generic.IReadOnlyDictionary{System.String,HotPreview.SharedModel.App.UIComponentReflection},System.Collections.Generic.IReadOnlyDictionary{System.String,HotPreview.SharedModel.UIComponentCategory},System.Collections.Generic.IReadOnlyDictionary{System.String,HotPreview.SharedModel.App.PreviewCommandReflection})
  name.vb: New(IReadOnlyDictionary(Of String, UIComponentReflection), IReadOnlyDictionary(Of String, UIComponentCategory), IReadOnlyDictionary(Of String, PreviewCommandReflection))
  fullName: HotPreview.SharedModel.App.PreviewsManagerReflection.PreviewsManagerReflection(System.Collections.Generic.IReadOnlyDictionary<string, HotPreview.SharedModel.App.UIComponentReflection>, System.Collections.Generic.IReadOnlyDictionary<string, HotPreview.SharedModel.UIComponentCategory>, System.Collections.Generic.IReadOnlyDictionary<string, HotPreview.SharedModel.App.PreviewCommandReflection>)
  fullName.vb: HotPreview.SharedModel.App.PreviewsManagerReflection.New(System.Collections.Generic.IReadOnlyDictionary(Of String, HotPreview.SharedModel.App.UIComponentReflection), System.Collections.Generic.IReadOnlyDictionary(Of String, HotPreview.SharedModel.UIComponentCategory), System.Collections.Generic.IReadOnlyDictionary(Of String, HotPreview.SharedModel.App.PreviewCommandReflection))
  nameWithType: PreviewsManagerReflection.PreviewsManagerReflection(IReadOnlyDictionary<string, UIComponentReflection>, IReadOnlyDictionary<string, UIComponentCategory>, IReadOnlyDictionary<string, PreviewCommandReflection>)
  nameWithType.vb: PreviewsManagerReflection.New(IReadOnlyDictionary(Of String, UIComponentReflection), IReadOnlyDictionary(Of String, UIComponentCategory), IReadOnlyDictionary(Of String, PreviewCommandReflection))
- uid: HotPreview.SharedModel.App.PreviewsManagerReflection.#ctor*
  name: PreviewsManagerReflection
  href: api/HotPreview.SharedModel.App.PreviewsManagerReflection.html#HotPreview_SharedModel_App_PreviewsManagerReflection__ctor_
  commentId: Overload:HotPreview.SharedModel.App.PreviewsManagerReflection.#ctor
  isSpec: "True"
  name.vb: New
  fullName: HotPreview.SharedModel.App.PreviewsManagerReflection.PreviewsManagerReflection
  fullName.vb: HotPreview.SharedModel.App.PreviewsManagerReflection.New
  nameWithType: PreviewsManagerReflection.PreviewsManagerReflection
  nameWithType.vb: PreviewsManagerReflection.New
- uid: HotPreview.SharedModel.App.ToolingAppClientConnection
  name: ToolingAppClientConnection
  href: api/HotPreview.SharedModel.App.ToolingAppClientConnection.html
  commentId: T:HotPreview.SharedModel.App.ToolingAppClientConnection
  fullName: HotPreview.SharedModel.App.ToolingAppClientConnection
  nameWithType: ToolingAppClientConnection
- uid: HotPreview.SharedModel.App.ToolingAppClientConnection.#ctor(System.String)
  name: ToolingAppClientConnection(string)
  href: api/HotPreview.SharedModel.App.ToolingAppClientConnection.html#HotPreview_SharedModel_App_ToolingAppClientConnection__ctor_System_String_
  commentId: M:HotPreview.SharedModel.App.ToolingAppClientConnection.#ctor(System.String)
  name.vb: New(String)
  fullName: HotPreview.SharedModel.App.ToolingAppClientConnection.ToolingAppClientConnection(string)
  fullName.vb: HotPreview.SharedModel.App.ToolingAppClientConnection.New(String)
  nameWithType: ToolingAppClientConnection.ToolingAppClientConnection(string)
  nameWithType.vb: ToolingAppClientConnection.New(String)
- uid: HotPreview.SharedModel.App.ToolingAppClientConnection.#ctor*
  name: ToolingAppClientConnection
  href: api/HotPreview.SharedModel.App.ToolingAppClientConnection.html#HotPreview_SharedModel_App_ToolingAppClientConnection__ctor_
  commentId: Overload:HotPreview.SharedModel.App.ToolingAppClientConnection.#ctor
  isSpec: "True"
  name.vb: New
  fullName: HotPreview.SharedModel.App.ToolingAppClientConnection.ToolingAppClientConnection
  fullName.vb: HotPreview.SharedModel.App.ToolingAppClientConnection.New
  nameWithType: ToolingAppClientConnection.ToolingAppClientConnection
  nameWithType.vb: ToolingAppClientConnection.New
- uid: HotPreview.SharedModel.App.ToolingAppClientConnection.Dispose
  name: Dispose()
  href: api/HotPreview.SharedModel.App.ToolingAppClientConnection.html#HotPreview_SharedModel_App_ToolingAppClientConnection_Dispose
  commentId: M:HotPreview.SharedModel.App.ToolingAppClientConnection.Dispose
  fullName: HotPreview.SharedModel.App.ToolingAppClientConnection.Dispose()
  nameWithType: ToolingAppClientConnection.Dispose()
- uid: HotPreview.SharedModel.App.ToolingAppClientConnection.Dispose*
  name: Dispose
  href: api/HotPreview.SharedModel.App.ToolingAppClientConnection.html#HotPreview_SharedModel_App_ToolingAppClientConnection_Dispose_
  commentId: Overload:HotPreview.SharedModel.App.ToolingAppClientConnection.Dispose
  isSpec: "True"
  fullName: HotPreview.SharedModel.App.ToolingAppClientConnection.Dispose
  nameWithType: ToolingAppClientConnection.Dispose
- uid: HotPreview.SharedModel.App.ToolingAppClientConnection.StartConnectionAsync(HotPreview.SharedModel.App.PreviewAppService)
  name: StartConnectionAsync(PreviewAppService)
  href: api/HotPreview.SharedModel.App.ToolingAppClientConnection.html#HotPreview_SharedModel_App_ToolingAppClientConnection_StartConnectionAsync_HotPreview_SharedModel_App_PreviewAppService_
  commentId: M:HotPreview.SharedModel.App.ToolingAppClientConnection.StartConnectionAsync(HotPreview.SharedModel.App.PreviewAppService)
  fullName: HotPreview.SharedModel.App.ToolingAppClientConnection.StartConnectionAsync(HotPreview.SharedModel.App.PreviewAppService)
  nameWithType: ToolingAppClientConnection.StartConnectionAsync(PreviewAppService)
- uid: HotPreview.SharedModel.App.ToolingAppClientConnection.StartConnectionAsync*
  name: StartConnectionAsync
  href: api/HotPreview.SharedModel.App.ToolingAppClientConnection.html#HotPreview_SharedModel_App_ToolingAppClientConnection_StartConnectionAsync_
  commentId: Overload:HotPreview.SharedModel.App.ToolingAppClientConnection.StartConnectionAsync
  isSpec: "True"
  fullName: HotPreview.SharedModel.App.ToolingAppClientConnection.StartConnectionAsync
  nameWithType: ToolingAppClientConnection.StartConnectionAsync
- uid: HotPreview.SharedModel.App.UIComponentPreviewPairReflection
  name: UIComponentPreviewPairReflection
  href: api/HotPreview.SharedModel.App.UIComponentPreviewPairReflection.html
  commentId: T:HotPreview.SharedModel.App.UIComponentPreviewPairReflection
  fullName: HotPreview.SharedModel.App.UIComponentPreviewPairReflection
  nameWithType: UIComponentPreviewPairReflection
- uid: HotPreview.SharedModel.App.UIComponentPreviewPairReflection.#ctor(HotPreview.SharedModel.App.UIComponentReflection,HotPreview.SharedModel.App.PreviewReflection)
  name: UIComponentPreviewPairReflection(UIComponentReflection, PreviewReflection)
  href: api/HotPreview.SharedModel.App.UIComponentPreviewPairReflection.html#HotPreview_SharedModel_App_UIComponentPreviewPairReflection__ctor_HotPreview_SharedModel_App_UIComponentReflection_HotPreview_SharedModel_App_PreviewReflection_
  commentId: M:HotPreview.SharedModel.App.UIComponentPreviewPairReflection.#ctor(HotPreview.SharedModel.App.UIComponentReflection,HotPreview.SharedModel.App.PreviewReflection)
  name.vb: New(UIComponentReflection, PreviewReflection)
  fullName: HotPreview.SharedModel.App.UIComponentPreviewPairReflection.UIComponentPreviewPairReflection(HotPreview.SharedModel.App.UIComponentReflection, HotPreview.SharedModel.App.PreviewReflection)
  fullName.vb: HotPreview.SharedModel.App.UIComponentPreviewPairReflection.New(HotPreview.SharedModel.App.UIComponentReflection, HotPreview.SharedModel.App.PreviewReflection)
  nameWithType: UIComponentPreviewPairReflection.UIComponentPreviewPairReflection(UIComponentReflection, PreviewReflection)
  nameWithType.vb: UIComponentPreviewPairReflection.New(UIComponentReflection, PreviewReflection)
- uid: HotPreview.SharedModel.App.UIComponentPreviewPairReflection.#ctor*
  name: UIComponentPreviewPairReflection
  href: api/HotPreview.SharedModel.App.UIComponentPreviewPairReflection.html#HotPreview_SharedModel_App_UIComponentPreviewPairReflection__ctor_
  commentId: Overload:HotPreview.SharedModel.App.UIComponentPreviewPairReflection.#ctor
  isSpec: "True"
  name.vb: New
  fullName: HotPreview.SharedModel.App.UIComponentPreviewPairReflection.UIComponentPreviewPairReflection
  fullName.vb: HotPreview.SharedModel.App.UIComponentPreviewPairReflection.New
  nameWithType: UIComponentPreviewPairReflection.UIComponentPreviewPairReflection
  nameWithType.vb: UIComponentPreviewPairReflection.New
- uid: HotPreview.SharedModel.App.UIComponentPreviewPairReflection.Preview
  name: Preview
  href: api/HotPreview.SharedModel.App.UIComponentPreviewPairReflection.html#HotPreview_SharedModel_App_UIComponentPreviewPairReflection_Preview
  commentId: P:HotPreview.SharedModel.App.UIComponentPreviewPairReflection.Preview
  fullName: HotPreview.SharedModel.App.UIComponentPreviewPairReflection.Preview
  nameWithType: UIComponentPreviewPairReflection.Preview
- uid: HotPreview.SharedModel.App.UIComponentPreviewPairReflection.Preview*
  name: Preview
  href: api/HotPreview.SharedModel.App.UIComponentPreviewPairReflection.html#HotPreview_SharedModel_App_UIComponentPreviewPairReflection_Preview_
  commentId: Overload:HotPreview.SharedModel.App.UIComponentPreviewPairReflection.Preview
  isSpec: "True"
  fullName: HotPreview.SharedModel.App.UIComponentPreviewPairReflection.Preview
  nameWithType: UIComponentPreviewPairReflection.Preview
- uid: HotPreview.SharedModel.App.UIComponentPreviewPairReflection.UIComponent
  name: UIComponent
  href: api/HotPreview.SharedModel.App.UIComponentPreviewPairReflection.html#HotPreview_SharedModel_App_UIComponentPreviewPairReflection_UIComponent
  commentId: P:HotPreview.SharedModel.App.UIComponentPreviewPairReflection.UIComponent
  fullName: HotPreview.SharedModel.App.UIComponentPreviewPairReflection.UIComponent
  nameWithType: UIComponentPreviewPairReflection.UIComponent
- uid: HotPreview.SharedModel.App.UIComponentPreviewPairReflection.UIComponent*
  name: UIComponent
  href: api/HotPreview.SharedModel.App.UIComponentPreviewPairReflection.html#HotPreview_SharedModel_App_UIComponentPreviewPairReflection_UIComponent_
  commentId: Overload:HotPreview.SharedModel.App.UIComponentPreviewPairReflection.UIComponent
  isSpec: "True"
  fullName: HotPreview.SharedModel.App.UIComponentPreviewPairReflection.UIComponent
  nameWithType: UIComponentPreviewPairReflection.UIComponent
- uid: HotPreview.SharedModel.App.UIComponentReflection
  name: UIComponentReflection
  href: api/HotPreview.SharedModel.App.UIComponentReflection.html
  commentId: T:HotPreview.SharedModel.App.UIComponentReflection
  fullName: HotPreview.SharedModel.App.UIComponentReflection
  nameWithType: UIComponentReflection
- uid: HotPreview.SharedModel.App.UIComponentReflection.#ctor(System.Type,HotPreview.SharedModel.UIComponentKind,System.String,System.Collections.Generic.IReadOnlyList{HotPreview.SharedModel.App.PreviewReflection})
  name: UIComponentReflection(Type, UIComponentKind, string?, IReadOnlyList<PreviewReflection>)
  href: api/HotPreview.SharedModel.App.UIComponentReflection.html#HotPreview_SharedModel_App_UIComponentReflection__ctor_System_Type_HotPreview_SharedModel_UIComponentKind_System_String_System_Collections_Generic_IReadOnlyList_HotPreview_SharedModel_App_PreviewReflection__
  commentId: M:HotPreview.SharedModel.App.UIComponentReflection.#ctor(System.Type,HotPreview.SharedModel.UIComponentKind,System.String,System.Collections.Generic.IReadOnlyList{HotPreview.SharedModel.App.PreviewReflection})
  name.vb: New(Type, UIComponentKind, String, IReadOnlyList(Of PreviewReflection))
  fullName: HotPreview.SharedModel.App.UIComponentReflection.UIComponentReflection(System.Type, HotPreview.SharedModel.UIComponentKind, string?, System.Collections.Generic.IReadOnlyList<HotPreview.SharedModel.App.PreviewReflection>)
  fullName.vb: HotPreview.SharedModel.App.UIComponentReflection.New(System.Type, HotPreview.SharedModel.UIComponentKind, String, System.Collections.Generic.IReadOnlyList(Of HotPreview.SharedModel.App.PreviewReflection))
  nameWithType: UIComponentReflection.UIComponentReflection(Type, UIComponentKind, string?, IReadOnlyList<PreviewReflection>)
  nameWithType.vb: UIComponentReflection.New(Type, UIComponentKind, String, IReadOnlyList(Of PreviewReflection))
- uid: HotPreview.SharedModel.App.UIComponentReflection.#ctor*
  name: UIComponentReflection
  href: api/HotPreview.SharedModel.App.UIComponentReflection.html#HotPreview_SharedModel_App_UIComponentReflection__ctor_
  commentId: Overload:HotPreview.SharedModel.App.UIComponentReflection.#ctor
  isSpec: "True"
  name.vb: New
  fullName: HotPreview.SharedModel.App.UIComponentReflection.UIComponentReflection
  fullName.vb: HotPreview.SharedModel.App.UIComponentReflection.New
  nameWithType: UIComponentReflection.UIComponentReflection
  nameWithType.vb: UIComponentReflection.New
- uid: HotPreview.SharedModel.App.UIComponentReflection.GetUIComponentInfo
  name: GetUIComponentInfo()
  href: api/HotPreview.SharedModel.App.UIComponentReflection.html#HotPreview_SharedModel_App_UIComponentReflection_GetUIComponentInfo
  commentId: M:HotPreview.SharedModel.App.UIComponentReflection.GetUIComponentInfo
  fullName: HotPreview.SharedModel.App.UIComponentReflection.GetUIComponentInfo()
  nameWithType: UIComponentReflection.GetUIComponentInfo()
- uid: HotPreview.SharedModel.App.UIComponentReflection.GetUIComponentInfo*
  name: GetUIComponentInfo
  href: api/HotPreview.SharedModel.App.UIComponentReflection.html#HotPreview_SharedModel_App_UIComponentReflection_GetUIComponentInfo_
  commentId: Overload:HotPreview.SharedModel.App.UIComponentReflection.GetUIComponentInfo
  isSpec: "True"
  fullName: HotPreview.SharedModel.App.UIComponentReflection.GetUIComponentInfo
  nameWithType: UIComponentReflection.GetUIComponentInfo
- uid: HotPreview.SharedModel.App.UIComponentReflection.Name
  name: Name
  href: api/HotPreview.SharedModel.App.UIComponentReflection.html#HotPreview_SharedModel_App_UIComponentReflection_Name
  commentId: P:HotPreview.SharedModel.App.UIComponentReflection.Name
  fullName: HotPreview.SharedModel.App.UIComponentReflection.Name
  nameWithType: UIComponentReflection.Name
- uid: HotPreview.SharedModel.App.UIComponentReflection.Name*
  name: Name
  href: api/HotPreview.SharedModel.App.UIComponentReflection.html#HotPreview_SharedModel_App_UIComponentReflection_Name_
  commentId: Overload:HotPreview.SharedModel.App.UIComponentReflection.Name
  isSpec: "True"
  fullName: HotPreview.SharedModel.App.UIComponentReflection.Name
  nameWithType: UIComponentReflection.Name
- uid: HotPreview.SharedModel.App.UIComponentReflection.Type
  name: Type
  href: api/HotPreview.SharedModel.App.UIComponentReflection.html#HotPreview_SharedModel_App_UIComponentReflection_Type
  commentId: P:HotPreview.SharedModel.App.UIComponentReflection.Type
  fullName: HotPreview.SharedModel.App.UIComponentReflection.Type
  nameWithType: UIComponentReflection.Type
- uid: HotPreview.SharedModel.App.UIComponentReflection.Type*
  name: Type
  href: api/HotPreview.SharedModel.App.UIComponentReflection.html#HotPreview_SharedModel_App_UIComponentReflection_Type_
  commentId: Overload:HotPreview.SharedModel.App.UIComponentReflection.Type
  isSpec: "True"
  fullName: HotPreview.SharedModel.App.UIComponentReflection.Type
  nameWithType: UIComponentReflection.Type
- uid: HotPreview.SharedModel.App.UIComponentReflection.WithAddedPreview(HotPreview.SharedModel.App.PreviewReflection)
  name: WithAddedPreview(PreviewReflection)
  href: api/HotPreview.SharedModel.App.UIComponentReflection.html#HotPreview_SharedModel_App_UIComponentReflection_WithAddedPreview_HotPreview_SharedModel_App_PreviewReflection_
  commentId: M:HotPreview.SharedModel.App.UIComponentReflection.WithAddedPreview(HotPreview.SharedModel.App.PreviewReflection)
  fullName: HotPreview.SharedModel.App.UIComponentReflection.WithAddedPreview(HotPreview.SharedModel.App.PreviewReflection)
  nameWithType: UIComponentReflection.WithAddedPreview(PreviewReflection)
- uid: HotPreview.SharedModel.App.UIComponentReflection.WithAddedPreview*
  name: WithAddedPreview
  href: api/HotPreview.SharedModel.App.UIComponentReflection.html#HotPreview_SharedModel_App_UIComponentReflection_WithAddedPreview_
  commentId: Overload:HotPreview.SharedModel.App.UIComponentReflection.WithAddedPreview
  isSpec: "True"
  fullName: HotPreview.SharedModel.App.UIComponentReflection.WithAddedPreview
  nameWithType: UIComponentReflection.WithAddedPreview
- uid: HotPreview.SharedModel.IUIComponentExclusionFilter
  name: IUIComponentExclusionFilter
  href: api/HotPreview.SharedModel.IUIComponentExclusionFilter.html
  commentId: T:HotPreview.SharedModel.IUIComponentExclusionFilter
  fullName: HotPreview.SharedModel.IUIComponentExclusionFilter
  nameWithType: IUIComponentExclusionFilter
- uid: HotPreview.SharedModel.IUIComponentExclusionFilter.ExcludeAssembly(System.Reflection.Assembly)
  name: ExcludeAssembly(Assembly)
  href: api/HotPreview.SharedModel.IUIComponentExclusionFilter.html#HotPreview_SharedModel_IUIComponentExclusionFilter_ExcludeAssembly_System_Reflection_Assembly_
  commentId: M:HotPreview.SharedModel.IUIComponentExclusionFilter.ExcludeAssembly(System.Reflection.Assembly)
  fullName: HotPreview.SharedModel.IUIComponentExclusionFilter.ExcludeAssembly(System.Reflection.Assembly)
  nameWithType: IUIComponentExclusionFilter.ExcludeAssembly(Assembly)
- uid: HotPreview.SharedModel.IUIComponentExclusionFilter.ExcludeAssembly*
  name: ExcludeAssembly
  href: api/HotPreview.SharedModel.IUIComponentExclusionFilter.html#HotPreview_SharedModel_IUIComponentExclusionFilter_ExcludeAssembly_
  commentId: Overload:HotPreview.SharedModel.IUIComponentExclusionFilter.ExcludeAssembly
  isSpec: "True"
  fullName: HotPreview.SharedModel.IUIComponentExclusionFilter.ExcludeAssembly
  nameWithType: IUIComponentExclusionFilter.ExcludeAssembly
- uid: HotPreview.SharedModel.IUIComponentExclusionFilter.ExcludeType(System.Type)
  name: ExcludeType(Type)
  href: api/HotPreview.SharedModel.IUIComponentExclusionFilter.html#HotPreview_SharedModel_IUIComponentExclusionFilter_ExcludeType_System_Type_
  commentId: M:HotPreview.SharedModel.IUIComponentExclusionFilter.ExcludeType(System.Type)
  fullName: HotPreview.SharedModel.IUIComponentExclusionFilter.ExcludeType(System.Type)
  nameWithType: IUIComponentExclusionFilter.ExcludeType(Type)
- uid: HotPreview.SharedModel.IUIComponentExclusionFilter.ExcludeType*
  name: ExcludeType
  href: api/HotPreview.SharedModel.IUIComponentExclusionFilter.html#HotPreview_SharedModel_IUIComponentExclusionFilter_ExcludeType_
  commentId: Overload:HotPreview.SharedModel.IUIComponentExclusionFilter.ExcludeType
  isSpec: "True"
  fullName: HotPreview.SharedModel.IUIComponentExclusionFilter.ExcludeType
  nameWithType: IUIComponentExclusionFilter.ExcludeType
- uid: HotPreview.SharedModel.PreviewBase
  name: PreviewBase
  href: api/HotPreview.SharedModel.PreviewBase.html
  commentId: T:HotPreview.SharedModel.PreviewBase
  fullName: HotPreview.SharedModel.PreviewBase
  nameWithType: PreviewBase
- uid: HotPreview.SharedModel.PreviewBase.#ctor(System.String)
  name: PreviewBase(string?)
  href: api/HotPreview.SharedModel.PreviewBase.html#HotPreview_SharedModel_PreviewBase__ctor_System_String_
  commentId: M:HotPreview.SharedModel.PreviewBase.#ctor(System.String)
  name.vb: New(String)
  fullName: HotPreview.SharedModel.PreviewBase.PreviewBase(string?)
  fullName.vb: HotPreview.SharedModel.PreviewBase.New(String)
  nameWithType: PreviewBase.PreviewBase(string?)
  nameWithType.vb: PreviewBase.New(String)
- uid: HotPreview.SharedModel.PreviewBase.#ctor*
  name: PreviewBase
  href: api/HotPreview.SharedModel.PreviewBase.html#HotPreview_SharedModel_PreviewBase__ctor_
  commentId: Overload:HotPreview.SharedModel.PreviewBase.#ctor
  isSpec: "True"
  name.vb: New
  fullName: HotPreview.SharedModel.PreviewBase.PreviewBase
  fullName.vb: HotPreview.SharedModel.PreviewBase.New
  nameWithType: PreviewBase.PreviewBase
  nameWithType.vb: PreviewBase.New
- uid: HotPreview.SharedModel.PreviewBase.DisplayName
  name: DisplayName
  href: api/HotPreview.SharedModel.PreviewBase.html#HotPreview_SharedModel_PreviewBase_DisplayName
  commentId: P:HotPreview.SharedModel.PreviewBase.DisplayName
  fullName: HotPreview.SharedModel.PreviewBase.DisplayName
  nameWithType: PreviewBase.DisplayName
- uid: HotPreview.SharedModel.PreviewBase.DisplayName*
  name: DisplayName
  href: api/HotPreview.SharedModel.PreviewBase.html#HotPreview_SharedModel_PreviewBase_DisplayName_
  commentId: Overload:HotPreview.SharedModel.PreviewBase.DisplayName
  isSpec: "True"
  fullName: HotPreview.SharedModel.PreviewBase.DisplayName
  nameWithType: PreviewBase.DisplayName
- uid: HotPreview.SharedModel.PreviewBase.DisplayNameOverride
  name: DisplayNameOverride
  href: api/HotPreview.SharedModel.PreviewBase.html#HotPreview_SharedModel_PreviewBase_DisplayNameOverride
  commentId: P:HotPreview.SharedModel.PreviewBase.DisplayNameOverride
  fullName: HotPreview.SharedModel.PreviewBase.DisplayNameOverride
  nameWithType: PreviewBase.DisplayNameOverride
- uid: HotPreview.SharedModel.PreviewBase.DisplayNameOverride*
  name: DisplayNameOverride
  href: api/HotPreview.SharedModel.PreviewBase.html#HotPreview_SharedModel_PreviewBase_DisplayNameOverride_
  commentId: Overload:HotPreview.SharedModel.PreviewBase.DisplayNameOverride
  isSpec: "True"
  fullName: HotPreview.SharedModel.PreviewBase.DisplayNameOverride
  nameWithType: PreviewBase.DisplayNameOverride
- uid: HotPreview.SharedModel.PreviewBase.IsAutoGenerated
  name: IsAutoGenerated
  href: api/HotPreview.SharedModel.PreviewBase.html#HotPreview_SharedModel_PreviewBase_IsAutoGenerated
  commentId: P:HotPreview.SharedModel.PreviewBase.IsAutoGenerated
  fullName: HotPreview.SharedModel.PreviewBase.IsAutoGenerated
  nameWithType: PreviewBase.IsAutoGenerated
- uid: HotPreview.SharedModel.PreviewBase.IsAutoGenerated*
  name: IsAutoGenerated
  href: api/HotPreview.SharedModel.PreviewBase.html#HotPreview_SharedModel_PreviewBase_IsAutoGenerated_
  commentId: Overload:HotPreview.SharedModel.PreviewBase.IsAutoGenerated
  isSpec: "True"
  fullName: HotPreview.SharedModel.PreviewBase.IsAutoGenerated
  nameWithType: PreviewBase.IsAutoGenerated
- uid: HotPreview.SharedModel.PreviewBase.Name
  name: Name
  href: api/HotPreview.SharedModel.PreviewBase.html#HotPreview_SharedModel_PreviewBase_Name
  commentId: P:HotPreview.SharedModel.PreviewBase.Name
  fullName: HotPreview.SharedModel.PreviewBase.Name
  nameWithType: PreviewBase.Name
- uid: HotPreview.SharedModel.PreviewBase.Name*
  name: Name
  href: api/HotPreview.SharedModel.PreviewBase.html#HotPreview_SharedModel_PreviewBase_Name_
  commentId: Overload:HotPreview.SharedModel.PreviewBase.Name
  isSpec: "True"
  fullName: HotPreview.SharedModel.PreviewBase.Name
  nameWithType: PreviewBase.Name
- uid: HotPreview.SharedModel.PreviewCommandBase
  name: PreviewCommandBase
  href: api/HotPreview.SharedModel.PreviewCommandBase.html
  commentId: T:HotPreview.SharedModel.PreviewCommandBase
  fullName: HotPreview.SharedModel.PreviewCommandBase
  nameWithType: PreviewCommandBase
- uid: HotPreview.SharedModel.PreviewCommandBase.#ctor(System.String)
  name: PreviewCommandBase(string?)
  href: api/HotPreview.SharedModel.PreviewCommandBase.html#HotPreview_SharedModel_PreviewCommandBase__ctor_System_String_
  commentId: M:HotPreview.SharedModel.PreviewCommandBase.#ctor(System.String)
  name.vb: New(String)
  fullName: HotPreview.SharedModel.PreviewCommandBase.PreviewCommandBase(string?)
  fullName.vb: HotPreview.SharedModel.PreviewCommandBase.New(String)
  nameWithType: PreviewCommandBase.PreviewCommandBase(string?)
  nameWithType.vb: PreviewCommandBase.New(String)
- uid: HotPreview.SharedModel.PreviewCommandBase.#ctor*
  name: PreviewCommandBase
  href: api/HotPreview.SharedModel.PreviewCommandBase.html#HotPreview_SharedModel_PreviewCommandBase__ctor_
  commentId: Overload:HotPreview.SharedModel.PreviewCommandBase.#ctor
  isSpec: "True"
  name.vb: New
  fullName: HotPreview.SharedModel.PreviewCommandBase.PreviewCommandBase
  fullName.vb: HotPreview.SharedModel.PreviewCommandBase.New
  nameWithType: PreviewCommandBase.PreviewCommandBase
  nameWithType.vb: PreviewCommandBase.New
- uid: HotPreview.SharedModel.PreviewCommandBase.DisplayName
  name: DisplayName
  href: api/HotPreview.SharedModel.PreviewCommandBase.html#HotPreview_SharedModel_PreviewCommandBase_DisplayName
  commentId: P:HotPreview.SharedModel.PreviewCommandBase.DisplayName
  fullName: HotPreview.SharedModel.PreviewCommandBase.DisplayName
  nameWithType: PreviewCommandBase.DisplayName
- uid: HotPreview.SharedModel.PreviewCommandBase.DisplayName*
  name: DisplayName
  href: api/HotPreview.SharedModel.PreviewCommandBase.html#HotPreview_SharedModel_PreviewCommandBase_DisplayName_
  commentId: Overload:HotPreview.SharedModel.PreviewCommandBase.DisplayName
  isSpec: "True"
  fullName: HotPreview.SharedModel.PreviewCommandBase.DisplayName
  nameWithType: PreviewCommandBase.DisplayName
- uid: HotPreview.SharedModel.PreviewCommandBase.DisplayNameOverride
  name: DisplayNameOverride
  href: api/HotPreview.SharedModel.PreviewCommandBase.html#HotPreview_SharedModel_PreviewCommandBase_DisplayNameOverride
  commentId: P:HotPreview.SharedModel.PreviewCommandBase.DisplayNameOverride
  fullName: HotPreview.SharedModel.PreviewCommandBase.DisplayNameOverride
  nameWithType: PreviewCommandBase.DisplayNameOverride
- uid: HotPreview.SharedModel.PreviewCommandBase.DisplayNameOverride*
  name: DisplayNameOverride
  href: api/HotPreview.SharedModel.PreviewCommandBase.html#HotPreview_SharedModel_PreviewCommandBase_DisplayNameOverride_
  commentId: Overload:HotPreview.SharedModel.PreviewCommandBase.DisplayNameOverride
  isSpec: "True"
  fullName: HotPreview.SharedModel.PreviewCommandBase.DisplayNameOverride
  nameWithType: PreviewCommandBase.DisplayNameOverride
- uid: HotPreview.SharedModel.PreviewCommandBase.Name
  name: Name
  href: api/HotPreview.SharedModel.PreviewCommandBase.html#HotPreview_SharedModel_PreviewCommandBase_Name
  commentId: P:HotPreview.SharedModel.PreviewCommandBase.Name
  fullName: HotPreview.SharedModel.PreviewCommandBase.Name
  nameWithType: PreviewCommandBase.Name
- uid: HotPreview.SharedModel.PreviewCommandBase.Name*
  name: Name
  href: api/HotPreview.SharedModel.PreviewCommandBase.html#HotPreview_SharedModel_PreviewCommandBase_Name_
  commentId: Overload:HotPreview.SharedModel.PreviewCommandBase.Name
  isSpec: "True"
  fullName: HotPreview.SharedModel.PreviewCommandBase.Name
  nameWithType: PreviewCommandBase.Name
- uid: HotPreview.SharedModel.PreviewNotFoundException
  name: PreviewNotFoundException
  href: api/HotPreview.SharedModel.PreviewNotFoundException.html
  commentId: T:HotPreview.SharedModel.PreviewNotFoundException
  fullName: HotPreview.SharedModel.PreviewNotFoundException
  nameWithType: PreviewNotFoundException
- uid: HotPreview.SharedModel.PreviewNotFoundException.#ctor(System.String)
  name: PreviewNotFoundException(string)
  href: api/HotPreview.SharedModel.PreviewNotFoundException.html#HotPreview_SharedModel_PreviewNotFoundException__ctor_System_String_
  commentId: M:HotPreview.SharedModel.PreviewNotFoundException.#ctor(System.String)
  name.vb: New(String)
  fullName: HotPreview.SharedModel.PreviewNotFoundException.PreviewNotFoundException(string)
  fullName.vb: HotPreview.SharedModel.PreviewNotFoundException.New(String)
  nameWithType: PreviewNotFoundException.PreviewNotFoundException(string)
  nameWithType.vb: PreviewNotFoundException.New(String)
- uid: HotPreview.SharedModel.PreviewNotFoundException.#ctor*
  name: PreviewNotFoundException
  href: api/HotPreview.SharedModel.PreviewNotFoundException.html#HotPreview_SharedModel_PreviewNotFoundException__ctor_
  commentId: Overload:HotPreview.SharedModel.PreviewNotFoundException.#ctor
  isSpec: "True"
  name.vb: New
  fullName: HotPreview.SharedModel.PreviewNotFoundException.PreviewNotFoundException
  fullName.vb: HotPreview.SharedModel.PreviewNotFoundException.New
  nameWithType: PreviewNotFoundException.PreviewNotFoundException
  nameWithType.vb: PreviewNotFoundException.New
- uid: HotPreview.SharedModel.PreviewsManagerBase`3
  name: PreviewsManagerBase<TUIComponent, TPreview, TCommand>
  href: api/HotPreview.SharedModel.PreviewsManagerBase-3.html
  commentId: T:HotPreview.SharedModel.PreviewsManagerBase`3
  name.vb: PreviewsManagerBase(Of TUIComponent, TPreview, TCommand)
  fullName: HotPreview.SharedModel.PreviewsManagerBase<TUIComponent, TPreview, TCommand>
  fullName.vb: HotPreview.SharedModel.PreviewsManagerBase(Of TUIComponent, TPreview, TCommand)
  nameWithType: PreviewsManagerBase<TUIComponent, TPreview, TCommand>
  nameWithType.vb: PreviewsManagerBase(Of TUIComponent, TPreview, TCommand)
- uid: HotPreview.SharedModel.PreviewsManagerBase`3.#ctor(System.Collections.Generic.IReadOnlyDictionary{System.String,`0},System.Collections.Generic.IReadOnlyDictionary{System.String,HotPreview.SharedModel.UIComponentCategory},System.Collections.Generic.IReadOnlyDictionary{System.String,`2})
  name: PreviewsManagerBase(IReadOnlyDictionary<string, TUIComponent>, IReadOnlyDictionary<string, UIComponentCategory>, IReadOnlyDictionary<string, TCommand>)
  href: api/HotPreview.SharedModel.PreviewsManagerBase-3.html#HotPreview_SharedModel_PreviewsManagerBase_3__ctor_System_Collections_Generic_IReadOnlyDictionary_System_String__0__System_Collections_Generic_IReadOnlyDictionary_System_String_HotPreview_SharedModel_UIComponentCategory__System_Collections_Generic_IReadOnlyDictionary_System_String__2__
  commentId: M:HotPreview.SharedModel.PreviewsManagerBase`3.#ctor(System.Collections.Generic.IReadOnlyDictionary{System.String,`0},System.Collections.Generic.IReadOnlyDictionary{System.String,HotPreview.SharedModel.UIComponentCategory},System.Collections.Generic.IReadOnlyDictionary{System.String,`2})
  name.vb: New(IReadOnlyDictionary(Of String, TUIComponent), IReadOnlyDictionary(Of String, UIComponentCategory), IReadOnlyDictionary(Of String, TCommand))
  fullName: HotPreview.SharedModel.PreviewsManagerBase<TUIComponent, TPreview, TCommand>.PreviewsManagerBase(System.Collections.Generic.IReadOnlyDictionary<string, TUIComponent>, System.Collections.Generic.IReadOnlyDictionary<string, HotPreview.SharedModel.UIComponentCategory>, System.Collections.Generic.IReadOnlyDictionary<string, TCommand>)
  fullName.vb: HotPreview.SharedModel.PreviewsManagerBase(Of TUIComponent, TPreview, TCommand).New(System.Collections.Generic.IReadOnlyDictionary(Of String, TUIComponent), System.Collections.Generic.IReadOnlyDictionary(Of String, HotPreview.SharedModel.UIComponentCategory), System.Collections.Generic.IReadOnlyDictionary(Of String, TCommand))
  nameWithType: PreviewsManagerBase<TUIComponent, TPreview, TCommand>.PreviewsManagerBase(IReadOnlyDictionary<string, TUIComponent>, IReadOnlyDictionary<string, UIComponentCategory>, IReadOnlyDictionary<string, TCommand>)
  nameWithType.vb: PreviewsManagerBase(Of TUIComponent, TPreview, TCommand).New(IReadOnlyDictionary(Of String, TUIComponent), IReadOnlyDictionary(Of String, UIComponentCategory), IReadOnlyDictionary(Of String, TCommand))
- uid: HotPreview.SharedModel.PreviewsManagerBase`3.#ctor*
  name: PreviewsManagerBase
  href: api/HotPreview.SharedModel.PreviewsManagerBase-3.html#HotPreview_SharedModel_PreviewsManagerBase_3__ctor_
  commentId: Overload:HotPreview.SharedModel.PreviewsManagerBase`3.#ctor
  isSpec: "True"
  name.vb: New
  fullName: HotPreview.SharedModel.PreviewsManagerBase<TUIComponent, TPreview, TCommand>.PreviewsManagerBase
  fullName.vb: HotPreview.SharedModel.PreviewsManagerBase(Of TUIComponent, TPreview, TCommand).New
  nameWithType: PreviewsManagerBase<TUIComponent, TPreview, TCommand>.PreviewsManagerBase
  nameWithType.vb: PreviewsManagerBase(Of TUIComponent, TPreview, TCommand).New
- uid: HotPreview.SharedModel.PreviewsManagerBase`3.Categories
  name: Categories
  href: api/HotPreview.SharedModel.PreviewsManagerBase-3.html#HotPreview_SharedModel_PreviewsManagerBase_3_Categories
  commentId: P:HotPreview.SharedModel.PreviewsManagerBase`3.Categories
  fullName: HotPreview.SharedModel.PreviewsManagerBase<TUIComponent, TPreview, TCommand>.Categories
  fullName.vb: HotPreview.SharedModel.PreviewsManagerBase(Of TUIComponent, TPreview, TCommand).Categories
  nameWithType: PreviewsManagerBase<TUIComponent, TPreview, TCommand>.Categories
  nameWithType.vb: PreviewsManagerBase(Of TUIComponent, TPreview, TCommand).Categories
- uid: HotPreview.SharedModel.PreviewsManagerBase`3.Categories*
  name: Categories
  href: api/HotPreview.SharedModel.PreviewsManagerBase-3.html#HotPreview_SharedModel_PreviewsManagerBase_3_Categories_
  commentId: Overload:HotPreview.SharedModel.PreviewsManagerBase`3.Categories
  isSpec: "True"
  fullName: HotPreview.SharedModel.PreviewsManagerBase<TUIComponent, TPreview, TCommand>.Categories
  fullName.vb: HotPreview.SharedModel.PreviewsManagerBase(Of TUIComponent, TPreview, TCommand).Categories
  nameWithType: PreviewsManagerBase<TUIComponent, TPreview, TCommand>.Categories
  nameWithType.vb: PreviewsManagerBase(Of TUIComponent, TPreview, TCommand).Categories
- uid: HotPreview.SharedModel.PreviewsManagerBase`3.CategorizedUIComponents
  name: CategorizedUIComponents
  href: api/HotPreview.SharedModel.PreviewsManagerBase-3.html#HotPreview_SharedModel_PreviewsManagerBase_3_CategorizedUIComponents
  commentId: P:HotPreview.SharedModel.PreviewsManagerBase`3.CategorizedUIComponents
  fullName: HotPreview.SharedModel.PreviewsManagerBase<TUIComponent, TPreview, TCommand>.CategorizedUIComponents
  fullName.vb: HotPreview.SharedModel.PreviewsManagerBase(Of TUIComponent, TPreview, TCommand).CategorizedUIComponents
  nameWithType: PreviewsManagerBase<TUIComponent, TPreview, TCommand>.CategorizedUIComponents
  nameWithType.vb: PreviewsManagerBase(Of TUIComponent, TPreview, TCommand).CategorizedUIComponents
- uid: HotPreview.SharedModel.PreviewsManagerBase`3.CategorizedUIComponents*
  name: CategorizedUIComponents
  href: api/HotPreview.SharedModel.PreviewsManagerBase-3.html#HotPreview_SharedModel_PreviewsManagerBase_3_CategorizedUIComponents_
  commentId: Overload:HotPreview.SharedModel.PreviewsManagerBase`3.CategorizedUIComponents
  isSpec: "True"
  fullName: HotPreview.SharedModel.PreviewsManagerBase<TUIComponent, TPreview, TCommand>.CategorizedUIComponents
  fullName.vb: HotPreview.SharedModel.PreviewsManagerBase(Of TUIComponent, TPreview, TCommand).CategorizedUIComponents
  nameWithType: PreviewsManagerBase<TUIComponent, TPreview, TCommand>.CategorizedUIComponents
  nameWithType.vb: PreviewsManagerBase(Of TUIComponent, TPreview, TCommand).CategorizedUIComponents
- uid: HotPreview.SharedModel.PreviewsManagerBase`3.Commands
  name: Commands
  href: api/HotPreview.SharedModel.PreviewsManagerBase-3.html#HotPreview_SharedModel_PreviewsManagerBase_3_Commands
  commentId: P:HotPreview.SharedModel.PreviewsManagerBase`3.Commands
  fullName: HotPreview.SharedModel.PreviewsManagerBase<TUIComponent, TPreview, TCommand>.Commands
  fullName.vb: HotPreview.SharedModel.PreviewsManagerBase(Of TUIComponent, TPreview, TCommand).Commands
  nameWithType: PreviewsManagerBase<TUIComponent, TPreview, TCommand>.Commands
  nameWithType.vb: PreviewsManagerBase(Of TUIComponent, TPreview, TCommand).Commands
- uid: HotPreview.SharedModel.PreviewsManagerBase`3.Commands*
  name: Commands
  href: api/HotPreview.SharedModel.PreviewsManagerBase-3.html#HotPreview_SharedModel_PreviewsManagerBase_3_Commands_
  commentId: Overload:HotPreview.SharedModel.PreviewsManagerBase`3.Commands
  isSpec: "True"
  fullName: HotPreview.SharedModel.PreviewsManagerBase<TUIComponent, TPreview, TCommand>.Commands
  fullName.vb: HotPreview.SharedModel.PreviewsManagerBase(Of TUIComponent, TPreview, TCommand).Commands
  nameWithType: PreviewsManagerBase<TUIComponent, TPreview, TCommand>.Commands
  nameWithType.vb: PreviewsManagerBase(Of TUIComponent, TPreview, TCommand).Commands
- uid: HotPreview.SharedModel.PreviewsManagerBase`3.GetCommand(System.String)
  name: GetCommand(string)
  href: api/HotPreview.SharedModel.PreviewsManagerBase-3.html#HotPreview_SharedModel_PreviewsManagerBase_3_GetCommand_System_String_
  commentId: M:HotPreview.SharedModel.PreviewsManagerBase`3.GetCommand(System.String)
  name.vb: GetCommand(String)
  fullName: HotPreview.SharedModel.PreviewsManagerBase<TUIComponent, TPreview, TCommand>.GetCommand(string)
  fullName.vb: HotPreview.SharedModel.PreviewsManagerBase(Of TUIComponent, TPreview, TCommand).GetCommand(String)
  nameWithType: PreviewsManagerBase<TUIComponent, TPreview, TCommand>.GetCommand(string)
  nameWithType.vb: PreviewsManagerBase(Of TUIComponent, TPreview, TCommand).GetCommand(String)
- uid: HotPreview.SharedModel.PreviewsManagerBase`3.GetCommand*
  name: GetCommand
  href: api/HotPreview.SharedModel.PreviewsManagerBase-3.html#HotPreview_SharedModel_PreviewsManagerBase_3_GetCommand_
  commentId: Overload:HotPreview.SharedModel.PreviewsManagerBase`3.GetCommand
  isSpec: "True"
  fullName: HotPreview.SharedModel.PreviewsManagerBase<TUIComponent, TPreview, TCommand>.GetCommand
  fullName.vb: HotPreview.SharedModel.PreviewsManagerBase(Of TUIComponent, TPreview, TCommand).GetCommand
  nameWithType: PreviewsManagerBase<TUIComponent, TPreview, TCommand>.GetCommand
  nameWithType.vb: PreviewsManagerBase(Of TUIComponent, TPreview, TCommand).GetCommand
- uid: HotPreview.SharedModel.PreviewsManagerBase`3.GetUIComponent(System.String)
  name: GetUIComponent(string)
  href: api/HotPreview.SharedModel.PreviewsManagerBase-3.html#HotPreview_SharedModel_PreviewsManagerBase_3_GetUIComponent_System_String_
  commentId: M:HotPreview.SharedModel.PreviewsManagerBase`3.GetUIComponent(System.String)
  name.vb: GetUIComponent(String)
  fullName: HotPreview.SharedModel.PreviewsManagerBase<TUIComponent, TPreview, TCommand>.GetUIComponent(string)
  fullName.vb: HotPreview.SharedModel.PreviewsManagerBase(Of TUIComponent, TPreview, TCommand).GetUIComponent(String)
  nameWithType: PreviewsManagerBase<TUIComponent, TPreview, TCommand>.GetUIComponent(string)
  nameWithType.vb: PreviewsManagerBase(Of TUIComponent, TPreview, TCommand).GetUIComponent(String)
- uid: HotPreview.SharedModel.PreviewsManagerBase`3.GetUIComponent*
  name: GetUIComponent
  href: api/HotPreview.SharedModel.PreviewsManagerBase-3.html#HotPreview_SharedModel_PreviewsManagerBase_3_GetUIComponent_
  commentId: Overload:HotPreview.SharedModel.PreviewsManagerBase`3.GetUIComponent
  isSpec: "True"
  fullName: HotPreview.SharedModel.PreviewsManagerBase<TUIComponent, TPreview, TCommand>.GetUIComponent
  fullName.vb: HotPreview.SharedModel.PreviewsManagerBase(Of TUIComponent, TPreview, TCommand).GetUIComponent
  nameWithType: PreviewsManagerBase<TUIComponent, TPreview, TCommand>.GetUIComponent
  nameWithType.vb: PreviewsManagerBase(Of TUIComponent, TPreview, TCommand).GetUIComponent
- uid: HotPreview.SharedModel.PreviewsManagerBase`3.HasCategories
  name: HasCategories
  href: api/HotPreview.SharedModel.PreviewsManagerBase-3.html#HotPreview_SharedModel_PreviewsManagerBase_3_HasCategories
  commentId: P:HotPreview.SharedModel.PreviewsManagerBase`3.HasCategories
  fullName: HotPreview.SharedModel.PreviewsManagerBase<TUIComponent, TPreview, TCommand>.HasCategories
  fullName.vb: HotPreview.SharedModel.PreviewsManagerBase(Of TUIComponent, TPreview, TCommand).HasCategories
  nameWithType: PreviewsManagerBase<TUIComponent, TPreview, TCommand>.HasCategories
  nameWithType.vb: PreviewsManagerBase(Of TUIComponent, TPreview, TCommand).HasCategories
- uid: HotPreview.SharedModel.PreviewsManagerBase`3.HasCategories*
  name: HasCategories
  href: api/HotPreview.SharedModel.PreviewsManagerBase-3.html#HotPreview_SharedModel_PreviewsManagerBase_3_HasCategories_
  commentId: Overload:HotPreview.SharedModel.PreviewsManagerBase`3.HasCategories
  isSpec: "True"
  fullName: HotPreview.SharedModel.PreviewsManagerBase<TUIComponent, TPreview, TCommand>.HasCategories
  fullName.vb: HotPreview.SharedModel.PreviewsManagerBase(Of TUIComponent, TPreview, TCommand).HasCategories
  nameWithType: PreviewsManagerBase<TUIComponent, TPreview, TCommand>.HasCategories
  nameWithType.vb: PreviewsManagerBase(Of TUIComponent, TPreview, TCommand).HasCategories
- uid: HotPreview.SharedModel.PreviewsManagerBase`3.HasPreview(System.String,System.String)
  name: HasPreview(string, string)
  href: api/HotPreview.SharedModel.PreviewsManagerBase-3.html#HotPreview_SharedModel_PreviewsManagerBase_3_HasPreview_System_String_System_String_
  commentId: M:HotPreview.SharedModel.PreviewsManagerBase`3.HasPreview(System.String,System.String)
  name.vb: HasPreview(String, String)
  fullName: HotPreview.SharedModel.PreviewsManagerBase<TUIComponent, TPreview, TCommand>.HasPreview(string, string)
  fullName.vb: HotPreview.SharedModel.PreviewsManagerBase(Of TUIComponent, TPreview, TCommand).HasPreview(String, String)
  nameWithType: PreviewsManagerBase<TUIComponent, TPreview, TCommand>.HasPreview(string, string)
  nameWithType.vb: PreviewsManagerBase(Of TUIComponent, TPreview, TCommand).HasPreview(String, String)
- uid: HotPreview.SharedModel.PreviewsManagerBase`3.HasPreview*
  name: HasPreview
  href: api/HotPreview.SharedModel.PreviewsManagerBase-3.html#HotPreview_SharedModel_PreviewsManagerBase_3_HasPreview_
  commentId: Overload:HotPreview.SharedModel.PreviewsManagerBase`3.HasPreview
  isSpec: "True"
  fullName: HotPreview.SharedModel.PreviewsManagerBase<TUIComponent, TPreview, TCommand>.HasPreview
  fullName.vb: HotPreview.SharedModel.PreviewsManagerBase(Of TUIComponent, TPreview, TCommand).HasPreview
  nameWithType: PreviewsManagerBase<TUIComponent, TPreview, TCommand>.HasPreview
  nameWithType.vb: PreviewsManagerBase(Of TUIComponent, TPreview, TCommand).HasPreview
- uid: HotPreview.SharedModel.PreviewsManagerBase`3.UIComponents
  name: UIComponents
  href: api/HotPreview.SharedModel.PreviewsManagerBase-3.html#HotPreview_SharedModel_PreviewsManagerBase_3_UIComponents
  commentId: P:HotPreview.SharedModel.PreviewsManagerBase`3.UIComponents
  fullName: HotPreview.SharedModel.PreviewsManagerBase<TUIComponent, TPreview, TCommand>.UIComponents
  fullName.vb: HotPreview.SharedModel.PreviewsManagerBase(Of TUIComponent, TPreview, TCommand).UIComponents
  nameWithType: PreviewsManagerBase<TUIComponent, TPreview, TCommand>.UIComponents
  nameWithType.vb: PreviewsManagerBase(Of TUIComponent, TPreview, TCommand).UIComponents
- uid: HotPreview.SharedModel.PreviewsManagerBase`3.UIComponents*
  name: UIComponents
  href: api/HotPreview.SharedModel.PreviewsManagerBase-3.html#HotPreview_SharedModel_PreviewsManagerBase_3_UIComponents_
  commentId: Overload:HotPreview.SharedModel.PreviewsManagerBase`3.UIComponents
  isSpec: "True"
  fullName: HotPreview.SharedModel.PreviewsManagerBase<TUIComponent, TPreview, TCommand>.UIComponents
  fullName.vb: HotPreview.SharedModel.PreviewsManagerBase(Of TUIComponent, TPreview, TCommand).UIComponents
  nameWithType: PreviewsManagerBase<TUIComponent, TPreview, TCommand>.UIComponents
  nameWithType.vb: PreviewsManagerBase(Of TUIComponent, TPreview, TCommand).UIComponents
- uid: HotPreview.SharedModel.PreviewsManagerBuilderBase`3
  name: PreviewsManagerBuilderBase<TUIComponent, TPreview, TCommand>
  href: api/HotPreview.SharedModel.PreviewsManagerBuilderBase-3.html
  commentId: T:HotPreview.SharedModel.PreviewsManagerBuilderBase`3
  name.vb: PreviewsManagerBuilderBase(Of TUIComponent, TPreview, TCommand)
  fullName: HotPreview.SharedModel.PreviewsManagerBuilderBase<TUIComponent, TPreview, TCommand>
  fullName.vb: HotPreview.SharedModel.PreviewsManagerBuilderBase(Of TUIComponent, TPreview, TCommand)
  nameWithType: PreviewsManagerBuilderBase<TUIComponent, TPreview, TCommand>
  nameWithType.vb: PreviewsManagerBuilderBase(Of TUIComponent, TPreview, TCommand)
- uid: HotPreview.SharedModel.PreviewsManagerBuilderBase`3.#ctor
  name: PreviewsManagerBuilderBase()
  href: api/HotPreview.SharedModel.PreviewsManagerBuilderBase-3.html#HotPreview_SharedModel_PreviewsManagerBuilderBase_3__ctor
  commentId: M:HotPreview.SharedModel.PreviewsManagerBuilderBase`3.#ctor
  name.vb: New()
  fullName: HotPreview.SharedModel.PreviewsManagerBuilderBase<TUIComponent, TPreview, TCommand>.PreviewsManagerBuilderBase()
  fullName.vb: HotPreview.SharedModel.PreviewsManagerBuilderBase(Of TUIComponent, TPreview, TCommand).New()
  nameWithType: PreviewsManagerBuilderBase<TUIComponent, TPreview, TCommand>.PreviewsManagerBuilderBase()
  nameWithType.vb: PreviewsManagerBuilderBase(Of TUIComponent, TPreview, TCommand).New()
- uid: HotPreview.SharedModel.PreviewsManagerBuilderBase`3.#ctor*
  name: PreviewsManagerBuilderBase
  href: api/HotPreview.SharedModel.PreviewsManagerBuilderBase-3.html#HotPreview_SharedModel_PreviewsManagerBuilderBase_3__ctor_
  commentId: Overload:HotPreview.SharedModel.PreviewsManagerBuilderBase`3.#ctor
  isSpec: "True"
  name.vb: New
  fullName: HotPreview.SharedModel.PreviewsManagerBuilderBase<TUIComponent, TPreview, TCommand>.PreviewsManagerBuilderBase
  fullName.vb: HotPreview.SharedModel.PreviewsManagerBuilderBase(Of TUIComponent, TPreview, TCommand).New
  nameWithType: PreviewsManagerBuilderBase<TUIComponent, TPreview, TCommand>.PreviewsManagerBuilderBase
  nameWithType.vb: PreviewsManagerBuilderBase(Of TUIComponent, TPreview, TCommand).New
- uid: HotPreview.SharedModel.PreviewsManagerBuilderBase`3.AddCategory(HotPreview.SharedModel.UIComponentCategory)
  name: AddCategory(UIComponentCategory)
  href: api/HotPreview.SharedModel.PreviewsManagerBuilderBase-3.html#HotPreview_SharedModel_PreviewsManagerBuilderBase_3_AddCategory_HotPreview_SharedModel_UIComponentCategory_
  commentId: M:HotPreview.SharedModel.PreviewsManagerBuilderBase`3.AddCategory(HotPreview.SharedModel.UIComponentCategory)
  fullName: HotPreview.SharedModel.PreviewsManagerBuilderBase<TUIComponent, TPreview, TCommand>.AddCategory(HotPreview.SharedModel.UIComponentCategory)
  fullName.vb: HotPreview.SharedModel.PreviewsManagerBuilderBase(Of TUIComponent, TPreview, TCommand).AddCategory(HotPreview.SharedModel.UIComponentCategory)
  nameWithType: PreviewsManagerBuilderBase<TUIComponent, TPreview, TCommand>.AddCategory(UIComponentCategory)
  nameWithType.vb: PreviewsManagerBuilderBase(Of TUIComponent, TPreview, TCommand).AddCategory(UIComponentCategory)
- uid: HotPreview.SharedModel.PreviewsManagerBuilderBase`3.AddCategory*
  name: AddCategory
  href: api/HotPreview.SharedModel.PreviewsManagerBuilderBase-3.html#HotPreview_SharedModel_PreviewsManagerBuilderBase_3_AddCategory_
  commentId: Overload:HotPreview.SharedModel.PreviewsManagerBuilderBase`3.AddCategory
  isSpec: "True"
  fullName: HotPreview.SharedModel.PreviewsManagerBuilderBase<TUIComponent, TPreview, TCommand>.AddCategory
  fullName.vb: HotPreview.SharedModel.PreviewsManagerBuilderBase(Of TUIComponent, TPreview, TCommand).AddCategory
  nameWithType: PreviewsManagerBuilderBase<TUIComponent, TPreview, TCommand>.AddCategory
  nameWithType.vb: PreviewsManagerBuilderBase(Of TUIComponent, TPreview, TCommand).AddCategory
- uid: HotPreview.SharedModel.PreviewsManagerBuilderBase`3.AddCommand(`2)
  name: AddCommand(TCommand)
  href: api/HotPreview.SharedModel.PreviewsManagerBuilderBase-3.html#HotPreview_SharedModel_PreviewsManagerBuilderBase_3_AddCommand__2_
  commentId: M:HotPreview.SharedModel.PreviewsManagerBuilderBase`3.AddCommand(`2)
  fullName: HotPreview.SharedModel.PreviewsManagerBuilderBase<TUIComponent, TPreview, TCommand>.AddCommand(TCommand)
  fullName.vb: HotPreview.SharedModel.PreviewsManagerBuilderBase(Of TUIComponent, TPreview, TCommand).AddCommand(TCommand)
  nameWithType: PreviewsManagerBuilderBase<TUIComponent, TPreview, TCommand>.AddCommand(TCommand)
  nameWithType.vb: PreviewsManagerBuilderBase(Of TUIComponent, TPreview, TCommand).AddCommand(TCommand)
- uid: HotPreview.SharedModel.PreviewsManagerBuilderBase`3.AddCommand*
  name: AddCommand
  href: api/HotPreview.SharedModel.PreviewsManagerBuilderBase-3.html#HotPreview_SharedModel_PreviewsManagerBuilderBase_3_AddCommand_
  commentId: Overload:HotPreview.SharedModel.PreviewsManagerBuilderBase`3.AddCommand
  isSpec: "True"
  fullName: HotPreview.SharedModel.PreviewsManagerBuilderBase<TUIComponent, TPreview, TCommand>.AddCommand
  fullName.vb: HotPreview.SharedModel.PreviewsManagerBuilderBase(Of TUIComponent, TPreview, TCommand).AddCommand
  nameWithType: PreviewsManagerBuilderBase<TUIComponent, TPreview, TCommand>.AddCommand
  nameWithType.vb: PreviewsManagerBuilderBase(Of TUIComponent, TPreview, TCommand).AddCommand
- uid: HotPreview.SharedModel.PreviewsManagerBuilderBase`3.AddOrUpdateCategory(System.String,System.Collections.Generic.IReadOnlyList{System.String})
  name: AddOrUpdateCategory(string, IReadOnlyList<string>)
  href: api/HotPreview.SharedModel.PreviewsManagerBuilderBase-3.html#HotPreview_SharedModel_PreviewsManagerBuilderBase_3_AddOrUpdateCategory_System_String_System_Collections_Generic_IReadOnlyList_System_String__
  commentId: M:HotPreview.SharedModel.PreviewsManagerBuilderBase`3.AddOrUpdateCategory(System.String,System.Collections.Generic.IReadOnlyList{System.String})
  name.vb: AddOrUpdateCategory(String, IReadOnlyList(Of String))
  fullName: HotPreview.SharedModel.PreviewsManagerBuilderBase<TUIComponent, TPreview, TCommand>.AddOrUpdateCategory(string, System.Collections.Generic.IReadOnlyList<string>)
  fullName.vb: HotPreview.SharedModel.PreviewsManagerBuilderBase(Of TUIComponent, TPreview, TCommand).AddOrUpdateCategory(String, System.Collections.Generic.IReadOnlyList(Of String))
  nameWithType: PreviewsManagerBuilderBase<TUIComponent, TPreview, TCommand>.AddOrUpdateCategory(string, IReadOnlyList<string>)
  nameWithType.vb: PreviewsManagerBuilderBase(Of TUIComponent, TPreview, TCommand).AddOrUpdateCategory(String, IReadOnlyList(Of String))
- uid: HotPreview.SharedModel.PreviewsManagerBuilderBase`3.AddOrUpdateCategory*
  name: AddOrUpdateCategory
  href: api/HotPreview.SharedModel.PreviewsManagerBuilderBase-3.html#HotPreview_SharedModel_PreviewsManagerBuilderBase_3_AddOrUpdateCategory_
  commentId: Overload:HotPreview.SharedModel.PreviewsManagerBuilderBase`3.AddOrUpdateCategory
  isSpec: "True"
  fullName: HotPreview.SharedModel.PreviewsManagerBuilderBase<TUIComponent, TPreview, TCommand>.AddOrUpdateCategory
  fullName.vb: HotPreview.SharedModel.PreviewsManagerBuilderBase(Of TUIComponent, TPreview, TCommand).AddOrUpdateCategory
  nameWithType: PreviewsManagerBuilderBase<TUIComponent, TPreview, TCommand>.AddOrUpdateCategory
  nameWithType.vb: PreviewsManagerBuilderBase(Of TUIComponent, TPreview, TCommand).AddOrUpdateCategory
- uid: HotPreview.SharedModel.PreviewsManagerBuilderBase`3.AddOrUpdateCommand(`2)
  name: AddOrUpdateCommand(TCommand)
  href: api/HotPreview.SharedModel.PreviewsManagerBuilderBase-3.html#HotPreview_SharedModel_PreviewsManagerBuilderBase_3_AddOrUpdateCommand__2_
  commentId: M:HotPreview.SharedModel.PreviewsManagerBuilderBase`3.AddOrUpdateCommand(`2)
  fullName: HotPreview.SharedModel.PreviewsManagerBuilderBase<TUIComponent, TPreview, TCommand>.AddOrUpdateCommand(TCommand)
  fullName.vb: HotPreview.SharedModel.PreviewsManagerBuilderBase(Of TUIComponent, TPreview, TCommand).AddOrUpdateCommand(TCommand)
  nameWithType: PreviewsManagerBuilderBase<TUIComponent, TPreview, TCommand>.AddOrUpdateCommand(TCommand)
  nameWithType.vb: PreviewsManagerBuilderBase(Of TUIComponent, TPreview, TCommand).AddOrUpdateCommand(TCommand)
- uid: HotPreview.SharedModel.PreviewsManagerBuilderBase`3.AddOrUpdateCommand*
  name: AddOrUpdateCommand
  href: api/HotPreview.SharedModel.PreviewsManagerBuilderBase-3.html#HotPreview_SharedModel_PreviewsManagerBuilderBase_3_AddOrUpdateCommand_
  commentId: Overload:HotPreview.SharedModel.PreviewsManagerBuilderBase`3.AddOrUpdateCommand
  isSpec: "True"
  fullName: HotPreview.SharedModel.PreviewsManagerBuilderBase<TUIComponent, TPreview, TCommand>.AddOrUpdateCommand
  fullName.vb: HotPreview.SharedModel.PreviewsManagerBuilderBase(Of TUIComponent, TPreview, TCommand).AddOrUpdateCommand
  nameWithType: PreviewsManagerBuilderBase<TUIComponent, TPreview, TCommand>.AddOrUpdateCommand
  nameWithType.vb: PreviewsManagerBuilderBase(Of TUIComponent, TPreview, TCommand).AddOrUpdateCommand
- uid: HotPreview.SharedModel.PreviewsManagerBuilderBase`3.AddOrUpdateUIComponent(`0)
  name: AddOrUpdateUIComponent(TUIComponent)
  href: api/HotPreview.SharedModel.PreviewsManagerBuilderBase-3.html#HotPreview_SharedModel_PreviewsManagerBuilderBase_3_AddOrUpdateUIComponent__0_
  commentId: M:HotPreview.SharedModel.PreviewsManagerBuilderBase`3.AddOrUpdateUIComponent(`0)
  fullName: HotPreview.SharedModel.PreviewsManagerBuilderBase<TUIComponent, TPreview, TCommand>.AddOrUpdateUIComponent(TUIComponent)
  fullName.vb: HotPreview.SharedModel.PreviewsManagerBuilderBase(Of TUIComponent, TPreview, TCommand).AddOrUpdateUIComponent(TUIComponent)
  nameWithType: PreviewsManagerBuilderBase<TUIComponent, TPreview, TCommand>.AddOrUpdateUIComponent(TUIComponent)
  nameWithType.vb: PreviewsManagerBuilderBase(Of TUIComponent, TPreview, TCommand).AddOrUpdateUIComponent(TUIComponent)
- uid: HotPreview.SharedModel.PreviewsManagerBuilderBase`3.AddOrUpdateUIComponent*
  name: AddOrUpdateUIComponent
  href: api/HotPreview.SharedModel.PreviewsManagerBuilderBase-3.html#HotPreview_SharedModel_PreviewsManagerBuilderBase_3_AddOrUpdateUIComponent_
  commentId: Overload:HotPreview.SharedModel.PreviewsManagerBuilderBase`3.AddOrUpdateUIComponent
  isSpec: "True"
  fullName: HotPreview.SharedModel.PreviewsManagerBuilderBase<TUIComponent, TPreview, TCommand>.AddOrUpdateUIComponent
  fullName.vb: HotPreview.SharedModel.PreviewsManagerBuilderBase(Of TUIComponent, TPreview, TCommand).AddOrUpdateUIComponent
  nameWithType: PreviewsManagerBuilderBase<TUIComponent, TPreview, TCommand>.AddOrUpdateUIComponent
  nameWithType.vb: PreviewsManagerBuilderBase(Of TUIComponent, TPreview, TCommand).AddOrUpdateUIComponent
- uid: HotPreview.SharedModel.PreviewsManagerBuilderBase`3.AddUIComponent(`0)
  name: AddUIComponent(TUIComponent)
  href: api/HotPreview.SharedModel.PreviewsManagerBuilderBase-3.html#HotPreview_SharedModel_PreviewsManagerBuilderBase_3_AddUIComponent__0_
  commentId: M:HotPreview.SharedModel.PreviewsManagerBuilderBase`3.AddUIComponent(`0)
  fullName: HotPreview.SharedModel.PreviewsManagerBuilderBase<TUIComponent, TPreview, TCommand>.AddUIComponent(TUIComponent)
  fullName.vb: HotPreview.SharedModel.PreviewsManagerBuilderBase(Of TUIComponent, TPreview, TCommand).AddUIComponent(TUIComponent)
  nameWithType: PreviewsManagerBuilderBase<TUIComponent, TPreview, TCommand>.AddUIComponent(TUIComponent)
  nameWithType.vb: PreviewsManagerBuilderBase(Of TUIComponent, TPreview, TCommand).AddUIComponent(TUIComponent)
- uid: HotPreview.SharedModel.PreviewsManagerBuilderBase`3.AddUIComponent*
  name: AddUIComponent
  href: api/HotPreview.SharedModel.PreviewsManagerBuilderBase-3.html#HotPreview_SharedModel_PreviewsManagerBuilderBase_3_AddUIComponent_
  commentId: Overload:HotPreview.SharedModel.PreviewsManagerBuilderBase`3.AddUIComponent
  isSpec: "True"
  fullName: HotPreview.SharedModel.PreviewsManagerBuilderBase<TUIComponent, TPreview, TCommand>.AddUIComponent
  fullName.vb: HotPreview.SharedModel.PreviewsManagerBuilderBase(Of TUIComponent, TPreview, TCommand).AddUIComponent
  nameWithType: PreviewsManagerBuilderBase<TUIComponent, TPreview, TCommand>.AddUIComponent
  nameWithType.vb: PreviewsManagerBuilderBase(Of TUIComponent, TPreview, TCommand).AddUIComponent
- uid: HotPreview.SharedModel.PreviewsManagerBuilderBase`3.AddUIComponentBaseType(HotPreview.SharedModel.UIComponentKind,System.String,System.String)
  name: AddUIComponentBaseType(UIComponentKind, string, string)
  href: api/HotPreview.SharedModel.PreviewsManagerBuilderBase-3.html#HotPreview_SharedModel_PreviewsManagerBuilderBase_3_AddUIComponentBaseType_HotPreview_SharedModel_UIComponentKind_System_String_System_String_
  commentId: M:HotPreview.SharedModel.PreviewsManagerBuilderBase`3.AddUIComponentBaseType(HotPreview.SharedModel.UIComponentKind,System.String,System.String)
  name.vb: AddUIComponentBaseType(UIComponentKind, String, String)
  fullName: HotPreview.SharedModel.PreviewsManagerBuilderBase<TUIComponent, TPreview, TCommand>.AddUIComponentBaseType(HotPreview.SharedModel.UIComponentKind, string, string)
  fullName.vb: HotPreview.SharedModel.PreviewsManagerBuilderBase(Of TUIComponent, TPreview, TCommand).AddUIComponentBaseType(HotPreview.SharedModel.UIComponentKind, String, String)
  nameWithType: PreviewsManagerBuilderBase<TUIComponent, TPreview, TCommand>.AddUIComponentBaseType(UIComponentKind, string, string)
  nameWithType.vb: PreviewsManagerBuilderBase(Of TUIComponent, TPreview, TCommand).AddUIComponentBaseType(UIComponentKind, String, String)
- uid: HotPreview.SharedModel.PreviewsManagerBuilderBase`3.AddUIComponentBaseType*
  name: AddUIComponentBaseType
  href: api/HotPreview.SharedModel.PreviewsManagerBuilderBase-3.html#HotPreview_SharedModel_PreviewsManagerBuilderBase_3_AddUIComponentBaseType_
  commentId: Overload:HotPreview.SharedModel.PreviewsManagerBuilderBase`3.AddUIComponentBaseType
  isSpec: "True"
  fullName: HotPreview.SharedModel.PreviewsManagerBuilderBase<TUIComponent, TPreview, TCommand>.AddUIComponentBaseType
  fullName.vb: HotPreview.SharedModel.PreviewsManagerBuilderBase(Of TUIComponent, TPreview, TCommand).AddUIComponentBaseType
  nameWithType: PreviewsManagerBuilderBase<TUIComponent, TPreview, TCommand>.AddUIComponentBaseType
  nameWithType.vb: PreviewsManagerBuilderBase(Of TUIComponent, TPreview, TCommand).AddUIComponentBaseType
- uid: HotPreview.SharedModel.PreviewsManagerBuilderBase`3.Categories
  name: Categories
  href: api/HotPreview.SharedModel.PreviewsManagerBuilderBase-3.html#HotPreview_SharedModel_PreviewsManagerBuilderBase_3_Categories
  commentId: P:HotPreview.SharedModel.PreviewsManagerBuilderBase`3.Categories
  fullName: HotPreview.SharedModel.PreviewsManagerBuilderBase<TUIComponent, TPreview, TCommand>.Categories
  fullName.vb: HotPreview.SharedModel.PreviewsManagerBuilderBase(Of TUIComponent, TPreview, TCommand).Categories
  nameWithType: PreviewsManagerBuilderBase<TUIComponent, TPreview, TCommand>.Categories
  nameWithType.vb: PreviewsManagerBuilderBase(Of TUIComponent, TPreview, TCommand).Categories
- uid: HotPreview.SharedModel.PreviewsManagerBuilderBase`3.Categories*
  name: Categories
  href: api/HotPreview.SharedModel.PreviewsManagerBuilderBase-3.html#HotPreview_SharedModel_PreviewsManagerBuilderBase_3_Categories_
  commentId: Overload:HotPreview.SharedModel.PreviewsManagerBuilderBase`3.Categories
  isSpec: "True"
  fullName: HotPreview.SharedModel.PreviewsManagerBuilderBase<TUIComponent, TPreview, TCommand>.Categories
  fullName.vb: HotPreview.SharedModel.PreviewsManagerBuilderBase(Of TUIComponent, TPreview, TCommand).Categories
  nameWithType: PreviewsManagerBuilderBase<TUIComponent, TPreview, TCommand>.Categories
  nameWithType.vb: PreviewsManagerBuilderBase(Of TUIComponent, TPreview, TCommand).Categories
- uid: HotPreview.SharedModel.PreviewsManagerBuilderBase`3.CommandsByName
  name: CommandsByName
  href: api/HotPreview.SharedModel.PreviewsManagerBuilderBase-3.html#HotPreview_SharedModel_PreviewsManagerBuilderBase_3_CommandsByName
  commentId: P:HotPreview.SharedModel.PreviewsManagerBuilderBase`3.CommandsByName
  fullName: HotPreview.SharedModel.PreviewsManagerBuilderBase<TUIComponent, TPreview, TCommand>.CommandsByName
  fullName.vb: HotPreview.SharedModel.PreviewsManagerBuilderBase(Of TUIComponent, TPreview, TCommand).CommandsByName
  nameWithType: PreviewsManagerBuilderBase<TUIComponent, TPreview, TCommand>.CommandsByName
  nameWithType.vb: PreviewsManagerBuilderBase(Of TUIComponent, TPreview, TCommand).CommandsByName
- uid: HotPreview.SharedModel.PreviewsManagerBuilderBase`3.CommandsByName*
  name: CommandsByName
  href: api/HotPreview.SharedModel.PreviewsManagerBuilderBase-3.html#HotPreview_SharedModel_PreviewsManagerBuilderBase_3_CommandsByName_
  commentId: Overload:HotPreview.SharedModel.PreviewsManagerBuilderBase`3.CommandsByName
  isSpec: "True"
  fullName: HotPreview.SharedModel.PreviewsManagerBuilderBase<TUIComponent, TPreview, TCommand>.CommandsByName
  fullName.vb: HotPreview.SharedModel.PreviewsManagerBuilderBase(Of TUIComponent, TPreview, TCommand).CommandsByName
  nameWithType: PreviewsManagerBuilderBase<TUIComponent, TPreview, TCommand>.CommandsByName
  nameWithType.vb: PreviewsManagerBuilderBase(Of TUIComponent, TPreview, TCommand).CommandsByName
- uid: HotPreview.SharedModel.PreviewsManagerBuilderBase`3.GetCommand(System.String)
  name: GetCommand(string)
  href: api/HotPreview.SharedModel.PreviewsManagerBuilderBase-3.html#HotPreview_SharedModel_PreviewsManagerBuilderBase_3_GetCommand_System_String_
  commentId: M:HotPreview.SharedModel.PreviewsManagerBuilderBase`3.GetCommand(System.String)
  name.vb: GetCommand(String)
  fullName: HotPreview.SharedModel.PreviewsManagerBuilderBase<TUIComponent, TPreview, TCommand>.GetCommand(string)
  fullName.vb: HotPreview.SharedModel.PreviewsManagerBuilderBase(Of TUIComponent, TPreview, TCommand).GetCommand(String)
  nameWithType: PreviewsManagerBuilderBase<TUIComponent, TPreview, TCommand>.GetCommand(string)
  nameWithType.vb: PreviewsManagerBuilderBase(Of TUIComponent, TPreview, TCommand).GetCommand(String)
- uid: HotPreview.SharedModel.PreviewsManagerBuilderBase`3.GetCommand*
  name: GetCommand
  href: api/HotPreview.SharedModel.PreviewsManagerBuilderBase-3.html#HotPreview_SharedModel_PreviewsManagerBuilderBase_3_GetCommand_
  commentId: Overload:HotPreview.SharedModel.PreviewsManagerBuilderBase`3.GetCommand
  isSpec: "True"
  fullName: HotPreview.SharedModel.PreviewsManagerBuilderBase<TUIComponent, TPreview, TCommand>.GetCommand
  fullName.vb: HotPreview.SharedModel.PreviewsManagerBuilderBase(Of TUIComponent, TPreview, TCommand).GetCommand
  nameWithType: PreviewsManagerBuilderBase<TUIComponent, TPreview, TCommand>.GetCommand
  nameWithType.vb: PreviewsManagerBuilderBase(Of TUIComponent, TPreview, TCommand).GetCommand
- uid: HotPreview.SharedModel.PreviewsManagerBuilderBase`3.GetUIComponent(System.String)
  name: GetUIComponent(string)
  href: api/HotPreview.SharedModel.PreviewsManagerBuilderBase-3.html#HotPreview_SharedModel_PreviewsManagerBuilderBase_3_GetUIComponent_System_String_
  commentId: M:HotPreview.SharedModel.PreviewsManagerBuilderBase`3.GetUIComponent(System.String)
  name.vb: GetUIComponent(String)
  fullName: HotPreview.SharedModel.PreviewsManagerBuilderBase<TUIComponent, TPreview, TCommand>.GetUIComponent(string)
  fullName.vb: HotPreview.SharedModel.PreviewsManagerBuilderBase(Of TUIComponent, TPreview, TCommand).GetUIComponent(String)
  nameWithType: PreviewsManagerBuilderBase<TUIComponent, TPreview, TCommand>.GetUIComponent(string)
  nameWithType.vb: PreviewsManagerBuilderBase(Of TUIComponent, TPreview, TCommand).GetUIComponent(String)
- uid: HotPreview.SharedModel.PreviewsManagerBuilderBase`3.GetUIComponent*
  name: GetUIComponent
  href: api/HotPreview.SharedModel.PreviewsManagerBuilderBase-3.html#HotPreview_SharedModel_PreviewsManagerBuilderBase_3_GetUIComponent_
  commentId: Overload:HotPreview.SharedModel.PreviewsManagerBuilderBase`3.GetUIComponent
  isSpec: "True"
  fullName: HotPreview.SharedModel.PreviewsManagerBuilderBase<TUIComponent, TPreview, TCommand>.GetUIComponent
  fullName.vb: HotPreview.SharedModel.PreviewsManagerBuilderBase(Of TUIComponent, TPreview, TCommand).GetUIComponent
  nameWithType: PreviewsManagerBuilderBase<TUIComponent, TPreview, TCommand>.GetUIComponent
  nameWithType.vb: PreviewsManagerBuilderBase(Of TUIComponent, TPreview, TCommand).GetUIComponent
- uid: HotPreview.SharedModel.PreviewsManagerBuilderBase`3.IsUIComponentBaseType(System.String,HotPreview.SharedModel.UIComponentKind@)
  name: IsUIComponentBaseType(string, out UIComponentKind)
  href: api/HotPreview.SharedModel.PreviewsManagerBuilderBase-3.html#HotPreview_SharedModel_PreviewsManagerBuilderBase_3_IsUIComponentBaseType_System_String_HotPreview_SharedModel_UIComponentKind__
  commentId: M:HotPreview.SharedModel.PreviewsManagerBuilderBase`3.IsUIComponentBaseType(System.String,HotPreview.SharedModel.UIComponentKind@)
  name.vb: IsUIComponentBaseType(String, UIComponentKind)
  fullName: HotPreview.SharedModel.PreviewsManagerBuilderBase<TUIComponent, TPreview, TCommand>.IsUIComponentBaseType(string, out HotPreview.SharedModel.UIComponentKind)
  fullName.vb: HotPreview.SharedModel.PreviewsManagerBuilderBase(Of TUIComponent, TPreview, TCommand).IsUIComponentBaseType(String, HotPreview.SharedModel.UIComponentKind)
  nameWithType: PreviewsManagerBuilderBase<TUIComponent, TPreview, TCommand>.IsUIComponentBaseType(string, out UIComponentKind)
  nameWithType.vb: PreviewsManagerBuilderBase(Of TUIComponent, TPreview, TCommand).IsUIComponentBaseType(String, UIComponentKind)
- uid: HotPreview.SharedModel.PreviewsManagerBuilderBase`3.IsUIComponentBaseType*
  name: IsUIComponentBaseType
  href: api/HotPreview.SharedModel.PreviewsManagerBuilderBase-3.html#HotPreview_SharedModel_PreviewsManagerBuilderBase_3_IsUIComponentBaseType_
  commentId: Overload:HotPreview.SharedModel.PreviewsManagerBuilderBase`3.IsUIComponentBaseType
  isSpec: "True"
  fullName: HotPreview.SharedModel.PreviewsManagerBuilderBase<TUIComponent, TPreview, TCommand>.IsUIComponentBaseType
  fullName.vb: HotPreview.SharedModel.PreviewsManagerBuilderBase(Of TUIComponent, TPreview, TCommand).IsUIComponentBaseType
  nameWithType: PreviewsManagerBuilderBase<TUIComponent, TPreview, TCommand>.IsUIComponentBaseType
  nameWithType.vb: PreviewsManagerBuilderBase(Of TUIComponent, TPreview, TCommand).IsUIComponentBaseType
- uid: HotPreview.SharedModel.PreviewsManagerBuilderBase`3.UIComponentsByName
  name: UIComponentsByName
  href: api/HotPreview.SharedModel.PreviewsManagerBuilderBase-3.html#HotPreview_SharedModel_PreviewsManagerBuilderBase_3_UIComponentsByName
  commentId: P:HotPreview.SharedModel.PreviewsManagerBuilderBase`3.UIComponentsByName
  fullName: HotPreview.SharedModel.PreviewsManagerBuilderBase<TUIComponent, TPreview, TCommand>.UIComponentsByName
  fullName.vb: HotPreview.SharedModel.PreviewsManagerBuilderBase(Of TUIComponent, TPreview, TCommand).UIComponentsByName
  nameWithType: PreviewsManagerBuilderBase<TUIComponent, TPreview, TCommand>.UIComponentsByName
  nameWithType.vb: PreviewsManagerBuilderBase(Of TUIComponent, TPreview, TCommand).UIComponentsByName
- uid: HotPreview.SharedModel.PreviewsManagerBuilderBase`3.UIComponentsByName*
  name: UIComponentsByName
  href: api/HotPreview.SharedModel.PreviewsManagerBuilderBase-3.html#HotPreview_SharedModel_PreviewsManagerBuilderBase_3_UIComponentsByName_
  commentId: Overload:HotPreview.SharedModel.PreviewsManagerBuilderBase`3.UIComponentsByName
  isSpec: "True"
  fullName: HotPreview.SharedModel.PreviewsManagerBuilderBase<TUIComponent, TPreview, TCommand>.UIComponentsByName
  fullName.vb: HotPreview.SharedModel.PreviewsManagerBuilderBase(Of TUIComponent, TPreview, TCommand).UIComponentsByName
  nameWithType: PreviewsManagerBuilderBase<TUIComponent, TPreview, TCommand>.UIComponentsByName
  nameWithType.vb: PreviewsManagerBuilderBase(Of TUIComponent, TPreview, TCommand).UIComponentsByName
- uid: HotPreview.SharedModel.PreviewsManagerBuilderBase`3.Validate
  name: Validate()
  href: api/HotPreview.SharedModel.PreviewsManagerBuilderBase-3.html#HotPreview_SharedModel_PreviewsManagerBuilderBase_3_Validate
  commentId: M:HotPreview.SharedModel.PreviewsManagerBuilderBase`3.Validate
  fullName: HotPreview.SharedModel.PreviewsManagerBuilderBase<TUIComponent, TPreview, TCommand>.Validate()
  fullName.vb: HotPreview.SharedModel.PreviewsManagerBuilderBase(Of TUIComponent, TPreview, TCommand).Validate()
  nameWithType: PreviewsManagerBuilderBase<TUIComponent, TPreview, TCommand>.Validate()
  nameWithType.vb: PreviewsManagerBuilderBase(Of TUIComponent, TPreview, TCommand).Validate()
- uid: HotPreview.SharedModel.PreviewsManagerBuilderBase`3.Validate*
  name: Validate
  href: api/HotPreview.SharedModel.PreviewsManagerBuilderBase-3.html#HotPreview_SharedModel_PreviewsManagerBuilderBase_3_Validate_
  commentId: Overload:HotPreview.SharedModel.PreviewsManagerBuilderBase`3.Validate
  isSpec: "True"
  fullName: HotPreview.SharedModel.PreviewsManagerBuilderBase<TUIComponent, TPreview, TCommand>.Validate
  fullName.vb: HotPreview.SharedModel.PreviewsManagerBuilderBase(Of TUIComponent, TPreview, TCommand).Validate
  nameWithType: PreviewsManagerBuilderBase<TUIComponent, TPreview, TCommand>.Validate
  nameWithType.vb: PreviewsManagerBuilderBase(Of TUIComponent, TPreview, TCommand).Validate
- uid: HotPreview.SharedModel.PreviewsManagerBuilderBase`3._baseTypes
  name: _baseTypes
  href: api/HotPreview.SharedModel.PreviewsManagerBuilderBase-3.html#HotPreview_SharedModel_PreviewsManagerBuilderBase_3__baseTypes
  commentId: F:HotPreview.SharedModel.PreviewsManagerBuilderBase`3._baseTypes
  fullName: HotPreview.SharedModel.PreviewsManagerBuilderBase<TUIComponent, TPreview, TCommand>._baseTypes
  fullName.vb: HotPreview.SharedModel.PreviewsManagerBuilderBase(Of TUIComponent, TPreview, TCommand)._baseTypes
  nameWithType: PreviewsManagerBuilderBase<TUIComponent, TPreview, TCommand>._baseTypes
  nameWithType.vb: PreviewsManagerBuilderBase(Of TUIComponent, TPreview, TCommand)._baseTypes
- uid: HotPreview.SharedModel.PreviewsManagerBuilderBase`3._categories
  name: _categories
  href: api/HotPreview.SharedModel.PreviewsManagerBuilderBase-3.html#HotPreview_SharedModel_PreviewsManagerBuilderBase_3__categories
  commentId: F:HotPreview.SharedModel.PreviewsManagerBuilderBase`3._categories
  fullName: HotPreview.SharedModel.PreviewsManagerBuilderBase<TUIComponent, TPreview, TCommand>._categories
  fullName.vb: HotPreview.SharedModel.PreviewsManagerBuilderBase(Of TUIComponent, TPreview, TCommand)._categories
  nameWithType: PreviewsManagerBuilderBase<TUIComponent, TPreview, TCommand>._categories
  nameWithType.vb: PreviewsManagerBuilderBase(Of TUIComponent, TPreview, TCommand)._categories
- uid: HotPreview.SharedModel.PreviewsManagerBuilderBase`3._commandsByName
  name: _commandsByName
  href: api/HotPreview.SharedModel.PreviewsManagerBuilderBase-3.html#HotPreview_SharedModel_PreviewsManagerBuilderBase_3__commandsByName
  commentId: F:HotPreview.SharedModel.PreviewsManagerBuilderBase`3._commandsByName
  fullName: HotPreview.SharedModel.PreviewsManagerBuilderBase<TUIComponent, TPreview, TCommand>._commandsByName
  fullName.vb: HotPreview.SharedModel.PreviewsManagerBuilderBase(Of TUIComponent, TPreview, TCommand)._commandsByName
  nameWithType: PreviewsManagerBuilderBase<TUIComponent, TPreview, TCommand>._commandsByName
  nameWithType.vb: PreviewsManagerBuilderBase(Of TUIComponent, TPreview, TCommand)._commandsByName
- uid: HotPreview.SharedModel.PreviewsManagerBuilderBase`3._uiComponentsByName
  name: _uiComponentsByName
  href: api/HotPreview.SharedModel.PreviewsManagerBuilderBase-3.html#HotPreview_SharedModel_PreviewsManagerBuilderBase_3__uiComponentsByName
  commentId: F:HotPreview.SharedModel.PreviewsManagerBuilderBase`3._uiComponentsByName
  fullName: HotPreview.SharedModel.PreviewsManagerBuilderBase<TUIComponent, TPreview, TCommand>._uiComponentsByName
  fullName.vb: HotPreview.SharedModel.PreviewsManagerBuilderBase(Of TUIComponent, TPreview, TCommand)._uiComponentsByName
  nameWithType: PreviewsManagerBuilderBase<TUIComponent, TPreview, TCommand>._uiComponentsByName
  nameWithType.vb: PreviewsManagerBuilderBase(Of TUIComponent, TPreview, TCommand)._uiComponentsByName
- uid: HotPreview.SharedModel.Protocol
  name: HotPreview.SharedModel.Protocol
  href: api/HotPreview.SharedModel.Protocol.html
  commentId: N:HotPreview.SharedModel.Protocol
  fullName: HotPreview.SharedModel.Protocol
  nameWithType: HotPreview.SharedModel.Protocol
- uid: HotPreview.SharedModel.Protocol.IPreviewAppControllerService
  name: IPreviewAppControllerService
  href: api/HotPreview.SharedModel.Protocol.IPreviewAppControllerService.html
  commentId: T:HotPreview.SharedModel.Protocol.IPreviewAppControllerService
  fullName: HotPreview.SharedModel.Protocol.IPreviewAppControllerService
  nameWithType: IPreviewAppControllerService
- uid: HotPreview.SharedModel.Protocol.IPreviewAppControllerService.NotifyPreviewsChangedAsync
  name: NotifyPreviewsChangedAsync()
  href: api/HotPreview.SharedModel.Protocol.IPreviewAppControllerService.html#HotPreview_SharedModel_Protocol_IPreviewAppControllerService_NotifyPreviewsChangedAsync
  commentId: M:HotPreview.SharedModel.Protocol.IPreviewAppControllerService.NotifyPreviewsChangedAsync
  fullName: HotPreview.SharedModel.Protocol.IPreviewAppControllerService.NotifyPreviewsChangedAsync()
  nameWithType: IPreviewAppControllerService.NotifyPreviewsChangedAsync()
- uid: HotPreview.SharedModel.Protocol.IPreviewAppControllerService.NotifyPreviewsChangedAsync*
  name: NotifyPreviewsChangedAsync
  href: api/HotPreview.SharedModel.Protocol.IPreviewAppControllerService.html#HotPreview_SharedModel_Protocol_IPreviewAppControllerService_NotifyPreviewsChangedAsync_
  commentId: Overload:HotPreview.SharedModel.Protocol.IPreviewAppControllerService.NotifyPreviewsChangedAsync
  isSpec: "True"
  fullName: HotPreview.SharedModel.Protocol.IPreviewAppControllerService.NotifyPreviewsChangedAsync
  nameWithType: IPreviewAppControllerService.NotifyPreviewsChangedAsync
- uid: HotPreview.SharedModel.Protocol.IPreviewAppControllerService.RegisterAppAsync(System.String,System.String)
  name: RegisterAppAsync(string, string)
  href: api/HotPreview.SharedModel.Protocol.IPreviewAppControllerService.html#HotPreview_SharedModel_Protocol_IPreviewAppControllerService_RegisterAppAsync_System_String_System_String_
  commentId: M:HotPreview.SharedModel.Protocol.IPreviewAppControllerService.RegisterAppAsync(System.String,System.String)
  name.vb: RegisterAppAsync(String, String)
  fullName: HotPreview.SharedModel.Protocol.IPreviewAppControllerService.RegisterAppAsync(string, string)
  fullName.vb: HotPreview.SharedModel.Protocol.IPreviewAppControllerService.RegisterAppAsync(String, String)
  nameWithType: IPreviewAppControllerService.RegisterAppAsync(string, string)
  nameWithType.vb: IPreviewAppControllerService.RegisterAppAsync(String, String)
- uid: HotPreview.SharedModel.Protocol.IPreviewAppControllerService.RegisterAppAsync*
  name: RegisterAppAsync
  href: api/HotPreview.SharedModel.Protocol.IPreviewAppControllerService.html#HotPreview_SharedModel_Protocol_IPreviewAppControllerService_RegisterAppAsync_
  commentId: Overload:HotPreview.SharedModel.Protocol.IPreviewAppControllerService.RegisterAppAsync
  isSpec: "True"
  fullName: HotPreview.SharedModel.Protocol.IPreviewAppControllerService.RegisterAppAsync
  nameWithType: IPreviewAppControllerService.RegisterAppAsync
- uid: HotPreview.SharedModel.Protocol.IPreviewAppService
  name: IPreviewAppService
  href: api/HotPreview.SharedModel.Protocol.IPreviewAppService.html
  commentId: T:HotPreview.SharedModel.Protocol.IPreviewAppService
  fullName: HotPreview.SharedModel.Protocol.IPreviewAppService
  nameWithType: IPreviewAppService
- uid: HotPreview.SharedModel.Protocol.IPreviewAppService.GetCommandsAsync
  name: GetCommandsAsync()
  href: api/HotPreview.SharedModel.Protocol.IPreviewAppService.html#HotPreview_SharedModel_Protocol_IPreviewAppService_GetCommandsAsync
  commentId: M:HotPreview.SharedModel.Protocol.IPreviewAppService.GetCommandsAsync
  fullName: HotPreview.SharedModel.Protocol.IPreviewAppService.GetCommandsAsync()
  nameWithType: IPreviewAppService.GetCommandsAsync()
- uid: HotPreview.SharedModel.Protocol.IPreviewAppService.GetCommandsAsync*
  name: GetCommandsAsync
  href: api/HotPreview.SharedModel.Protocol.IPreviewAppService.html#HotPreview_SharedModel_Protocol_IPreviewAppService_GetCommandsAsync_
  commentId: Overload:HotPreview.SharedModel.Protocol.IPreviewAppService.GetCommandsAsync
  isSpec: "True"
  fullName: HotPreview.SharedModel.Protocol.IPreviewAppService.GetCommandsAsync
  nameWithType: IPreviewAppService.GetCommandsAsync
- uid: HotPreview.SharedModel.Protocol.IPreviewAppService.GetPreviewSnapshotAsync(System.String,System.String)
  name: GetPreviewSnapshotAsync(string, string)
  href: api/HotPreview.SharedModel.Protocol.IPreviewAppService.html#HotPreview_SharedModel_Protocol_IPreviewAppService_GetPreviewSnapshotAsync_System_String_System_String_
  commentId: M:HotPreview.SharedModel.Protocol.IPreviewAppService.GetPreviewSnapshotAsync(System.String,System.String)
  name.vb: GetPreviewSnapshotAsync(String, String)
  fullName: HotPreview.SharedModel.Protocol.IPreviewAppService.GetPreviewSnapshotAsync(string, string)
  fullName.vb: HotPreview.SharedModel.Protocol.IPreviewAppService.GetPreviewSnapshotAsync(String, String)
  nameWithType: IPreviewAppService.GetPreviewSnapshotAsync(string, string)
  nameWithType.vb: IPreviewAppService.GetPreviewSnapshotAsync(String, String)
- uid: HotPreview.SharedModel.Protocol.IPreviewAppService.GetPreviewSnapshotAsync*
  name: GetPreviewSnapshotAsync
  href: api/HotPreview.SharedModel.Protocol.IPreviewAppService.html#HotPreview_SharedModel_Protocol_IPreviewAppService_GetPreviewSnapshotAsync_
  commentId: Overload:HotPreview.SharedModel.Protocol.IPreviewAppService.GetPreviewSnapshotAsync
  isSpec: "True"
  fullName: HotPreview.SharedModel.Protocol.IPreviewAppService.GetPreviewSnapshotAsync
  nameWithType: IPreviewAppService.GetPreviewSnapshotAsync
- uid: HotPreview.SharedModel.Protocol.IPreviewAppService.GetUIComponentPreviewsAsync(System.String)
  name: GetUIComponentPreviewsAsync(string)
  href: api/HotPreview.SharedModel.Protocol.IPreviewAppService.html#HotPreview_SharedModel_Protocol_IPreviewAppService_GetUIComponentPreviewsAsync_System_String_
  commentId: M:HotPreview.SharedModel.Protocol.IPreviewAppService.GetUIComponentPreviewsAsync(System.String)
  name.vb: GetUIComponentPreviewsAsync(String)
  fullName: HotPreview.SharedModel.Protocol.IPreviewAppService.GetUIComponentPreviewsAsync(string)
  fullName.vb: HotPreview.SharedModel.Protocol.IPreviewAppService.GetUIComponentPreviewsAsync(String)
  nameWithType: IPreviewAppService.GetUIComponentPreviewsAsync(string)
  nameWithType.vb: IPreviewAppService.GetUIComponentPreviewsAsync(String)
- uid: HotPreview.SharedModel.Protocol.IPreviewAppService.GetUIComponentPreviewsAsync*
  name: GetUIComponentPreviewsAsync
  href: api/HotPreview.SharedModel.Protocol.IPreviewAppService.html#HotPreview_SharedModel_Protocol_IPreviewAppService_GetUIComponentPreviewsAsync_
  commentId: Overload:HotPreview.SharedModel.Protocol.IPreviewAppService.GetUIComponentPreviewsAsync
  isSpec: "True"
  fullName: HotPreview.SharedModel.Protocol.IPreviewAppService.GetUIComponentPreviewsAsync
  nameWithType: IPreviewAppService.GetUIComponentPreviewsAsync
- uid: HotPreview.SharedModel.Protocol.IPreviewAppService.GetUIComponentsAsync
  name: GetUIComponentsAsync()
  href: api/HotPreview.SharedModel.Protocol.IPreviewAppService.html#HotPreview_SharedModel_Protocol_IPreviewAppService_GetUIComponentsAsync
  commentId: M:HotPreview.SharedModel.Protocol.IPreviewAppService.GetUIComponentsAsync
  fullName: HotPreview.SharedModel.Protocol.IPreviewAppService.GetUIComponentsAsync()
  nameWithType: IPreviewAppService.GetUIComponentsAsync()
- uid: HotPreview.SharedModel.Protocol.IPreviewAppService.GetUIComponentsAsync*
  name: GetUIComponentsAsync
  href: api/HotPreview.SharedModel.Protocol.IPreviewAppService.html#HotPreview_SharedModel_Protocol_IPreviewAppService_GetUIComponentsAsync_
  commentId: Overload:HotPreview.SharedModel.Protocol.IPreviewAppService.GetUIComponentsAsync
  isSpec: "True"
  fullName: HotPreview.SharedModel.Protocol.IPreviewAppService.GetUIComponentsAsync
  nameWithType: IPreviewAppService.GetUIComponentsAsync
- uid: HotPreview.SharedModel.Protocol.IPreviewAppService.InvokeCommandAsync(System.String)
  name: InvokeCommandAsync(string)
  href: api/HotPreview.SharedModel.Protocol.IPreviewAppService.html#HotPreview_SharedModel_Protocol_IPreviewAppService_InvokeCommandAsync_System_String_
  commentId: M:HotPreview.SharedModel.Protocol.IPreviewAppService.InvokeCommandAsync(System.String)
  name.vb: InvokeCommandAsync(String)
  fullName: HotPreview.SharedModel.Protocol.IPreviewAppService.InvokeCommandAsync(string)
  fullName.vb: HotPreview.SharedModel.Protocol.IPreviewAppService.InvokeCommandAsync(String)
  nameWithType: IPreviewAppService.InvokeCommandAsync(string)
  nameWithType.vb: IPreviewAppService.InvokeCommandAsync(String)
- uid: HotPreview.SharedModel.Protocol.IPreviewAppService.InvokeCommandAsync*
  name: InvokeCommandAsync
  href: api/HotPreview.SharedModel.Protocol.IPreviewAppService.html#HotPreview_SharedModel_Protocol_IPreviewAppService_InvokeCommandAsync_
  commentId: Overload:HotPreview.SharedModel.Protocol.IPreviewAppService.InvokeCommandAsync
  isSpec: "True"
  fullName: HotPreview.SharedModel.Protocol.IPreviewAppService.InvokeCommandAsync
  nameWithType: IPreviewAppService.InvokeCommandAsync
- uid: HotPreview.SharedModel.Protocol.IPreviewAppService.NavigateToPreviewAsync(System.String,System.String)
  name: NavigateToPreviewAsync(string, string)
  href: api/HotPreview.SharedModel.Protocol.IPreviewAppService.html#HotPreview_SharedModel_Protocol_IPreviewAppService_NavigateToPreviewAsync_System_String_System_String_
  commentId: M:HotPreview.SharedModel.Protocol.IPreviewAppService.NavigateToPreviewAsync(System.String,System.String)
  name.vb: NavigateToPreviewAsync(String, String)
  fullName: HotPreview.SharedModel.Protocol.IPreviewAppService.NavigateToPreviewAsync(string, string)
  fullName.vb: HotPreview.SharedModel.Protocol.IPreviewAppService.NavigateToPreviewAsync(String, String)
  nameWithType: IPreviewAppService.NavigateToPreviewAsync(string, string)
  nameWithType.vb: IPreviewAppService.NavigateToPreviewAsync(String, String)
- uid: HotPreview.SharedModel.Protocol.IPreviewAppService.NavigateToPreviewAsync*
  name: NavigateToPreviewAsync
  href: api/HotPreview.SharedModel.Protocol.IPreviewAppService.html#HotPreview_SharedModel_Protocol_IPreviewAppService_NavigateToPreviewAsync_
  commentId: Overload:HotPreview.SharedModel.Protocol.IPreviewAppService.NavigateToPreviewAsync
  isSpec: "True"
  fullName: HotPreview.SharedModel.Protocol.IPreviewAppService.NavigateToPreviewAsync
  nameWithType: IPreviewAppService.NavigateToPreviewAsync
- uid: HotPreview.SharedModel.Protocol.PreviewCommandInfo
  name: PreviewCommandInfo
  href: api/HotPreview.SharedModel.Protocol.PreviewCommandInfo.html
  commentId: T:HotPreview.SharedModel.Protocol.PreviewCommandInfo
  fullName: HotPreview.SharedModel.Protocol.PreviewCommandInfo
  nameWithType: PreviewCommandInfo
- uid: HotPreview.SharedModel.Protocol.PreviewCommandInfo.#ctor(System.String,System.String)
  name: PreviewCommandInfo(string, string?)
  href: api/HotPreview.SharedModel.Protocol.PreviewCommandInfo.html#HotPreview_SharedModel_Protocol_PreviewCommandInfo__ctor_System_String_System_String_
  commentId: M:HotPreview.SharedModel.Protocol.PreviewCommandInfo.#ctor(System.String,System.String)
  name.vb: New(String, String)
  fullName: HotPreview.SharedModel.Protocol.PreviewCommandInfo.PreviewCommandInfo(string, string?)
  fullName.vb: HotPreview.SharedModel.Protocol.PreviewCommandInfo.New(String, String)
  nameWithType: PreviewCommandInfo.PreviewCommandInfo(string, string?)
  nameWithType.vb: PreviewCommandInfo.New(String, String)
- uid: HotPreview.SharedModel.Protocol.PreviewCommandInfo.#ctor*
  name: PreviewCommandInfo
  href: api/HotPreview.SharedModel.Protocol.PreviewCommandInfo.html#HotPreview_SharedModel_Protocol_PreviewCommandInfo__ctor_
  commentId: Overload:HotPreview.SharedModel.Protocol.PreviewCommandInfo.#ctor
  isSpec: "True"
  name.vb: New
  fullName: HotPreview.SharedModel.Protocol.PreviewCommandInfo.PreviewCommandInfo
  fullName.vb: HotPreview.SharedModel.Protocol.PreviewCommandInfo.New
  nameWithType: PreviewCommandInfo.PreviewCommandInfo
  nameWithType.vb: PreviewCommandInfo.New
- uid: HotPreview.SharedModel.Protocol.PreviewCommandInfo.DisplayName
  name: DisplayName
  href: api/HotPreview.SharedModel.Protocol.PreviewCommandInfo.html#HotPreview_SharedModel_Protocol_PreviewCommandInfo_DisplayName
  commentId: P:HotPreview.SharedModel.Protocol.PreviewCommandInfo.DisplayName
  fullName: HotPreview.SharedModel.Protocol.PreviewCommandInfo.DisplayName
  nameWithType: PreviewCommandInfo.DisplayName
- uid: HotPreview.SharedModel.Protocol.PreviewCommandInfo.DisplayName*
  name: DisplayName
  href: api/HotPreview.SharedModel.Protocol.PreviewCommandInfo.html#HotPreview_SharedModel_Protocol_PreviewCommandInfo_DisplayName_
  commentId: Overload:HotPreview.SharedModel.Protocol.PreviewCommandInfo.DisplayName
  isSpec: "True"
  fullName: HotPreview.SharedModel.Protocol.PreviewCommandInfo.DisplayName
  nameWithType: PreviewCommandInfo.DisplayName
- uid: HotPreview.SharedModel.Protocol.PreviewCommandInfo.Name
  name: Name
  href: api/HotPreview.SharedModel.Protocol.PreviewCommandInfo.html#HotPreview_SharedModel_Protocol_PreviewCommandInfo_Name
  commentId: P:HotPreview.SharedModel.Protocol.PreviewCommandInfo.Name
  fullName: HotPreview.SharedModel.Protocol.PreviewCommandInfo.Name
  nameWithType: PreviewCommandInfo.Name
- uid: HotPreview.SharedModel.Protocol.PreviewCommandInfo.Name*
  name: Name
  href: api/HotPreview.SharedModel.Protocol.PreviewCommandInfo.html#HotPreview_SharedModel_Protocol_PreviewCommandInfo_Name_
  commentId: Overload:HotPreview.SharedModel.Protocol.PreviewCommandInfo.Name
  isSpec: "True"
  fullName: HotPreview.SharedModel.Protocol.PreviewCommandInfo.Name
  nameWithType: PreviewCommandInfo.Name
- uid: HotPreview.SharedModel.Protocol.PreviewInfo
  name: PreviewInfo
  href: api/HotPreview.SharedModel.Protocol.PreviewInfo.html
  commentId: T:HotPreview.SharedModel.Protocol.PreviewInfo
  fullName: HotPreview.SharedModel.Protocol.PreviewInfo
  nameWithType: PreviewInfo
- uid: HotPreview.SharedModel.Protocol.PreviewInfo.#ctor(System.String,System.String,System.String,System.Boolean)
  name: PreviewInfo(string, string, string?, bool)
  href: api/HotPreview.SharedModel.Protocol.PreviewInfo.html#HotPreview_SharedModel_Protocol_PreviewInfo__ctor_System_String_System_String_System_String_System_Boolean_
  commentId: M:HotPreview.SharedModel.Protocol.PreviewInfo.#ctor(System.String,System.String,System.String,System.Boolean)
  name.vb: New(String, String, String, Boolean)
  fullName: HotPreview.SharedModel.Protocol.PreviewInfo.PreviewInfo(string, string, string?, bool)
  fullName.vb: HotPreview.SharedModel.Protocol.PreviewInfo.New(String, String, String, Boolean)
  nameWithType: PreviewInfo.PreviewInfo(string, string, string?, bool)
  nameWithType.vb: PreviewInfo.New(String, String, String, Boolean)
- uid: HotPreview.SharedModel.Protocol.PreviewInfo.#ctor*
  name: PreviewInfo
  href: api/HotPreview.SharedModel.Protocol.PreviewInfo.html#HotPreview_SharedModel_Protocol_PreviewInfo__ctor_
  commentId: Overload:HotPreview.SharedModel.Protocol.PreviewInfo.#ctor
  isSpec: "True"
  name.vb: New
  fullName: HotPreview.SharedModel.Protocol.PreviewInfo.PreviewInfo
  fullName.vb: HotPreview.SharedModel.Protocol.PreviewInfo.New
  nameWithType: PreviewInfo.PreviewInfo
  nameWithType.vb: PreviewInfo.New
- uid: HotPreview.SharedModel.Protocol.PreviewInfo.DisplayName
  name: DisplayName
  href: api/HotPreview.SharedModel.Protocol.PreviewInfo.html#HotPreview_SharedModel_Protocol_PreviewInfo_DisplayName
  commentId: P:HotPreview.SharedModel.Protocol.PreviewInfo.DisplayName
  fullName: HotPreview.SharedModel.Protocol.PreviewInfo.DisplayName
  nameWithType: PreviewInfo.DisplayName
- uid: HotPreview.SharedModel.Protocol.PreviewInfo.DisplayName*
  name: DisplayName
  href: api/HotPreview.SharedModel.Protocol.PreviewInfo.html#HotPreview_SharedModel_Protocol_PreviewInfo_DisplayName_
  commentId: Overload:HotPreview.SharedModel.Protocol.PreviewInfo.DisplayName
  isSpec: "True"
  fullName: HotPreview.SharedModel.Protocol.PreviewInfo.DisplayName
  nameWithType: PreviewInfo.DisplayName
- uid: HotPreview.SharedModel.Protocol.PreviewInfo.IsAutoGenerated
  name: IsAutoGenerated
  href: api/HotPreview.SharedModel.Protocol.PreviewInfo.html#HotPreview_SharedModel_Protocol_PreviewInfo_IsAutoGenerated
  commentId: P:HotPreview.SharedModel.Protocol.PreviewInfo.IsAutoGenerated
  fullName: HotPreview.SharedModel.Protocol.PreviewInfo.IsAutoGenerated
  nameWithType: PreviewInfo.IsAutoGenerated
- uid: HotPreview.SharedModel.Protocol.PreviewInfo.IsAutoGenerated*
  name: IsAutoGenerated
  href: api/HotPreview.SharedModel.Protocol.PreviewInfo.html#HotPreview_SharedModel_Protocol_PreviewInfo_IsAutoGenerated_
  commentId: Overload:HotPreview.SharedModel.Protocol.PreviewInfo.IsAutoGenerated
  isSpec: "True"
  fullName: HotPreview.SharedModel.Protocol.PreviewInfo.IsAutoGenerated
  nameWithType: PreviewInfo.IsAutoGenerated
- uid: HotPreview.SharedModel.Protocol.PreviewInfo.Name
  name: Name
  href: api/HotPreview.SharedModel.Protocol.PreviewInfo.html#HotPreview_SharedModel_Protocol_PreviewInfo_Name
  commentId: P:HotPreview.SharedModel.Protocol.PreviewInfo.Name
  fullName: HotPreview.SharedModel.Protocol.PreviewInfo.Name
  nameWithType: PreviewInfo.Name
- uid: HotPreview.SharedModel.Protocol.PreviewInfo.Name*
  name: Name
  href: api/HotPreview.SharedModel.Protocol.PreviewInfo.html#HotPreview_SharedModel_Protocol_PreviewInfo_Name_
  commentId: Overload:HotPreview.SharedModel.Protocol.PreviewInfo.Name
  isSpec: "True"
  fullName: HotPreview.SharedModel.Protocol.PreviewInfo.Name
  nameWithType: PreviewInfo.Name
- uid: HotPreview.SharedModel.Protocol.PreviewInfo.PreviewType
  name: PreviewType
  href: api/HotPreview.SharedModel.Protocol.PreviewInfo.html#HotPreview_SharedModel_Protocol_PreviewInfo_PreviewType
  commentId: P:HotPreview.SharedModel.Protocol.PreviewInfo.PreviewType
  fullName: HotPreview.SharedModel.Protocol.PreviewInfo.PreviewType
  nameWithType: PreviewInfo.PreviewType
- uid: HotPreview.SharedModel.Protocol.PreviewInfo.PreviewType*
  name: PreviewType
  href: api/HotPreview.SharedModel.Protocol.PreviewInfo.html#HotPreview_SharedModel_Protocol_PreviewInfo_PreviewType_
  commentId: Overload:HotPreview.SharedModel.Protocol.PreviewInfo.PreviewType
  isSpec: "True"
  fullName: HotPreview.SharedModel.Protocol.PreviewInfo.PreviewType
  nameWithType: PreviewInfo.PreviewType
- uid: HotPreview.SharedModel.Protocol.PreviewTypeInfo
  name: PreviewTypeInfo
  href: api/HotPreview.SharedModel.Protocol.PreviewTypeInfo.html
  commentId: T:HotPreview.SharedModel.Protocol.PreviewTypeInfo
  fullName: HotPreview.SharedModel.Protocol.PreviewTypeInfo
  nameWithType: PreviewTypeInfo
- uid: HotPreview.SharedModel.Protocol.PreviewTypeInfo.Class
  name: Class
  href: api/HotPreview.SharedModel.Protocol.PreviewTypeInfo.html#HotPreview_SharedModel_Protocol_PreviewTypeInfo_Class
  commentId: F:HotPreview.SharedModel.Protocol.PreviewTypeInfo.Class
  fullName: HotPreview.SharedModel.Protocol.PreviewTypeInfo.Class
  nameWithType: PreviewTypeInfo.Class
- uid: HotPreview.SharedModel.Protocol.PreviewTypeInfo.StaticMethod
  name: StaticMethod
  href: api/HotPreview.SharedModel.Protocol.PreviewTypeInfo.html#HotPreview_SharedModel_Protocol_PreviewTypeInfo_StaticMethod
  commentId: F:HotPreview.SharedModel.Protocol.PreviewTypeInfo.StaticMethod
  fullName: HotPreview.SharedModel.Protocol.PreviewTypeInfo.StaticMethod
  nameWithType: PreviewTypeInfo.StaticMethod
- uid: HotPreview.SharedModel.Protocol.UIComponentInfo
  name: UIComponentInfo
  href: api/HotPreview.SharedModel.Protocol.UIComponentInfo.html
  commentId: T:HotPreview.SharedModel.Protocol.UIComponentInfo
  fullName: HotPreview.SharedModel.Protocol.UIComponentInfo
  nameWithType: UIComponentInfo
- uid: HotPreview.SharedModel.Protocol.UIComponentInfo.#ctor(System.String,System.String,System.String,HotPreview.SharedModel.Protocol.PreviewInfo[])
  name: UIComponentInfo(string, string, string?, PreviewInfo[])
  href: api/HotPreview.SharedModel.Protocol.UIComponentInfo.html#HotPreview_SharedModel_Protocol_UIComponentInfo__ctor_System_String_System_String_System_String_HotPreview_SharedModel_Protocol_PreviewInfo___
  commentId: M:HotPreview.SharedModel.Protocol.UIComponentInfo.#ctor(System.String,System.String,System.String,HotPreview.SharedModel.Protocol.PreviewInfo[])
  name.vb: New(String, String, String, PreviewInfo())
  fullName: HotPreview.SharedModel.Protocol.UIComponentInfo.UIComponentInfo(string, string, string?, HotPreview.SharedModel.Protocol.PreviewInfo[])
  fullName.vb: HotPreview.SharedModel.Protocol.UIComponentInfo.New(String, String, String, HotPreview.SharedModel.Protocol.PreviewInfo())
  nameWithType: UIComponentInfo.UIComponentInfo(string, string, string?, PreviewInfo[])
  nameWithType.vb: UIComponentInfo.New(String, String, String, PreviewInfo())
- uid: HotPreview.SharedModel.Protocol.UIComponentInfo.#ctor*
  name: UIComponentInfo
  href: api/HotPreview.SharedModel.Protocol.UIComponentInfo.html#HotPreview_SharedModel_Protocol_UIComponentInfo__ctor_
  commentId: Overload:HotPreview.SharedModel.Protocol.UIComponentInfo.#ctor
  isSpec: "True"
  name.vb: New
  fullName: HotPreview.SharedModel.Protocol.UIComponentInfo.UIComponentInfo
  fullName.vb: HotPreview.SharedModel.Protocol.UIComponentInfo.New
  nameWithType: UIComponentInfo.UIComponentInfo
  nameWithType.vb: UIComponentInfo.New
- uid: HotPreview.SharedModel.Protocol.UIComponentInfo.DisplayName
  name: DisplayName
  href: api/HotPreview.SharedModel.Protocol.UIComponentInfo.html#HotPreview_SharedModel_Protocol_UIComponentInfo_DisplayName
  commentId: P:HotPreview.SharedModel.Protocol.UIComponentInfo.DisplayName
  fullName: HotPreview.SharedModel.Protocol.UIComponentInfo.DisplayName
  nameWithType: UIComponentInfo.DisplayName
- uid: HotPreview.SharedModel.Protocol.UIComponentInfo.DisplayName*
  name: DisplayName
  href: api/HotPreview.SharedModel.Protocol.UIComponentInfo.html#HotPreview_SharedModel_Protocol_UIComponentInfo_DisplayName_
  commentId: Overload:HotPreview.SharedModel.Protocol.UIComponentInfo.DisplayName
  isSpec: "True"
  fullName: HotPreview.SharedModel.Protocol.UIComponentInfo.DisplayName
  nameWithType: UIComponentInfo.DisplayName
- uid: HotPreview.SharedModel.Protocol.UIComponentInfo.Name
  name: Name
  href: api/HotPreview.SharedModel.Protocol.UIComponentInfo.html#HotPreview_SharedModel_Protocol_UIComponentInfo_Name
  commentId: P:HotPreview.SharedModel.Protocol.UIComponentInfo.Name
  fullName: HotPreview.SharedModel.Protocol.UIComponentInfo.Name
  nameWithType: UIComponentInfo.Name
- uid: HotPreview.SharedModel.Protocol.UIComponentInfo.Name*
  name: Name
  href: api/HotPreview.SharedModel.Protocol.UIComponentInfo.html#HotPreview_SharedModel_Protocol_UIComponentInfo_Name_
  commentId: Overload:HotPreview.SharedModel.Protocol.UIComponentInfo.Name
  isSpec: "True"
  fullName: HotPreview.SharedModel.Protocol.UIComponentInfo.Name
  nameWithType: UIComponentInfo.Name
- uid: HotPreview.SharedModel.Protocol.UIComponentInfo.Previews
  name: Previews
  href: api/HotPreview.SharedModel.Protocol.UIComponentInfo.html#HotPreview_SharedModel_Protocol_UIComponentInfo_Previews
  commentId: P:HotPreview.SharedModel.Protocol.UIComponentInfo.Previews
  fullName: HotPreview.SharedModel.Protocol.UIComponentInfo.Previews
  nameWithType: UIComponentInfo.Previews
- uid: HotPreview.SharedModel.Protocol.UIComponentInfo.Previews*
  name: Previews
  href: api/HotPreview.SharedModel.Protocol.UIComponentInfo.html#HotPreview_SharedModel_Protocol_UIComponentInfo_Previews_
  commentId: Overload:HotPreview.SharedModel.Protocol.UIComponentInfo.Previews
  isSpec: "True"
  fullName: HotPreview.SharedModel.Protocol.UIComponentInfo.Previews
  nameWithType: UIComponentInfo.Previews
- uid: HotPreview.SharedModel.Protocol.UIComponentInfo.UIComponentKindInfo
  name: UIComponentKindInfo
  href: api/HotPreview.SharedModel.Protocol.UIComponentInfo.html#HotPreview_SharedModel_Protocol_UIComponentInfo_UIComponentKindInfo
  commentId: P:HotPreview.SharedModel.Protocol.UIComponentInfo.UIComponentKindInfo
  fullName: HotPreview.SharedModel.Protocol.UIComponentInfo.UIComponentKindInfo
  nameWithType: UIComponentInfo.UIComponentKindInfo
- uid: HotPreview.SharedModel.Protocol.UIComponentInfo.UIComponentKindInfo*
  name: UIComponentKindInfo
  href: api/HotPreview.SharedModel.Protocol.UIComponentInfo.html#HotPreview_SharedModel_Protocol_UIComponentInfo_UIComponentKindInfo_
  commentId: Overload:HotPreview.SharedModel.Protocol.UIComponentInfo.UIComponentKindInfo
  isSpec: "True"
  fullName: HotPreview.SharedModel.Protocol.UIComponentInfo.UIComponentKindInfo
  nameWithType: UIComponentInfo.UIComponentKindInfo
- uid: HotPreview.SharedModel.Protocol.UIComponentKindInfo
  name: UIComponentKindInfo
  href: api/HotPreview.SharedModel.Protocol.UIComponentKindInfo.html
  commentId: T:HotPreview.SharedModel.Protocol.UIComponentKindInfo
  fullName: HotPreview.SharedModel.Protocol.UIComponentKindInfo
  nameWithType: UIComponentKindInfo
- uid: HotPreview.SharedModel.Protocol.UIComponentKindInfo.Control
  name: Control
  href: api/HotPreview.SharedModel.Protocol.UIComponentKindInfo.html#HotPreview_SharedModel_Protocol_UIComponentKindInfo_Control
  commentId: F:HotPreview.SharedModel.Protocol.UIComponentKindInfo.Control
  fullName: HotPreview.SharedModel.Protocol.UIComponentKindInfo.Control
  nameWithType: UIComponentKindInfo.Control
- uid: HotPreview.SharedModel.Protocol.UIComponentKindInfo.FromUIComponentKind(HotPreview.SharedModel.UIComponentKind)
  name: FromUIComponentKind(UIComponentKind)
  href: api/HotPreview.SharedModel.Protocol.UIComponentKindInfo.html#HotPreview_SharedModel_Protocol_UIComponentKindInfo_FromUIComponentKind_HotPreview_SharedModel_UIComponentKind_
  commentId: M:HotPreview.SharedModel.Protocol.UIComponentKindInfo.FromUIComponentKind(HotPreview.SharedModel.UIComponentKind)
  fullName: HotPreview.SharedModel.Protocol.UIComponentKindInfo.FromUIComponentKind(HotPreview.SharedModel.UIComponentKind)
  nameWithType: UIComponentKindInfo.FromUIComponentKind(UIComponentKind)
- uid: HotPreview.SharedModel.Protocol.UIComponentKindInfo.FromUIComponentKind*
  name: FromUIComponentKind
  href: api/HotPreview.SharedModel.Protocol.UIComponentKindInfo.html#HotPreview_SharedModel_Protocol_UIComponentKindInfo_FromUIComponentKind_
  commentId: Overload:HotPreview.SharedModel.Protocol.UIComponentKindInfo.FromUIComponentKind
  isSpec: "True"
  fullName: HotPreview.SharedModel.Protocol.UIComponentKindInfo.FromUIComponentKind
  nameWithType: UIComponentKindInfo.FromUIComponentKind
- uid: HotPreview.SharedModel.Protocol.UIComponentKindInfo.Page
  name: Page
  href: api/HotPreview.SharedModel.Protocol.UIComponentKindInfo.html#HotPreview_SharedModel_Protocol_UIComponentKindInfo_Page
  commentId: F:HotPreview.SharedModel.Protocol.UIComponentKindInfo.Page
  fullName: HotPreview.SharedModel.Protocol.UIComponentKindInfo.Page
  nameWithType: UIComponentKindInfo.Page
- uid: HotPreview.SharedModel.Protocol.UIComponentKindInfo.ToUIComponentKind(System.String)
  name: ToUIComponentKind(string)
  href: api/HotPreview.SharedModel.Protocol.UIComponentKindInfo.html#HotPreview_SharedModel_Protocol_UIComponentKindInfo_ToUIComponentKind_System_String_
  commentId: M:HotPreview.SharedModel.Protocol.UIComponentKindInfo.ToUIComponentKind(System.String)
  name.vb: ToUIComponentKind(String)
  fullName: HotPreview.SharedModel.Protocol.UIComponentKindInfo.ToUIComponentKind(string)
  fullName.vb: HotPreview.SharedModel.Protocol.UIComponentKindInfo.ToUIComponentKind(String)
  nameWithType: UIComponentKindInfo.ToUIComponentKind(string)
  nameWithType.vb: UIComponentKindInfo.ToUIComponentKind(String)
- uid: HotPreview.SharedModel.Protocol.UIComponentKindInfo.ToUIComponentKind*
  name: ToUIComponentKind
  href: api/HotPreview.SharedModel.Protocol.UIComponentKindInfo.html#HotPreview_SharedModel_Protocol_UIComponentKindInfo_ToUIComponentKind_
  commentId: Overload:HotPreview.SharedModel.Protocol.UIComponentKindInfo.ToUIComponentKind
  isSpec: "True"
  fullName: HotPreview.SharedModel.Protocol.UIComponentKindInfo.ToUIComponentKind
  nameWithType: UIComponentKindInfo.ToUIComponentKind
- uid: HotPreview.SharedModel.Protocol.UIComponentKindInfo.Unknown
  name: Unknown
  href: api/HotPreview.SharedModel.Protocol.UIComponentKindInfo.html#HotPreview_SharedModel_Protocol_UIComponentKindInfo_Unknown
  commentId: F:HotPreview.SharedModel.Protocol.UIComponentKindInfo.Unknown
  fullName: HotPreview.SharedModel.Protocol.UIComponentKindInfo.Unknown
  nameWithType: UIComponentKindInfo.Unknown
- uid: HotPreview.SharedModel.StringUtilities
  name: StringUtilities
  href: api/HotPreview.SharedModel.StringUtilities.html
  commentId: T:HotPreview.SharedModel.StringUtilities
  fullName: HotPreview.SharedModel.StringUtilities
  nameWithType: StringUtilities
- uid: HotPreview.SharedModel.StringUtilities.StartCase(System.String)
  name: StartCase(string)
  href: api/HotPreview.SharedModel.StringUtilities.html#HotPreview_SharedModel_StringUtilities_StartCase_System_String_
  commentId: M:HotPreview.SharedModel.StringUtilities.StartCase(System.String)
  name.vb: StartCase(String)
  fullName: HotPreview.SharedModel.StringUtilities.StartCase(string)
  fullName.vb: HotPreview.SharedModel.StringUtilities.StartCase(String)
  nameWithType: StringUtilities.StartCase(string)
  nameWithType.vb: StringUtilities.StartCase(String)
- uid: HotPreview.SharedModel.StringUtilities.StartCase*
  name: StartCase
  href: api/HotPreview.SharedModel.StringUtilities.html#HotPreview_SharedModel_StringUtilities_StartCase_
  commentId: Overload:HotPreview.SharedModel.StringUtilities.StartCase
  isSpec: "True"
  fullName: HotPreview.SharedModel.StringUtilities.StartCase
  nameWithType: StringUtilities.StartCase
- uid: HotPreview.SharedModel.UIComponentBaseTypes
  name: UIComponentBaseTypes
  href: api/HotPreview.SharedModel.UIComponentBaseTypes.html
  commentId: T:HotPreview.SharedModel.UIComponentBaseTypes
  fullName: HotPreview.SharedModel.UIComponentBaseTypes
  nameWithType: UIComponentBaseTypes
- uid: HotPreview.SharedModel.UIComponentBaseTypes.AddBaseType(System.String,System.String)
  name: AddBaseType(string, string)
  href: api/HotPreview.SharedModel.UIComponentBaseTypes.html#HotPreview_SharedModel_UIComponentBaseTypes_AddBaseType_System_String_System_String_
  commentId: M:HotPreview.SharedModel.UIComponentBaseTypes.AddBaseType(System.String,System.String)
  name.vb: AddBaseType(String, String)
  fullName: HotPreview.SharedModel.UIComponentBaseTypes.AddBaseType(string, string)
  fullName.vb: HotPreview.SharedModel.UIComponentBaseTypes.AddBaseType(String, String)
  nameWithType: UIComponentBaseTypes.AddBaseType(string, string)
  nameWithType.vb: UIComponentBaseTypes.AddBaseType(String, String)
- uid: HotPreview.SharedModel.UIComponentBaseTypes.AddBaseType*
  name: AddBaseType
  href: api/HotPreview.SharedModel.UIComponentBaseTypes.html#HotPreview_SharedModel_UIComponentBaseTypes_AddBaseType_
  commentId: Overload:HotPreview.SharedModel.UIComponentBaseTypes.AddBaseType
  isSpec: "True"
  fullName: HotPreview.SharedModel.UIComponentBaseTypes.AddBaseType
  nameWithType: UIComponentBaseTypes.AddBaseType
- uid: HotPreview.SharedModel.UIComponentBaseTypes.IsUIComponentBaseType(System.String)
  name: IsUIComponentBaseType(string)
  href: api/HotPreview.SharedModel.UIComponentBaseTypes.html#HotPreview_SharedModel_UIComponentBaseTypes_IsUIComponentBaseType_System_String_
  commentId: M:HotPreview.SharedModel.UIComponentBaseTypes.IsUIComponentBaseType(System.String)
  name.vb: IsUIComponentBaseType(String)
  fullName: HotPreview.SharedModel.UIComponentBaseTypes.IsUIComponentBaseType(string)
  fullName.vb: HotPreview.SharedModel.UIComponentBaseTypes.IsUIComponentBaseType(String)
  nameWithType: UIComponentBaseTypes.IsUIComponentBaseType(string)
  nameWithType.vb: UIComponentBaseTypes.IsUIComponentBaseType(String)
- uid: HotPreview.SharedModel.UIComponentBaseTypes.IsUIComponentBaseType*
  name: IsUIComponentBaseType
  href: api/HotPreview.SharedModel.UIComponentBaseTypes.html#HotPreview_SharedModel_UIComponentBaseTypes_IsUIComponentBaseType_
  commentId: Overload:HotPreview.SharedModel.UIComponentBaseTypes.IsUIComponentBaseType
  isSpec: "True"
  fullName: HotPreview.SharedModel.UIComponentBaseTypes.IsUIComponentBaseType
  nameWithType: UIComponentBaseTypes.IsUIComponentBaseType
- uid: HotPreview.SharedModel.UIComponentBase`1
  name: UIComponentBase<TPreview>
  href: api/HotPreview.SharedModel.UIComponentBase-1.html
  commentId: T:HotPreview.SharedModel.UIComponentBase`1
  name.vb: UIComponentBase(Of TPreview)
  fullName: HotPreview.SharedModel.UIComponentBase<TPreview>
  fullName.vb: HotPreview.SharedModel.UIComponentBase(Of TPreview)
  nameWithType: UIComponentBase<TPreview>
  nameWithType.vb: UIComponentBase(Of TPreview)
- uid: HotPreview.SharedModel.UIComponentBase`1.#ctor(HotPreview.SharedModel.UIComponentKind,System.String,System.Collections.Generic.IReadOnlyList{`0})
  name: UIComponentBase(UIComponentKind, string?, IReadOnlyList<TPreview>)
  href: api/HotPreview.SharedModel.UIComponentBase-1.html#HotPreview_SharedModel_UIComponentBase_1__ctor_HotPreview_SharedModel_UIComponentKind_System_String_System_Collections_Generic_IReadOnlyList__0__
  commentId: M:HotPreview.SharedModel.UIComponentBase`1.#ctor(HotPreview.SharedModel.UIComponentKind,System.String,System.Collections.Generic.IReadOnlyList{`0})
  name.vb: New(UIComponentKind, String, IReadOnlyList(Of TPreview))
  fullName: HotPreview.SharedModel.UIComponentBase<TPreview>.UIComponentBase(HotPreview.SharedModel.UIComponentKind, string?, System.Collections.Generic.IReadOnlyList<TPreview>)
  fullName.vb: HotPreview.SharedModel.UIComponentBase(Of TPreview).New(HotPreview.SharedModel.UIComponentKind, String, System.Collections.Generic.IReadOnlyList(Of TPreview))
  nameWithType: UIComponentBase<TPreview>.UIComponentBase(UIComponentKind, string?, IReadOnlyList<TPreview>)
  nameWithType.vb: UIComponentBase(Of TPreview).New(UIComponentKind, String, IReadOnlyList(Of TPreview))
- uid: HotPreview.SharedModel.UIComponentBase`1.#ctor*
  name: UIComponentBase
  href: api/HotPreview.SharedModel.UIComponentBase-1.html#HotPreview_SharedModel_UIComponentBase_1__ctor_
  commentId: Overload:HotPreview.SharedModel.UIComponentBase`1.#ctor
  isSpec: "True"
  name.vb: New
  fullName: HotPreview.SharedModel.UIComponentBase<TPreview>.UIComponentBase
  fullName.vb: HotPreview.SharedModel.UIComponentBase(Of TPreview).New
  nameWithType: UIComponentBase<TPreview>.UIComponentBase
  nameWithType.vb: UIComponentBase(Of TPreview).New
- uid: HotPreview.SharedModel.UIComponentBase`1.Category
  name: Category
  href: api/HotPreview.SharedModel.UIComponentBase-1.html#HotPreview_SharedModel_UIComponentBase_1_Category
  commentId: P:HotPreview.SharedModel.UIComponentBase`1.Category
  fullName: HotPreview.SharedModel.UIComponentBase<TPreview>.Category
  fullName.vb: HotPreview.SharedModel.UIComponentBase(Of TPreview).Category
  nameWithType: UIComponentBase<TPreview>.Category
  nameWithType.vb: UIComponentBase(Of TPreview).Category
- uid: HotPreview.SharedModel.UIComponentBase`1.Category*
  name: Category
  href: api/HotPreview.SharedModel.UIComponentBase-1.html#HotPreview_SharedModel_UIComponentBase_1_Category_
  commentId: Overload:HotPreview.SharedModel.UIComponentBase`1.Category
  isSpec: "True"
  fullName: HotPreview.SharedModel.UIComponentBase<TPreview>.Category
  fullName.vb: HotPreview.SharedModel.UIComponentBase(Of TPreview).Category
  nameWithType: UIComponentBase<TPreview>.Category
  nameWithType.vb: UIComponentBase(Of TPreview).Category
- uid: HotPreview.SharedModel.UIComponentBase`1.DefaultPreview
  name: DefaultPreview
  href: api/HotPreview.SharedModel.UIComponentBase-1.html#HotPreview_SharedModel_UIComponentBase_1_DefaultPreview
  commentId: P:HotPreview.SharedModel.UIComponentBase`1.DefaultPreview
  fullName: HotPreview.SharedModel.UIComponentBase<TPreview>.DefaultPreview
  fullName.vb: HotPreview.SharedModel.UIComponentBase(Of TPreview).DefaultPreview
  nameWithType: UIComponentBase<TPreview>.DefaultPreview
  nameWithType.vb: UIComponentBase(Of TPreview).DefaultPreview
- uid: HotPreview.SharedModel.UIComponentBase`1.DefaultPreview*
  name: DefaultPreview
  href: api/HotPreview.SharedModel.UIComponentBase-1.html#HotPreview_SharedModel_UIComponentBase_1_DefaultPreview_
  commentId: Overload:HotPreview.SharedModel.UIComponentBase`1.DefaultPreview
  isSpec: "True"
  fullName: HotPreview.SharedModel.UIComponentBase<TPreview>.DefaultPreview
  fullName.vb: HotPreview.SharedModel.UIComponentBase(Of TPreview).DefaultPreview
  nameWithType: UIComponentBase<TPreview>.DefaultPreview
  nameWithType.vb: UIComponentBase(Of TPreview).DefaultPreview
- uid: HotPreview.SharedModel.UIComponentBase`1.DisplayName
  name: DisplayName
  href: api/HotPreview.SharedModel.UIComponentBase-1.html#HotPreview_SharedModel_UIComponentBase_1_DisplayName
  commentId: P:HotPreview.SharedModel.UIComponentBase`1.DisplayName
  fullName: HotPreview.SharedModel.UIComponentBase<TPreview>.DisplayName
  fullName.vb: HotPreview.SharedModel.UIComponentBase(Of TPreview).DisplayName
  nameWithType: UIComponentBase<TPreview>.DisplayName
  nameWithType.vb: UIComponentBase(Of TPreview).DisplayName
- uid: HotPreview.SharedModel.UIComponentBase`1.DisplayName*
  name: DisplayName
  href: api/HotPreview.SharedModel.UIComponentBase-1.html#HotPreview_SharedModel_UIComponentBase_1_DisplayName_
  commentId: Overload:HotPreview.SharedModel.UIComponentBase`1.DisplayName
  isSpec: "True"
  fullName: HotPreview.SharedModel.UIComponentBase<TPreview>.DisplayName
  fullName.vb: HotPreview.SharedModel.UIComponentBase(Of TPreview).DisplayName
  nameWithType: UIComponentBase<TPreview>.DisplayName
  nameWithType.vb: UIComponentBase(Of TPreview).DisplayName
- uid: HotPreview.SharedModel.UIComponentBase`1.DisplayNameOverride
  name: DisplayNameOverride
  href: api/HotPreview.SharedModel.UIComponentBase-1.html#HotPreview_SharedModel_UIComponentBase_1_DisplayNameOverride
  commentId: P:HotPreview.SharedModel.UIComponentBase`1.DisplayNameOverride
  fullName: HotPreview.SharedModel.UIComponentBase<TPreview>.DisplayNameOverride
  fullName.vb: HotPreview.SharedModel.UIComponentBase(Of TPreview).DisplayNameOverride
  nameWithType: UIComponentBase<TPreview>.DisplayNameOverride
  nameWithType.vb: UIComponentBase(Of TPreview).DisplayNameOverride
- uid: HotPreview.SharedModel.UIComponentBase`1.DisplayNameOverride*
  name: DisplayNameOverride
  href: api/HotPreview.SharedModel.UIComponentBase-1.html#HotPreview_SharedModel_UIComponentBase_1_DisplayNameOverride_
  commentId: Overload:HotPreview.SharedModel.UIComponentBase`1.DisplayNameOverride
  isSpec: "True"
  fullName: HotPreview.SharedModel.UIComponentBase<TPreview>.DisplayNameOverride
  fullName.vb: HotPreview.SharedModel.UIComponentBase(Of TPreview).DisplayNameOverride
  nameWithType: UIComponentBase<TPreview>.DisplayNameOverride
  nameWithType.vb: UIComponentBase(Of TPreview).DisplayNameOverride
- uid: HotPreview.SharedModel.UIComponentBase`1.GetPreview(System.String)
  name: GetPreview(string)
  href: api/HotPreview.SharedModel.UIComponentBase-1.html#HotPreview_SharedModel_UIComponentBase_1_GetPreview_System_String_
  commentId: M:HotPreview.SharedModel.UIComponentBase`1.GetPreview(System.String)
  name.vb: GetPreview(String)
  fullName: HotPreview.SharedModel.UIComponentBase<TPreview>.GetPreview(string)
  fullName.vb: HotPreview.SharedModel.UIComponentBase(Of TPreview).GetPreview(String)
  nameWithType: UIComponentBase<TPreview>.GetPreview(string)
  nameWithType.vb: UIComponentBase(Of TPreview).GetPreview(String)
- uid: HotPreview.SharedModel.UIComponentBase`1.GetPreview*
  name: GetPreview
  href: api/HotPreview.SharedModel.UIComponentBase-1.html#HotPreview_SharedModel_UIComponentBase_1_GetPreview_
  commentId: Overload:HotPreview.SharedModel.UIComponentBase`1.GetPreview
  isSpec: "True"
  fullName: HotPreview.SharedModel.UIComponentBase<TPreview>.GetPreview
  fullName.vb: HotPreview.SharedModel.UIComponentBase(Of TPreview).GetPreview
  nameWithType: UIComponentBase<TPreview>.GetPreview
  nameWithType.vb: UIComponentBase(Of TPreview).GetPreview
- uid: HotPreview.SharedModel.UIComponentBase`1.GetUpdatedPreviews(`0)
  name: GetUpdatedPreviews(TPreview)
  href: api/HotPreview.SharedModel.UIComponentBase-1.html#HotPreview_SharedModel_UIComponentBase_1_GetUpdatedPreviews__0_
  commentId: M:HotPreview.SharedModel.UIComponentBase`1.GetUpdatedPreviews(`0)
  fullName: HotPreview.SharedModel.UIComponentBase<TPreview>.GetUpdatedPreviews(TPreview)
  fullName.vb: HotPreview.SharedModel.UIComponentBase(Of TPreview).GetUpdatedPreviews(TPreview)
  nameWithType: UIComponentBase<TPreview>.GetUpdatedPreviews(TPreview)
  nameWithType.vb: UIComponentBase(Of TPreview).GetUpdatedPreviews(TPreview)
- uid: HotPreview.SharedModel.UIComponentBase`1.GetUpdatedPreviews*
  name: GetUpdatedPreviews
  href: api/HotPreview.SharedModel.UIComponentBase-1.html#HotPreview_SharedModel_UIComponentBase_1_GetUpdatedPreviews_
  commentId: Overload:HotPreview.SharedModel.UIComponentBase`1.GetUpdatedPreviews
  isSpec: "True"
  fullName: HotPreview.SharedModel.UIComponentBase<TPreview>.GetUpdatedPreviews
  fullName.vb: HotPreview.SharedModel.UIComponentBase(Of TPreview).GetUpdatedPreviews
  nameWithType: UIComponentBase<TPreview>.GetUpdatedPreviews
  nameWithType.vb: UIComponentBase(Of TPreview).GetUpdatedPreviews
- uid: HotPreview.SharedModel.UIComponentBase`1.HasMultiplePreviews
  name: HasMultiplePreviews
  href: api/HotPreview.SharedModel.UIComponentBase-1.html#HotPreview_SharedModel_UIComponentBase_1_HasMultiplePreviews
  commentId: P:HotPreview.SharedModel.UIComponentBase`1.HasMultiplePreviews
  fullName: HotPreview.SharedModel.UIComponentBase<TPreview>.HasMultiplePreviews
  fullName.vb: HotPreview.SharedModel.UIComponentBase(Of TPreview).HasMultiplePreviews
  nameWithType: UIComponentBase<TPreview>.HasMultiplePreviews
  nameWithType.vb: UIComponentBase(Of TPreview).HasMultiplePreviews
- uid: HotPreview.SharedModel.UIComponentBase`1.HasMultiplePreviews*
  name: HasMultiplePreviews
  href: api/HotPreview.SharedModel.UIComponentBase-1.html#HotPreview_SharedModel_UIComponentBase_1_HasMultiplePreviews_
  commentId: Overload:HotPreview.SharedModel.UIComponentBase`1.HasMultiplePreviews
  isSpec: "True"
  fullName: HotPreview.SharedModel.UIComponentBase<TPreview>.HasMultiplePreviews
  fullName.vb: HotPreview.SharedModel.UIComponentBase(Of TPreview).HasMultiplePreviews
  nameWithType: UIComponentBase<TPreview>.HasMultiplePreviews
  nameWithType.vb: UIComponentBase(Of TPreview).HasMultiplePreviews
- uid: HotPreview.SharedModel.UIComponentBase`1.HasNoPreviews
  name: HasNoPreviews
  href: api/HotPreview.SharedModel.UIComponentBase-1.html#HotPreview_SharedModel_UIComponentBase_1_HasNoPreviews
  commentId: P:HotPreview.SharedModel.UIComponentBase`1.HasNoPreviews
  fullName: HotPreview.SharedModel.UIComponentBase<TPreview>.HasNoPreviews
  fullName.vb: HotPreview.SharedModel.UIComponentBase(Of TPreview).HasNoPreviews
  nameWithType: UIComponentBase<TPreview>.HasNoPreviews
  nameWithType.vb: UIComponentBase(Of TPreview).HasNoPreviews
- uid: HotPreview.SharedModel.UIComponentBase`1.HasNoPreviews*
  name: HasNoPreviews
  href: api/HotPreview.SharedModel.UIComponentBase-1.html#HotPreview_SharedModel_UIComponentBase_1_HasNoPreviews_
  commentId: Overload:HotPreview.SharedModel.UIComponentBase`1.HasNoPreviews
  isSpec: "True"
  fullName: HotPreview.SharedModel.UIComponentBase<TPreview>.HasNoPreviews
  fullName.vb: HotPreview.SharedModel.UIComponentBase(Of TPreview).HasNoPreviews
  nameWithType: UIComponentBase<TPreview>.HasNoPreviews
  nameWithType.vb: UIComponentBase(Of TPreview).HasNoPreviews
- uid: HotPreview.SharedModel.UIComponentBase`1.HasPreview
  name: HasPreview
  href: api/HotPreview.SharedModel.UIComponentBase-1.html#HotPreview_SharedModel_UIComponentBase_1_HasPreview
  commentId: P:HotPreview.SharedModel.UIComponentBase`1.HasPreview
  fullName: HotPreview.SharedModel.UIComponentBase<TPreview>.HasPreview
  fullName.vb: HotPreview.SharedModel.UIComponentBase(Of TPreview).HasPreview
  nameWithType: UIComponentBase<TPreview>.HasPreview
  nameWithType.vb: UIComponentBase(Of TPreview).HasPreview
- uid: HotPreview.SharedModel.UIComponentBase`1.HasPreview*
  name: HasPreview
  href: api/HotPreview.SharedModel.UIComponentBase-1.html#HotPreview_SharedModel_UIComponentBase_1_HasPreview_
  commentId: Overload:HotPreview.SharedModel.UIComponentBase`1.HasPreview
  isSpec: "True"
  fullName: HotPreview.SharedModel.UIComponentBase<TPreview>.HasPreview
  fullName.vb: HotPreview.SharedModel.UIComponentBase(Of TPreview).HasPreview
  nameWithType: UIComponentBase<TPreview>.HasPreview
  nameWithType.vb: UIComponentBase(Of TPreview).HasPreview
- uid: HotPreview.SharedModel.UIComponentBase`1.HasSinglePreview
  name: HasSinglePreview
  href: api/HotPreview.SharedModel.UIComponentBase-1.html#HotPreview_SharedModel_UIComponentBase_1_HasSinglePreview
  commentId: P:HotPreview.SharedModel.UIComponentBase`1.HasSinglePreview
  fullName: HotPreview.SharedModel.UIComponentBase<TPreview>.HasSinglePreview
  fullName.vb: HotPreview.SharedModel.UIComponentBase(Of TPreview).HasSinglePreview
  nameWithType: UIComponentBase<TPreview>.HasSinglePreview
  nameWithType.vb: UIComponentBase(Of TPreview).HasSinglePreview
- uid: HotPreview.SharedModel.UIComponentBase`1.HasSinglePreview*
  name: HasSinglePreview
  href: api/HotPreview.SharedModel.UIComponentBase-1.html#HotPreview_SharedModel_UIComponentBase_1_HasSinglePreview_
  commentId: Overload:HotPreview.SharedModel.UIComponentBase`1.HasSinglePreview
  isSpec: "True"
  fullName: HotPreview.SharedModel.UIComponentBase<TPreview>.HasSinglePreview
  fullName.vb: HotPreview.SharedModel.UIComponentBase(Of TPreview).HasSinglePreview
  nameWithType: UIComponentBase<TPreview>.HasSinglePreview
  nameWithType.vb: UIComponentBase(Of TPreview).HasSinglePreview
- uid: HotPreview.SharedModel.UIComponentBase`1.IsAutoGenerated
  name: IsAutoGenerated
  href: api/HotPreview.SharedModel.UIComponentBase-1.html#HotPreview_SharedModel_UIComponentBase_1_IsAutoGenerated
  commentId: P:HotPreview.SharedModel.UIComponentBase`1.IsAutoGenerated
  fullName: HotPreview.SharedModel.UIComponentBase<TPreview>.IsAutoGenerated
  fullName.vb: HotPreview.SharedModel.UIComponentBase(Of TPreview).IsAutoGenerated
  nameWithType: UIComponentBase<TPreview>.IsAutoGenerated
  nameWithType.vb: UIComponentBase(Of TPreview).IsAutoGenerated
- uid: HotPreview.SharedModel.UIComponentBase`1.IsAutoGenerated*
  name: IsAutoGenerated
  href: api/HotPreview.SharedModel.UIComponentBase-1.html#HotPreview_SharedModel_UIComponentBase_1_IsAutoGenerated_
  commentId: Overload:HotPreview.SharedModel.UIComponentBase`1.IsAutoGenerated
  isSpec: "True"
  fullName: HotPreview.SharedModel.UIComponentBase<TPreview>.IsAutoGenerated
  fullName.vb: HotPreview.SharedModel.UIComponentBase(Of TPreview).IsAutoGenerated
  nameWithType: UIComponentBase<TPreview>.IsAutoGenerated
  nameWithType.vb: UIComponentBase(Of TPreview).IsAutoGenerated
- uid: HotPreview.SharedModel.UIComponentBase`1.Kind
  name: Kind
  href: api/HotPreview.SharedModel.UIComponentBase-1.html#HotPreview_SharedModel_UIComponentBase_1_Kind
  commentId: P:HotPreview.SharedModel.UIComponentBase`1.Kind
  fullName: HotPreview.SharedModel.UIComponentBase<TPreview>.Kind
  fullName.vb: HotPreview.SharedModel.UIComponentBase(Of TPreview).Kind
  nameWithType: UIComponentBase<TPreview>.Kind
  nameWithType.vb: UIComponentBase(Of TPreview).Kind
- uid: HotPreview.SharedModel.UIComponentBase`1.Kind*
  name: Kind
  href: api/HotPreview.SharedModel.UIComponentBase-1.html#HotPreview_SharedModel_UIComponentBase_1_Kind_
  commentId: Overload:HotPreview.SharedModel.UIComponentBase`1.Kind
  isSpec: "True"
  fullName: HotPreview.SharedModel.UIComponentBase<TPreview>.Kind
  fullName.vb: HotPreview.SharedModel.UIComponentBase(Of TPreview).Kind
  nameWithType: UIComponentBase<TPreview>.Kind
  nameWithType.vb: UIComponentBase(Of TPreview).Kind
- uid: HotPreview.SharedModel.UIComponentBase`1.Name
  name: Name
  href: api/HotPreview.SharedModel.UIComponentBase-1.html#HotPreview_SharedModel_UIComponentBase_1_Name
  commentId: P:HotPreview.SharedModel.UIComponentBase`1.Name
  fullName: HotPreview.SharedModel.UIComponentBase<TPreview>.Name
  fullName.vb: HotPreview.SharedModel.UIComponentBase(Of TPreview).Name
  nameWithType: UIComponentBase<TPreview>.Name
  nameWithType.vb: UIComponentBase(Of TPreview).Name
- uid: HotPreview.SharedModel.UIComponentBase`1.Name*
  name: Name
  href: api/HotPreview.SharedModel.UIComponentBase-1.html#HotPreview_SharedModel_UIComponentBase_1_Name_
  commentId: Overload:HotPreview.SharedModel.UIComponentBase`1.Name
  isSpec: "True"
  fullName: HotPreview.SharedModel.UIComponentBase<TPreview>.Name
  fullName.vb: HotPreview.SharedModel.UIComponentBase(Of TPreview).Name
  nameWithType: UIComponentBase<TPreview>.Name
  nameWithType.vb: UIComponentBase(Of TPreview).Name
- uid: HotPreview.SharedModel.UIComponentBase`1.PathIcon
  name: PathIcon
  href: api/HotPreview.SharedModel.UIComponentBase-1.html#HotPreview_SharedModel_UIComponentBase_1_PathIcon
  commentId: P:HotPreview.SharedModel.UIComponentBase`1.PathIcon
  fullName: HotPreview.SharedModel.UIComponentBase<TPreview>.PathIcon
  fullName.vb: HotPreview.SharedModel.UIComponentBase(Of TPreview).PathIcon
  nameWithType: UIComponentBase<TPreview>.PathIcon
  nameWithType.vb: UIComponentBase(Of TPreview).PathIcon
- uid: HotPreview.SharedModel.UIComponentBase`1.PathIcon*
  name: PathIcon
  href: api/HotPreview.SharedModel.UIComponentBase-1.html#HotPreview_SharedModel_UIComponentBase_1_PathIcon_
  commentId: Overload:HotPreview.SharedModel.UIComponentBase`1.PathIcon
  isSpec: "True"
  fullName: HotPreview.SharedModel.UIComponentBase<TPreview>.PathIcon
  fullName.vb: HotPreview.SharedModel.UIComponentBase(Of TPreview).PathIcon
  nameWithType: UIComponentBase<TPreview>.PathIcon
  nameWithType.vb: UIComponentBase(Of TPreview).PathIcon
- uid: HotPreview.SharedModel.UIComponentBase`1.Previews
  name: Previews
  href: api/HotPreview.SharedModel.UIComponentBase-1.html#HotPreview_SharedModel_UIComponentBase_1_Previews
  commentId: P:HotPreview.SharedModel.UIComponentBase`1.Previews
  fullName: HotPreview.SharedModel.UIComponentBase<TPreview>.Previews
  fullName.vb: HotPreview.SharedModel.UIComponentBase(Of TPreview).Previews
  nameWithType: UIComponentBase<TPreview>.Previews
  nameWithType.vb: UIComponentBase(Of TPreview).Previews
- uid: HotPreview.SharedModel.UIComponentBase`1.Previews*
  name: Previews
  href: api/HotPreview.SharedModel.UIComponentBase-1.html#HotPreview_SharedModel_UIComponentBase_1_Previews_
  commentId: Overload:HotPreview.SharedModel.UIComponentBase`1.Previews
  isSpec: "True"
  fullName: HotPreview.SharedModel.UIComponentBase<TPreview>.Previews
  fullName.vb: HotPreview.SharedModel.UIComponentBase(Of TPreview).Previews
  nameWithType: UIComponentBase<TPreview>.Previews
  nameWithType.vb: UIComponentBase(Of TPreview).Previews
- uid: HotPreview.SharedModel.UIComponentBase`1.WithAddedPreview(`0)
  name: WithAddedPreview(TPreview)
  href: api/HotPreview.SharedModel.UIComponentBase-1.html#HotPreview_SharedModel_UIComponentBase_1_WithAddedPreview__0_
  commentId: M:HotPreview.SharedModel.UIComponentBase`1.WithAddedPreview(`0)
  fullName: HotPreview.SharedModel.UIComponentBase<TPreview>.WithAddedPreview(TPreview)
  fullName.vb: HotPreview.SharedModel.UIComponentBase(Of TPreview).WithAddedPreview(TPreview)
  nameWithType: UIComponentBase<TPreview>.WithAddedPreview(TPreview)
  nameWithType.vb: UIComponentBase(Of TPreview).WithAddedPreview(TPreview)
- uid: HotPreview.SharedModel.UIComponentBase`1.WithAddedPreview*
  name: WithAddedPreview
  href: api/HotPreview.SharedModel.UIComponentBase-1.html#HotPreview_SharedModel_UIComponentBase_1_WithAddedPreview_
  commentId: Overload:HotPreview.SharedModel.UIComponentBase`1.WithAddedPreview
  isSpec: "True"
  fullName: HotPreview.SharedModel.UIComponentBase<TPreview>.WithAddedPreview
  fullName.vb: HotPreview.SharedModel.UIComponentBase(Of TPreview).WithAddedPreview
  nameWithType: UIComponentBase<TPreview>.WithAddedPreview
  nameWithType.vb: UIComponentBase(Of TPreview).WithAddedPreview
- uid: HotPreview.SharedModel.UIComponentCategory
  name: UIComponentCategory
  href: api/HotPreview.SharedModel.UIComponentCategory.html
  commentId: T:HotPreview.SharedModel.UIComponentCategory
  fullName: HotPreview.SharedModel.UIComponentCategory
  nameWithType: UIComponentCategory
- uid: HotPreview.SharedModel.UIComponentCategory.#ctor(System.String,System.Collections.Generic.IReadOnlyList{System.String})
  name: UIComponentCategory(string, IReadOnlyList<string>)
  href: api/HotPreview.SharedModel.UIComponentCategory.html#HotPreview_SharedModel_UIComponentCategory__ctor_System_String_System_Collections_Generic_IReadOnlyList_System_String__
  commentId: M:HotPreview.SharedModel.UIComponentCategory.#ctor(System.String,System.Collections.Generic.IReadOnlyList{System.String})
  name.vb: New(String, IReadOnlyList(Of String))
  fullName: HotPreview.SharedModel.UIComponentCategory.UIComponentCategory(string, System.Collections.Generic.IReadOnlyList<string>)
  fullName.vb: HotPreview.SharedModel.UIComponentCategory.New(String, System.Collections.Generic.IReadOnlyList(Of String))
  nameWithType: UIComponentCategory.UIComponentCategory(string, IReadOnlyList<string>)
  nameWithType.vb: UIComponentCategory.New(String, IReadOnlyList(Of String))
- uid: HotPreview.SharedModel.UIComponentCategory.#ctor*
  name: UIComponentCategory
  href: api/HotPreview.SharedModel.UIComponentCategory.html#HotPreview_SharedModel_UIComponentCategory__ctor_
  commentId: Overload:HotPreview.SharedModel.UIComponentCategory.#ctor
  isSpec: "True"
  name.vb: New
  fullName: HotPreview.SharedModel.UIComponentCategory.UIComponentCategory
  fullName.vb: HotPreview.SharedModel.UIComponentCategory.New
  nameWithType: UIComponentCategory.UIComponentCategory
  nameWithType.vb: UIComponentCategory.New
- uid: HotPreview.SharedModel.UIComponentCategory.Name
  name: Name
  href: api/HotPreview.SharedModel.UIComponentCategory.html#HotPreview_SharedModel_UIComponentCategory_Name
  commentId: P:HotPreview.SharedModel.UIComponentCategory.Name
  fullName: HotPreview.SharedModel.UIComponentCategory.Name
  nameWithType: UIComponentCategory.Name
- uid: HotPreview.SharedModel.UIComponentCategory.Name*
  name: Name
  href: api/HotPreview.SharedModel.UIComponentCategory.html#HotPreview_SharedModel_UIComponentCategory_Name_
  commentId: Overload:HotPreview.SharedModel.UIComponentCategory.Name
  isSpec: "True"
  fullName: HotPreview.SharedModel.UIComponentCategory.Name
  nameWithType: UIComponentCategory.Name
- uid: HotPreview.SharedModel.UIComponentCategory.UIComponentNames
  name: UIComponentNames
  href: api/HotPreview.SharedModel.UIComponentCategory.html#HotPreview_SharedModel_UIComponentCategory_UIComponentNames
  commentId: P:HotPreview.SharedModel.UIComponentCategory.UIComponentNames
  fullName: HotPreview.SharedModel.UIComponentCategory.UIComponentNames
  nameWithType: UIComponentCategory.UIComponentNames
- uid: HotPreview.SharedModel.UIComponentCategory.UIComponentNames*
  name: UIComponentNames
  href: api/HotPreview.SharedModel.UIComponentCategory.html#HotPreview_SharedModel_UIComponentCategory_UIComponentNames_
  commentId: Overload:HotPreview.SharedModel.UIComponentCategory.UIComponentNames
  isSpec: "True"
  fullName: HotPreview.SharedModel.UIComponentCategory.UIComponentNames
  nameWithType: UIComponentCategory.UIComponentNames
- uid: HotPreview.SharedModel.UIComponentCategory.WithAddedUIComponentNames(System.Collections.Generic.IReadOnlyList{System.String})
  name: WithAddedUIComponentNames(IReadOnlyList<string>)
  href: api/HotPreview.SharedModel.UIComponentCategory.html#HotPreview_SharedModel_UIComponentCategory_WithAddedUIComponentNames_System_Collections_Generic_IReadOnlyList_System_String__
  commentId: M:HotPreview.SharedModel.UIComponentCategory.WithAddedUIComponentNames(System.Collections.Generic.IReadOnlyList{System.String})
  name.vb: WithAddedUIComponentNames(IReadOnlyList(Of String))
  fullName: HotPreview.SharedModel.UIComponentCategory.WithAddedUIComponentNames(System.Collections.Generic.IReadOnlyList<string>)
  fullName.vb: HotPreview.SharedModel.UIComponentCategory.WithAddedUIComponentNames(System.Collections.Generic.IReadOnlyList(Of String))
  nameWithType: UIComponentCategory.WithAddedUIComponentNames(IReadOnlyList<string>)
  nameWithType.vb: UIComponentCategory.WithAddedUIComponentNames(IReadOnlyList(Of String))
- uid: HotPreview.SharedModel.UIComponentCategory.WithAddedUIComponentNames*
  name: WithAddedUIComponentNames
  href: api/HotPreview.SharedModel.UIComponentCategory.html#HotPreview_SharedModel_UIComponentCategory_WithAddedUIComponentNames_
  commentId: Overload:HotPreview.SharedModel.UIComponentCategory.WithAddedUIComponentNames
  isSpec: "True"
  fullName: HotPreview.SharedModel.UIComponentCategory.WithAddedUIComponentNames
  nameWithType: UIComponentCategory.WithAddedUIComponentNames
- uid: HotPreview.SharedModel.UIComponentKind
  name: UIComponentKind
  href: api/HotPreview.SharedModel.UIComponentKind.html
  commentId: T:HotPreview.SharedModel.UIComponentKind
  fullName: HotPreview.SharedModel.UIComponentKind
  nameWithType: UIComponentKind
- uid: HotPreview.SharedModel.UIComponentKind.Control
  name: Control
  href: api/HotPreview.SharedModel.UIComponentKind.html#HotPreview_SharedModel_UIComponentKind_Control
  commentId: F:HotPreview.SharedModel.UIComponentKind.Control
  fullName: HotPreview.SharedModel.UIComponentKind.Control
  nameWithType: UIComponentKind.Control
- uid: HotPreview.SharedModel.UIComponentKind.Page
  name: Page
  href: api/HotPreview.SharedModel.UIComponentKind.html#HotPreview_SharedModel_UIComponentKind_Page
  commentId: F:HotPreview.SharedModel.UIComponentKind.Page
  fullName: HotPreview.SharedModel.UIComponentKind.Page
  nameWithType: UIComponentKind.Page
- uid: HotPreview.SharedModel.UIComponentKind.Unknown
  name: Unknown
  href: api/HotPreview.SharedModel.UIComponentKind.html#HotPreview_SharedModel_UIComponentKind_Unknown
  commentId: F:HotPreview.SharedModel.UIComponentKind.Unknown
  fullName: HotPreview.SharedModel.UIComponentKind.Unknown
  nameWithType: UIComponentKind.Unknown
- uid: HotPreview.SharedModel.UIComponentNotFoundException
  name: UIComponentNotFoundException
  href: api/HotPreview.SharedModel.UIComponentNotFoundException.html
  commentId: T:HotPreview.SharedModel.UIComponentNotFoundException
  fullName: HotPreview.SharedModel.UIComponentNotFoundException
  nameWithType: UIComponentNotFoundException
- uid: HotPreview.SharedModel.UIComponentNotFoundException.#ctor(System.String)
  name: UIComponentNotFoundException(string)
  href: api/HotPreview.SharedModel.UIComponentNotFoundException.html#HotPreview_SharedModel_UIComponentNotFoundException__ctor_System_String_
  commentId: M:HotPreview.SharedModel.UIComponentNotFoundException.#ctor(System.String)
  name.vb: New(String)
  fullName: HotPreview.SharedModel.UIComponentNotFoundException.UIComponentNotFoundException(string)
  fullName.vb: HotPreview.SharedModel.UIComponentNotFoundException.New(String)
  nameWithType: UIComponentNotFoundException.UIComponentNotFoundException(string)
  nameWithType.vb: UIComponentNotFoundException.New(String)
- uid: HotPreview.SharedModel.UIComponentNotFoundException.#ctor*
  name: UIComponentNotFoundException
  href: api/HotPreview.SharedModel.UIComponentNotFoundException.html#HotPreview_SharedModel_UIComponentNotFoundException__ctor_
  commentId: Overload:HotPreview.SharedModel.UIComponentNotFoundException.#ctor
  isSpec: "True"
  name.vb: New
  fullName: HotPreview.SharedModel.UIComponentNotFoundException.UIComponentNotFoundException
  fullName.vb: HotPreview.SharedModel.UIComponentNotFoundException.New
  nameWithType: UIComponentNotFoundException.UIComponentNotFoundException
  nameWithType.vb: UIComponentNotFoundException.New
- uid: HotPreview.SharedModel.UIComponentPreviewPair`2
  name: UIComponentPreviewPair<TUIComponent, TPreview>
  href: api/HotPreview.SharedModel.UIComponentPreviewPair-2.html
  commentId: T:HotPreview.SharedModel.UIComponentPreviewPair`2
  name.vb: UIComponentPreviewPair(Of TUIComponent, TPreview)
  fullName: HotPreview.SharedModel.UIComponentPreviewPair<TUIComponent, TPreview>
  fullName.vb: HotPreview.SharedModel.UIComponentPreviewPair(Of TUIComponent, TPreview)
  nameWithType: UIComponentPreviewPair<TUIComponent, TPreview>
  nameWithType.vb: UIComponentPreviewPair(Of TUIComponent, TPreview)
- uid: HotPreview.SharedModel.UIComponentPreviewPair`2.#ctor(`0,`1)
  name: UIComponentPreviewPair(TUIComponent, TPreview)
  href: api/HotPreview.SharedModel.UIComponentPreviewPair-2.html#HotPreview_SharedModel_UIComponentPreviewPair_2__ctor__0__1_
  commentId: M:HotPreview.SharedModel.UIComponentPreviewPair`2.#ctor(`0,`1)
  name.vb: New(TUIComponent, TPreview)
  fullName: HotPreview.SharedModel.UIComponentPreviewPair<TUIComponent, TPreview>.UIComponentPreviewPair(TUIComponent, TPreview)
  fullName.vb: HotPreview.SharedModel.UIComponentPreviewPair(Of TUIComponent, TPreview).New(TUIComponent, TPreview)
  nameWithType: UIComponentPreviewPair<TUIComponent, TPreview>.UIComponentPreviewPair(TUIComponent, TPreview)
  nameWithType.vb: UIComponentPreviewPair(Of TUIComponent, TPreview).New(TUIComponent, TPreview)
- uid: HotPreview.SharedModel.UIComponentPreviewPair`2.#ctor*
  name: UIComponentPreviewPair
  href: api/HotPreview.SharedModel.UIComponentPreviewPair-2.html#HotPreview_SharedModel_UIComponentPreviewPair_2__ctor_
  commentId: Overload:HotPreview.SharedModel.UIComponentPreviewPair`2.#ctor
  isSpec: "True"
  name.vb: New
  fullName: HotPreview.SharedModel.UIComponentPreviewPair<TUIComponent, TPreview>.UIComponentPreviewPair
  fullName.vb: HotPreview.SharedModel.UIComponentPreviewPair(Of TUIComponent, TPreview).New
  nameWithType: UIComponentPreviewPair<TUIComponent, TPreview>.UIComponentPreviewPair
  nameWithType.vb: UIComponentPreviewPair(Of TUIComponent, TPreview).New
- uid: HotPreview.SharedModel.UIComponentPreviewPair`2.Preview
  name: Preview
  href: api/HotPreview.SharedModel.UIComponentPreviewPair-2.html#HotPreview_SharedModel_UIComponentPreviewPair_2_Preview
  commentId: P:HotPreview.SharedModel.UIComponentPreviewPair`2.Preview
  fullName: HotPreview.SharedModel.UIComponentPreviewPair<TUIComponent, TPreview>.Preview
  fullName.vb: HotPreview.SharedModel.UIComponentPreviewPair(Of TUIComponent, TPreview).Preview
  nameWithType: UIComponentPreviewPair<TUIComponent, TPreview>.Preview
  nameWithType.vb: UIComponentPreviewPair(Of TUIComponent, TPreview).Preview
- uid: HotPreview.SharedModel.UIComponentPreviewPair`2.Preview*
  name: Preview
  href: api/HotPreview.SharedModel.UIComponentPreviewPair-2.html#HotPreview_SharedModel_UIComponentPreviewPair_2_Preview_
  commentId: Overload:HotPreview.SharedModel.UIComponentPreviewPair`2.Preview
  isSpec: "True"
  fullName: HotPreview.SharedModel.UIComponentPreviewPair<TUIComponent, TPreview>.Preview
  fullName.vb: HotPreview.SharedModel.UIComponentPreviewPair(Of TUIComponent, TPreview).Preview
  nameWithType: UIComponentPreviewPair<TUIComponent, TPreview>.Preview
  nameWithType.vb: UIComponentPreviewPair(Of TUIComponent, TPreview).Preview
- uid: HotPreview.SharedModel.UIComponentPreviewPair`2.UIComponent
  name: UIComponent
  href: api/HotPreview.SharedModel.UIComponentPreviewPair-2.html#HotPreview_SharedModel_UIComponentPreviewPair_2_UIComponent
  commentId: P:HotPreview.SharedModel.UIComponentPreviewPair`2.UIComponent
  fullName: HotPreview.SharedModel.UIComponentPreviewPair<TUIComponent, TPreview>.UIComponent
  fullName.vb: HotPreview.SharedModel.UIComponentPreviewPair(Of TUIComponent, TPreview).UIComponent
  nameWithType: UIComponentPreviewPair<TUIComponent, TPreview>.UIComponent
  nameWithType.vb: UIComponentPreviewPair(Of TUIComponent, TPreview).UIComponent
- uid: HotPreview.SharedModel.UIComponentPreviewPair`2.UIComponent*
  name: UIComponent
  href: api/HotPreview.SharedModel.UIComponentPreviewPair-2.html#HotPreview_SharedModel_UIComponentPreviewPair_2_UIComponent_
  commentId: Overload:HotPreview.SharedModel.UIComponentPreviewPair`2.UIComponent
  isSpec: "True"
  fullName: HotPreview.SharedModel.UIComponentPreviewPair<TUIComponent, TPreview>.UIComponent
  fullName.vb: HotPreview.SharedModel.UIComponentPreviewPair(Of TUIComponent, TPreview).UIComponent
  nameWithType: UIComponentPreviewPair<TUIComponent, TPreview>.UIComponent
  nameWithType.vb: UIComponentPreviewPair(Of TUIComponent, TPreview).UIComponent
- uid: HotPreview.SpecialUIComponentNames
  name: SpecialUIComponentNames
  href: api/HotPreview.SpecialUIComponentNames.html
  commentId: T:HotPreview.SpecialUIComponentNames
  fullName: HotPreview.SpecialUIComponentNames
  nameWithType: SpecialUIComponentNames
- uid: HotPreview.SpecialUIComponentNames.FullApp
  name: FullApp
  href: api/HotPreview.SpecialUIComponentNames.html#HotPreview_SpecialUIComponentNames_FullApp
  commentId: F:HotPreview.SpecialUIComponentNames.FullApp
  fullName: HotPreview.SpecialUIComponentNames.FullApp
  nameWithType: SpecialUIComponentNames.FullApp
- uid: HotPreview.UIComponentAttribute
  name: UIComponentAttribute
  href: api/HotPreview.UIComponentAttribute.html
  commentId: T:HotPreview.UIComponentAttribute
  fullName: HotPreview.UIComponentAttribute
  nameWithType: UIComponentAttribute
- uid: HotPreview.UIComponentAttribute.#ctor(System.String)
  name: UIComponentAttribute(string?)
  href: api/HotPreview.UIComponentAttribute.html#HotPreview_UIComponentAttribute__ctor_System_String_
  commentId: M:HotPreview.UIComponentAttribute.#ctor(System.String)
  name.vb: New(String)
  fullName: HotPreview.UIComponentAttribute.UIComponentAttribute(string?)
  fullName.vb: HotPreview.UIComponentAttribute.New(String)
  nameWithType: UIComponentAttribute.UIComponentAttribute(string?)
  nameWithType.vb: UIComponentAttribute.New(String)
- uid: HotPreview.UIComponentAttribute.#ctor*
  name: UIComponentAttribute
  href: api/HotPreview.UIComponentAttribute.html#HotPreview_UIComponentAttribute__ctor_
  commentId: Overload:HotPreview.UIComponentAttribute.#ctor
  isSpec: "True"
  name.vb: New
  fullName: HotPreview.UIComponentAttribute.UIComponentAttribute
  fullName.vb: HotPreview.UIComponentAttribute.New
  nameWithType: UIComponentAttribute.UIComponentAttribute
  nameWithType.vb: UIComponentAttribute.New
- uid: HotPreview.UIComponentAttribute.DisplayName
  name: DisplayName
  href: api/HotPreview.UIComponentAttribute.html#HotPreview_UIComponentAttribute_DisplayName
  commentId: P:HotPreview.UIComponentAttribute.DisplayName
  fullName: HotPreview.UIComponentAttribute.DisplayName
  nameWithType: UIComponentAttribute.DisplayName
- uid: HotPreview.UIComponentAttribute.DisplayName*
  name: DisplayName
  href: api/HotPreview.UIComponentAttribute.html#HotPreview_UIComponentAttribute_DisplayName_
  commentId: Overload:HotPreview.UIComponentAttribute.DisplayName
  isSpec: "True"
  fullName: HotPreview.UIComponentAttribute.DisplayName
  nameWithType: UIComponentAttribute.DisplayName
- uid: HotPreview.UIComponentCategoryAttribute
  name: UIComponentCategoryAttribute
  href: api/HotPreview.UIComponentCategoryAttribute.html
  commentId: T:HotPreview.UIComponentCategoryAttribute
  fullName: HotPreview.UIComponentCategoryAttribute
  nameWithType: UIComponentCategoryAttribute
- uid: HotPreview.UIComponentCategoryAttribute.#ctor(System.String,System.Type[])
  name: UIComponentCategoryAttribute(string, params Type[])
  href: api/HotPreview.UIComponentCategoryAttribute.html#HotPreview_UIComponentCategoryAttribute__ctor_System_String_System_Type___
  commentId: M:HotPreview.UIComponentCategoryAttribute.#ctor(System.String,System.Type[])
  name.vb: New(String, ParamArray Type())
  fullName: HotPreview.UIComponentCategoryAttribute.UIComponentCategoryAttribute(string, params System.Type[])
  fullName.vb: HotPreview.UIComponentCategoryAttribute.New(String, ParamArray System.Type())
  nameWithType: UIComponentCategoryAttribute.UIComponentCategoryAttribute(string, params Type[])
  nameWithType.vb: UIComponentCategoryAttribute.New(String, ParamArray Type())
- uid: HotPreview.UIComponentCategoryAttribute.#ctor*
  name: UIComponentCategoryAttribute
  href: api/HotPreview.UIComponentCategoryAttribute.html#HotPreview_UIComponentCategoryAttribute__ctor_
  commentId: Overload:HotPreview.UIComponentCategoryAttribute.#ctor
  isSpec: "True"
  name.vb: New
  fullName: HotPreview.UIComponentCategoryAttribute.UIComponentCategoryAttribute
  fullName.vb: HotPreview.UIComponentCategoryAttribute.New
  nameWithType: UIComponentCategoryAttribute.UIComponentCategoryAttribute
  nameWithType.vb: UIComponentCategoryAttribute.New
- uid: HotPreview.UIComponentCategoryAttribute.Name
  name: Name
  href: api/HotPreview.UIComponentCategoryAttribute.html#HotPreview_UIComponentCategoryAttribute_Name
  commentId: P:HotPreview.UIComponentCategoryAttribute.Name
  fullName: HotPreview.UIComponentCategoryAttribute.Name
  nameWithType: UIComponentCategoryAttribute.Name
- uid: HotPreview.UIComponentCategoryAttribute.Name*
  name: Name
  href: api/HotPreview.UIComponentCategoryAttribute.html#HotPreview_UIComponentCategoryAttribute_Name_
  commentId: Overload:HotPreview.UIComponentCategoryAttribute.Name
  isSpec: "True"
  fullName: HotPreview.UIComponentCategoryAttribute.Name
  nameWithType: UIComponentCategoryAttribute.Name
- uid: HotPreview.UIComponentCategoryAttribute.UIComponentTypes
  name: UIComponentTypes
  href: api/HotPreview.UIComponentCategoryAttribute.html#HotPreview_UIComponentCategoryAttribute_UIComponentTypes
  commentId: P:HotPreview.UIComponentCategoryAttribute.UIComponentTypes
  fullName: HotPreview.UIComponentCategoryAttribute.UIComponentTypes
  nameWithType: UIComponentCategoryAttribute.UIComponentTypes
- uid: HotPreview.UIComponentCategoryAttribute.UIComponentTypes*
  name: UIComponentTypes
  href: api/HotPreview.UIComponentCategoryAttribute.html#HotPreview_UIComponentCategoryAttribute_UIComponentTypes_
  commentId: Overload:HotPreview.UIComponentCategoryAttribute.UIComponentTypes
  isSpec: "True"
  fullName: HotPreview.UIComponentCategoryAttribute.UIComponentTypes
  nameWithType: UIComponentCategoryAttribute.UIComponentTypes
