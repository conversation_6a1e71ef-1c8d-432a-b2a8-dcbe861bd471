### YamlMime:ManagedReference
items:
- uid: HotPreview
  commentId: N:HotPreview
  id: HotPreview
  children:
  - HotPreview.AutoGeneratePreviewAttribute
  - HotPreview.ControlUIComponentBaseTypeAttribute
  - HotPreview.PageUIComponentBaseTypeAttribute
  - HotPreview.PreviewAttribute
  - HotPreview.PreviewAttribute`1
  - HotPreview.PreviewCommandAttribute
  - HotPreview.RoutePreview
  - HotPreview.RoutePreview`1
  - HotPreview.SpecialUIComponentNames
  - HotPreview.UIComponentAttribute
  - HotPreview.UIComponentCategoryAttribute
  langs:
  - csharp
  - vb
  name: HotPreview
  nameWithType: HotPreview
  fullName: HotPreview
  type: Namespace
  assemblies:
  - HotPreview
references:
- uid: HotPreview.AutoGeneratePreviewAttribute
  commentId: T:HotPreview.AutoGeneratePreviewAttribute
  href: HotPreview.AutoGeneratePreviewAttribute.html
  name: AutoGeneratePreviewAttribute
  nameWithType: AutoGeneratePreviewAttribute
  fullName: HotPreview.AutoGeneratePreviewAttribute
- uid: HotPreview.ControlUIComponentBaseTypeAttribute
  commentId: T:HotPreview.ControlUIComponentBaseTypeAttribute
  href: HotPreview.ControlUIComponentBaseTypeAttribute.html
  name: ControlUIComponentBaseTypeAttribute
  nameWithType: ControlUIComponentBaseTypeAttribute
  fullName: HotPreview.ControlUIComponentBaseTypeAttribute
- uid: HotPreview.PageUIComponentBaseTypeAttribute
  commentId: T:HotPreview.PageUIComponentBaseTypeAttribute
  href: HotPreview.PageUIComponentBaseTypeAttribute.html
  name: PageUIComponentBaseTypeAttribute
  nameWithType: PageUIComponentBaseTypeAttribute
  fullName: HotPreview.PageUIComponentBaseTypeAttribute
- uid: HotPreview.PreviewAttribute
  commentId: T:HotPreview.PreviewAttribute
  parent: HotPreview
  href: HotPreview.PreviewAttribute.html
  name: PreviewAttribute
  nameWithType: PreviewAttribute
  fullName: HotPreview.PreviewAttribute
- uid: HotPreview.PreviewAttribute`1
  commentId: T:HotPreview.PreviewAttribute`1
  href: HotPreview.PreviewAttribute-1.html
  name: PreviewAttribute<TUIComponent>
  nameWithType: PreviewAttribute<TUIComponent>
  fullName: HotPreview.PreviewAttribute<TUIComponent>
  nameWithType.vb: PreviewAttribute(Of TUIComponent)
  fullName.vb: HotPreview.PreviewAttribute(Of TUIComponent)
  name.vb: PreviewAttribute(Of TUIComponent)
  spec.csharp:
  - uid: HotPreview.PreviewAttribute`1
    name: PreviewAttribute
    href: HotPreview.PreviewAttribute-1.html
  - name: <
  - name: TUIComponent
  - name: '>'
  spec.vb:
  - uid: HotPreview.PreviewAttribute`1
    name: PreviewAttribute
    href: HotPreview.PreviewAttribute-1.html
  - name: (
  - name: Of
  - name: " "
  - name: TUIComponent
  - name: )
- uid: HotPreview.PreviewCommandAttribute
  commentId: T:HotPreview.PreviewCommandAttribute
  href: HotPreview.PreviewCommandAttribute.html
  name: PreviewCommandAttribute
  nameWithType: PreviewCommandAttribute
  fullName: HotPreview.PreviewCommandAttribute
- uid: HotPreview.RoutePreview
  commentId: T:HotPreview.RoutePreview
  parent: HotPreview
  href: HotPreview.RoutePreview.html
  name: RoutePreview
  nameWithType: RoutePreview
  fullName: HotPreview.RoutePreview
- uid: HotPreview.RoutePreview`1
  commentId: T:HotPreview.RoutePreview`1
  href: HotPreview.RoutePreview-1.html
  name: RoutePreview<T>
  nameWithType: RoutePreview<T>
  fullName: HotPreview.RoutePreview<T>
  nameWithType.vb: RoutePreview(Of T)
  fullName.vb: HotPreview.RoutePreview(Of T)
  name.vb: RoutePreview(Of T)
  spec.csharp:
  - uid: HotPreview.RoutePreview`1
    name: RoutePreview
    href: HotPreview.RoutePreview-1.html
  - name: <
  - name: T
  - name: '>'
  spec.vb:
  - uid: HotPreview.RoutePreview`1
    name: RoutePreview
    href: HotPreview.RoutePreview-1.html
  - name: (
  - name: Of
  - name: " "
  - name: T
  - name: )
- uid: HotPreview.SpecialUIComponentNames
  commentId: T:HotPreview.SpecialUIComponentNames
  href: HotPreview.SpecialUIComponentNames.html
  name: SpecialUIComponentNames
  nameWithType: SpecialUIComponentNames
  fullName: HotPreview.SpecialUIComponentNames
- uid: HotPreview.UIComponentAttribute
  commentId: T:HotPreview.UIComponentAttribute
  href: HotPreview.UIComponentAttribute.html
  name: UIComponentAttribute
  nameWithType: UIComponentAttribute
  fullName: HotPreview.UIComponentAttribute
- uid: HotPreview.UIComponentCategoryAttribute
  commentId: T:HotPreview.UIComponentCategoryAttribute
  href: HotPreview.UIComponentCategoryAttribute.html
  name: UIComponentCategoryAttribute
  nameWithType: UIComponentCategoryAttribute
  fullName: HotPreview.UIComponentCategoryAttribute
- uid: HotPreview
  commentId: N:HotPreview
  href: HotPreview.html
  name: HotPreview
  nameWithType: HotPreview
  fullName: HotPreview
