<!DOCTYPE html>
<html>
  <head>
    <meta charset="utf-8">
      <title>Architecture | Hot Preview Documentation </title>
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <meta name="title" content="Architecture | Hot Preview Documentation ">
      
      
      <link rel="icon" href="../favicon.ico">
      <link rel="stylesheet" href="../public/docfx.min.css">
      <link rel="stylesheet" href="../public/main.css">
      <meta name="docfx:navrel" content="../toc.html">
      <meta name="docfx:tocrel" content="toc.html">
      
      <meta name="docfx:rel" content="../">
      
      
      <meta name="docfx:docurl" content="https://github.com/BretJohnson/hot-preview/blob/main/docs/docs/architecture.md/#L1">
      <meta name="loc:inThisArticle" content="In this article">
      <meta name="loc:searchResultsCount" content="{count} results for &quot;{query}&quot;">
      <meta name="loc:searchNoResults" content="No results for &quot;{query}&quot;">
      <meta name="loc:tocFilter" content="Filter by title">
      <meta name="loc:nextArticle" content="Next">
      <meta name="loc:prevArticle" content="Previous">
      <meta name="loc:themeLight" content="Light">
      <meta name="loc:themeDark" content="Dark">
      <meta name="loc:themeAuto" content="Auto">
      <meta name="loc:changeTheme" content="Change theme">
      <meta name="loc:copy" content="Copy">
      <meta name="loc:downloadPdf" content="Download PDF">

      <script type="module" src="./../public/docfx.min.js"></script>

      <script>
        const theme = localStorage.getItem('theme') || 'auto'
        document.documentElement.setAttribute('data-bs-theme', theme === 'auto' ? (window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light') : theme)
      </script>

  </head>

  <body class="tex2jax_ignore" data-layout="" data-yaml-mime="">
    <header class="bg-body border-bottom">
      <nav id="autocollapse" class="navbar navbar-expand-md" role="navigation">
        <div class="container-xxl flex-nowrap">
          <a class="navbar-brand" href="../index.html">
            <img id="logo" class="svg" src="../logo.svg" alt="Hot Preview">
            Hot Preview
          </a>
          <button class="btn btn-lg d-md-none border-0" type="button" data-bs-toggle="collapse" data-bs-target="#navpanel" aria-controls="navpanel" aria-expanded="false" aria-label="Toggle navigation">
            <i class="bi bi-three-dots"></i>
          </button>
          <div class="collapse navbar-collapse" id="navpanel">
            <div id="navbar">
              <form class="search" role="search" id="search">
                <i class="bi bi-search"></i>
                <input class="form-control" id="search-query" type="search" disabled placeholder="Search" autocomplete="off" aria-label="Search">
              </form>
            </div>
          </div>
        </div>
      </nav>
    </header>

    <main class="container-xxl">
      <div class="toc-offcanvas">
        <div class="offcanvas-md offcanvas-start" tabindex="-1" id="tocOffcanvas" aria-labelledby="tocOffcanvasLabel">
          <div class="offcanvas-header">
            <h5 class="offcanvas-title" id="tocOffcanvasLabel">Table of Contents</h5>
            <button type="button" class="btn-close" data-bs-dismiss="offcanvas" data-bs-target="#tocOffcanvas" aria-label="Close"></button>
          </div>
          <div class="offcanvas-body">
            <nav class="toc" id="toc"></nav>
          </div>
        </div>
      </div>

      <div class="content">
        <div class="actionbar">
          <button class="btn btn-lg border-0 d-md-none" type="button" data-bs-toggle="offcanvas" data-bs-target="#tocOffcanvas" aria-controls="tocOffcanvas" aria-expanded="false" aria-label="Show table of contents">
            <i class="bi bi-list"></i>
          </button>

          <nav id="breadcrumb"></nav>
        </div>

        <article data-uid="">
<h1 id="architecture">Architecture</h1>

<p>HotPreview is designed as a cross-platform UI component preview system for .NET, with a modular architecture that separates concerns and enables platform-specific implementations.</p>
<h2 id="system-overview">System Overview</h2>
<pre><code class="lang-mermaid">graph LR
    subgraph &quot;DevTools Process&quot;
        DT[&quot;DevTools&lt;br/&gt;(Uno+Skia)&quot;]
    end

    subgraph &quot;Your App Process&quot;
        YA[&quot;Your App&quot;]
        HPL[&quot;Hot Preview&lt;br/&gt;Platform Library&quot;]
        YA -.-&gt; HPL
    end

    DT -.-&gt;|&quot;JSON-RPC Protocol&quot;| YA
</code></pre>
<h2 id="core-components">Core Components</h2>
<h3 id="1-hotpreview-core-library">1. HotPreview Core Library</h3>
<p><strong>Location:</strong> <code>src/HotPreview/</code>
<strong>Purpose:</strong> Base attributes and types for defining previews</p>
<p><strong>Key Components:</strong></p>
<ul>
<li><code>PreviewAttribute</code> - Marks methods as preview definitions</li>
<li><code>UIComponentAttribute</code> - Explicitly marks classes as UI components</li>
<li><code>PreviewCommandAttribute</code> - Defines executable commands</li>
<li><code>AutoGeneratePreviewAttribute</code> - Controls auto-generation behavior</li>
</ul>
<h3 id="2-hotpreview-shared-model">2. HotPreview Shared Model</h3>
<p><strong>Location:</strong> <code>src/HotPreview.SharedModel/</code>
<strong>Purpose:</strong> Cross-platform protocol and reflection utilities</p>
<p><strong>Key Components:</strong></p>
<ul>
<li><strong>Protocol Definitions:</strong> JSON-RPC interfaces for tool communication</li>
<li><strong>Reflection Utilities:</strong> Component and preview discovery at runtime</li>
<li><strong>App Services:</strong> Base classes for platform-specific implementations</li>
</ul>
<p><strong>Protocol Classes:</strong></p>
<pre><code class="lang-csharp">public interface IPreviewAppService
{
    Task&lt;PreviewInfo[]&gt; GetPreviewsAsync();
    Task NavigateToPreviewAsync(string previewId);
    Task ExecuteCommandAsync(string commandId);
}
</code></pre>
<h3 id="3-platform-applications">3. Platform Applications</h3>
<p><strong>Location:</strong> <code>src/platforms/</code>
<strong>Purpose:</strong> Platform-specific preview applications</p>
<h4 id="maui-implementation-hotpreviewappmaui">MAUI Implementation (<code>HotPreview.App.Maui</code>)</h4>
<ul>
<li><code>MauiPreviewApplication</code> - Main application service</li>
<li><code>MauiPreviewNavigatorService</code> - Navigation handling</li>
<li><code>PreviewsPage</code> - UI for displaying component tree</li>
<li>Platform-specific resource handling</li>
</ul>
<h4 id="wpf-implementation-hotpreviewappwpf-planned">WPF Implementation (<code>HotPreview.App.Wpf</code>) <em>(Planned)</em></h4>
<ul>
<li>Similar structure adapted for WPF platform</li>
<li>Windows-specific navigation and rendering</li>
</ul>
<h3 id="4-devtools-infrastructure">4. DevTools Infrastructure</h3>
<p><strong>Location:</strong> <code>src/tooling/</code>
<strong>Purpose:</strong> Visual development environment and tooling</p>
<h4 id="devtools-application-hotpreviewdevtoolsapp">DevTools Application (<code>HotPreview.DevToolsApp</code>)</h4>
<ul>
<li><strong>Uno+Skia Desktop App:</strong> Main user interface</li>
<li><strong>Connection Management:</strong> Handles multiple app connections</li>
<li><strong>Component Tree View:</strong> Hierarchical navigation interface</li>
<li><strong>Command Execution:</strong> UI for running preview commands</li>
</ul>
<h4 id="devtools-launcher-hotpreviewdevtools">DevTools Launcher (<code>HotPreview.DevTools</code>)</h4>
<ul>
<li><strong>Global Tool:</strong> <code>dotnet tool install -g HotPreview.DevTools</code></li>
<li><strong>Process Management:</strong> Launches and manages DevTools instances</li>
<li><strong>Single Instance Logic:</strong> Prevents multiple DevTools instances</li>
</ul>
<h4 id="tooling-infrastructure-hotpreviewtooling">Tooling Infrastructure (<code>HotPreview.Tooling</code>)</h4>
<ul>
<li><strong>Roslyn Analysis:</strong> Component discovery through source analysis</li>
<li><strong>JSON-RPC Server:</strong> Communication protocol implementation</li>
<li><strong>MCP Server:</strong> AI integration capabilities</li>
<li><strong>Visual Testing:</strong> Screenshot and comparison utilities</li>
</ul>
<h2 id="communication-protocol">Communication Protocol</h2>
<h3 id="json-rpc-over-pipes">JSON-RPC Over Pipes</h3>
<p>HotPreview uses JSON-RPC over named pipes for communication between DevTools and applications.</p>
<p><strong>Message Flow:</strong></p>
<ol>
<li><strong>App Startup:</strong> Application connects to DevTools via named pipe</li>
<li><strong>Discovery:</strong> DevTools requests component and preview information</li>
<li><strong>Navigation:</strong> User clicks component, DevTools sends navigation command</li>
<li><strong>Command Execution:</strong> User triggers command, DevTools forwards to app</li>
</ol>
<p><strong>Example Messages:</strong></p>
<pre><code class="lang-json">// Get Previews Request
{
  &quot;id&quot;: 1,
  &quot;method&quot;: &quot;getPreviewsAsync&quot;,
  &quot;params&quot;: {}
}

// Navigate to Preview Request
{
  &quot;id&quot;: 2,
  &quot;method&quot;: &quot;navigateToPreviewAsync&quot;,
  &quot;params&quot;: {
    &quot;previewId&quot;: &quot;MyApp.Views.ProductCard.Preview&quot;
  }
}
</code></pre>
<h2 id="component-discovery">Component Discovery</h2>
<h3 id="runtime-discovery-reflection">Runtime Discovery (Reflection)</h3>
<p><strong>Location:</strong> <code>HotPreview.SharedModel/App/GetPreviewsViaReflection.cs</code></p>
<p><strong>Process:</strong></p>
<ol>
<li><strong>Assembly Scanning:</strong> Scan loaded assemblies for components</li>
<li><strong>Type Analysis:</strong> Identify types inheriting from platform base types</li>
<li><strong>Attribute Detection:</strong> Find <code>[Preview]</code> and <code>[UIComponent]</code> attributes</li>
<li><strong>Auto-Generation:</strong> Create default previews for discovered components</li>
</ol>
<p><strong>Example Code:</strong></p>
<pre><code class="lang-csharp">public static PreviewInfo[] GetPreviews(Assembly assembly)
{
    var types = assembly.GetTypes()
        .Where(t =&gt; IsUIComponent(t))
        .ToArray();

    return types.SelectMany(CreatePreviewsForType).ToArray();
}
</code></pre>
<h3 id="build-time-discovery-roslyn">Build-Time Discovery (Roslyn)</h3>
<p><strong>Location:</strong> <code>HotPreview.Tooling/GetPreviewsFromRoslyn.cs</code></p>
<p><strong>Process:</strong></p>
<ol>
<li><strong>Source Analysis:</strong> Parse C# source files using Roslyn</li>
<li><strong>Syntax Tree Walking:</strong> Find class declarations and attributes</li>
<li><strong>Preview Method Detection:</strong> Locate <code>[Preview]</code> methods</li>
<li><strong>Metadata Generation:</strong> Create preview metadata without runtime loading</li>
</ol>
<p><strong>Benefits:</strong></p>
<ul>
<li>No runtime overhead</li>
<li>Works with uncompiled code</li>
<li>Better performance for large applications</li>
</ul>
<h2 id="msbuild-integration">MSBuild Integration</h2>
<h3 id="build-tasks">Build Tasks</h3>
<p><strong>Location:</strong> <code>src/HotPreview.AppBuildTasks/</code></p>
<p><strong>Key Tasks:</strong></p>
<ul>
<li><code>GeneratePreviewAppSettingsTask</code> - Creates app configuration</li>
<li><strong>DevTools Launch:</strong> Automatically starts DevTools during builds</li>
<li><strong>Symbol Definition:</strong> Ensures <code>PREVIEWS</code> symbol is defined in Debug builds</li>
</ul>
<p><strong>Integration Points:</strong></p>
<pre><code class="lang-xml">&lt;Target Name=&quot;LaunchDevTools&quot; BeforeTargets=&quot;Build&quot; Condition=&quot;$(Configuration) == 'Debug'&quot;&gt;
  &lt;Exec Command=&quot;hotpreview&quot; ContinueOnError=&quot;true&quot; /&gt;
&lt;/Target&gt;
</code></pre>
<h2 id="platform-abstraction">Platform Abstraction</h2>
<h3 id="base-type-configuration">Base Type Configuration</h3>
<p>Platform-specific base types are configured via assembly attributes:</p>
<pre><code class="lang-csharp">[assembly: ControlUIComponentBaseType(&quot;MAUI&quot;, &quot;Microsoft.Maui.Controls.View&quot;)]
[assembly: PageUIComponentBaseType(&quot;MAUI&quot;, &quot;Microsoft.Maui.Controls.Page&quot;)]
</code></pre>
<h3 id="service-implementation">Service Implementation</h3>
<p>Each platform implements the core interfaces:</p>
<pre><code class="lang-csharp">public class MauiPreviewApplication : PreviewApplication
{
    protected override IPreviewNavigator CreateNavigator()
        =&gt; new MauiPreviewNavigatorService();

    protected override IUIComponentExclusionFilter CreateExclusionFilter()
        =&gt; new MauiUIComponentExclusionFilter();
}
</code></pre>
<h2 id="ai-integration-mcp-server">AI Integration (MCP Server)</h2>
<h3 id="model-context-protocol">Model Context Protocol</h3>
<p><strong>Location:</strong> <code>HotPreview.Tooling/McpServer/</code></p>
<p><strong>Capabilities:</strong></p>
<ul>
<li><strong>Device Management:</strong> Android/iOS device discovery and control</li>
<li><strong>Screenshot Capture:</strong> Automated visual testing</li>
<li><strong>App Management:</strong> Launch and control preview applications</li>
<li><strong>Preview Generation:</strong> AI-assisted component creation</li>
</ul>
<p><strong>Architecture:</strong></p>
<pre><code>┌─────────────┐    ┌─────────────┐    ┌─────────────┐
│ AI Agent    │◄──►│ MCP Server  │◄──►│ HotPreview  │
│ (Claude)    │    │             │    │   DevTools  │
└─────────────┘    └─────────────┘    └─────────────┘
</code></pre>
<h2 id="visual-testing">Visual Testing</h2>
<h3 id="snapshot-generation">Snapshot Generation</h3>
<p><strong>Location:</strong> <code>HotPreview.Tooling/VisualTestUtils/</code></p>
<p><strong>Components:</strong></p>
<ul>
<li><code>IImageEditor</code> - Image manipulation interface</li>
<li><code>IVisualComparer</code> - Visual difference detection</li>
<li><code>VisualRegressionTester</code> - Test orchestration</li>
<li><strong>MagickNet Integration:</strong> ImageMagick-based image processing</li>
</ul>
<p><strong>Workflow:</strong></p>
<ol>
<li><strong>Capture Screenshot:</strong> Take snapshot of component</li>
<li><strong>Compare Images:</strong> Detect visual differences</li>
<li><strong>Generate Report:</strong> Create difference visualization</li>
<li><strong>Assert Results:</strong> Integrate with test frameworks</li>
</ol>
<h2 id="extension-points">Extension Points</h2>
<h3 id="custom-platform-support">Custom Platform Support</h3>
<p>To add support for a new platform:</p>
<ol>
<li><p><strong>Implement Base Services:</strong></p>
<pre><code class="lang-csharp">public class MyPlatformPreviewApplication : PreviewApplication
{
    // Platform-specific implementation
}
</code></pre>
</li>
<li><p><strong>Configure Base Types:</strong></p>
<pre><code class="lang-csharp">[assembly: ControlUIComponentBaseType(&quot;MyPlatform&quot;, &quot;MyPlatform.Controls.Control&quot;)]
</code></pre>
</li>
<li><p><strong>Create Package:</strong> Distribute as <code>HotPreview.App.MyPlatform</code></p>
</li>
</ol>
<h3 id="custom-discovery">Custom Discovery</h3>
<p>Extend component discovery:</p>
<pre><code class="lang-csharp">public class CustomComponentDiscovery : IComponentDiscovery
{
    public PreviewInfo[] DiscoverPreviews(Assembly assembly)
    {
        // Custom discovery logic
    }
}
</code></pre>
<h2 id="performance-considerations">Performance Considerations</h2>
<h3 id="memory-management">Memory Management</h3>
<ul>
<li><strong>Lazy Loading:</strong> Components loaded on-demand</li>
<li><strong>Weak References:</strong> Prevent memory leaks in long-running sessions</li>
<li><strong>Resource Cleanup:</strong> Proper disposal of platform resources</li>
</ul>
<h3 id="communication-optimization">Communication Optimization</h3>
<ul>
<li><strong>Batch Operations:</strong> Reduce JSON-RPC round trips</li>
<li><strong>Incremental Updates:</strong> Send only changed data</li>
<li><strong>Connection Pooling:</strong> Reuse connections across sessions</li>
</ul>
<h3 id="scalability">Scalability</h3>
<ul>
<li><strong>Concurrent Discovery:</strong> Parallel component analysis</li>
<li><strong>Caching:</strong> Cache discovered components and metadata</li>
<li><strong>Selective Loading:</strong> Load only visible components in DevTools</li>
</ul>

</article>

        <div class="contribution d-print-none">
          <a href="https://github.com/BretJohnson/hot-preview/blob/main/docs/docs/architecture.md/#L1" class="edit-link">Edit this page</a>
        </div>

        <div class="next-article d-print-none border-top" id="nextArticle"></div>

      </div>

      <div class="affix">
        <nav id="affix"></nav>
      </div>
    </main>

    <div class="container-xxl search-results" id="search-results"></div>

    <footer class="border-top text-secondary">
      <div class="container-xxl">
        <div class="flex-fill">
          <span>Made with <a href="https://dotnet.github.io/docfx">docfx</a></span>
        </div>
      </div>
    </footer>
  </body>
</html>
