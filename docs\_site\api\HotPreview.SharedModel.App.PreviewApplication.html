<!DOCTYPE html>
<html>
  <head>
    <meta charset="utf-8">
      <title>Class PreviewApplication | Hot Preview Documentation </title>
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <meta name="title" content="Class PreviewApplication | Hot Preview Documentation ">
      
      
      <link rel="icon" href="../favicon.ico">
      <link rel="stylesheet" href="../public/docfx.min.css">
      <link rel="stylesheet" href="../public/main.css">
      <meta name="docfx:navrel" content="../toc.html">
      <meta name="docfx:tocrel" content="toc.html">
      
      <meta name="docfx:rel" content="../">
      
      
      <meta name="docfx:docurl" content="https://github.com/BretJohnson/hot-preview/new/main/apiSpec/new?filename=HotPreview_SharedModel_App_PreviewApplication.md&amp;value=---%0Auid%3A%20HotPreview.SharedModel.App.PreviewApplication%0Asummary%3A%20&#39;*You%20can%20override%20summary%20for%20the%20API%20here%20using%20*MARKDOWN*%20syntax&#39;%0A---%0A%0A*Please%20type%20below%20more%20information%20about%20this%20API%3A*%0A%0A">
      <meta name="loc:inThisArticle" content="In this article">
      <meta name="loc:searchResultsCount" content="{count} results for &quot;{query}&quot;">
      <meta name="loc:searchNoResults" content="No results for &quot;{query}&quot;">
      <meta name="loc:tocFilter" content="Filter by title">
      <meta name="loc:nextArticle" content="Next">
      <meta name="loc:prevArticle" content="Previous">
      <meta name="loc:themeLight" content="Light">
      <meta name="loc:themeDark" content="Dark">
      <meta name="loc:themeAuto" content="Auto">
      <meta name="loc:changeTheme" content="Change theme">
      <meta name="loc:copy" content="Copy">
      <meta name="loc:downloadPdf" content="Download PDF">

      <script type="module" src="./../public/docfx.min.js"></script>

      <script>
        const theme = localStorage.getItem('theme') || 'auto'
        document.documentElement.setAttribute('data-bs-theme', theme === 'auto' ? (window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light') : theme)
      </script>

  </head>

  <body class="tex2jax_ignore" data-layout="" data-yaml-mime="ManagedReference">
    <header class="bg-body border-bottom">
      <nav id="autocollapse" class="navbar navbar-expand-md" role="navigation">
        <div class="container-xxl flex-nowrap">
          <a class="navbar-brand" href="../index.html">
            <img id="logo" class="svg" src="../logo.svg" alt="Hot Preview">
            Hot Preview
          </a>
          <button class="btn btn-lg d-md-none border-0" type="button" data-bs-toggle="collapse" data-bs-target="#navpanel" aria-controls="navpanel" aria-expanded="false" aria-label="Toggle navigation">
            <i class="bi bi-three-dots"></i>
          </button>
          <div class="collapse navbar-collapse" id="navpanel">
            <div id="navbar">
              <form class="search" role="search" id="search">
                <i class="bi bi-search"></i>
                <input class="form-control" id="search-query" type="search" disabled placeholder="Search" autocomplete="off" aria-label="Search">
              </form>
            </div>
          </div>
        </div>
      </nav>
    </header>

    <main class="container-xxl">
      <div class="toc-offcanvas">
        <div class="offcanvas-md offcanvas-start" tabindex="-1" id="tocOffcanvas" aria-labelledby="tocOffcanvasLabel">
          <div class="offcanvas-header">
            <h5 class="offcanvas-title" id="tocOffcanvasLabel">Table of Contents</h5>
            <button type="button" class="btn-close" data-bs-dismiss="offcanvas" data-bs-target="#tocOffcanvas" aria-label="Close"></button>
          </div>
          <div class="offcanvas-body">
            <nav class="toc" id="toc"></nav>
          </div>
        </div>
      </div>

      <div class="content">
        <div class="actionbar">
          <button class="btn btn-lg border-0 d-md-none" type="button" data-bs-toggle="offcanvas" data-bs-target="#tocOffcanvas" aria-controls="tocOffcanvas" aria-expanded="false" aria-label="Show table of contents">
            <i class="bi bi-list"></i>
          </button>

          <nav id="breadcrumb"></nav>
        </div>

        <article data-uid="HotPreview.SharedModel.App.PreviewApplication">



  <h1 id="HotPreview_SharedModel_App_PreviewApplication" data-uid="HotPreview.SharedModel.App.PreviewApplication" class="text-break">
Class PreviewApplication  <a class="header-action link-secondary" title="View source" href="https://github.com/BretJohnson/hot-preview/blob/main/src/HotPreview.SharedModel/App/PreviewApplication.cs/#L7"><i class="bi bi-code-slash"></i></a>
  </h1>

  <div class="facts text-secondary">
    <dl><dt>Namespace</dt><dd><a class="xref" href="HotPreview.html">HotPreview</a>.<a class="xref" href="HotPreview.SharedModel.html">SharedModel</a>.<a class="xref" href="HotPreview.SharedModel.App.html">App</a></dd></dl>
  <dl><dt>Assembly</dt><dd>HotPreview.SharedModel.dll</dd></dl>
  </div>

  <div class="markdown summary"></div>
  <div class="markdown conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public abstract class PreviewApplication : IDisposable</code></pre>
  </div>




  <dl class="typelist inheritance">
    <dt>Inheritance</dt>
    <dd>
      <div><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object">object</a></div>
      <div><span class="xref">PreviewApplication</span></div>
    </dd>
  </dl>

  <dl class="typelist implements">
    <dt>Implements</dt>
    <dd>
      <div><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.idisposable">IDisposable</a></div>
    </dd>
  </dl>


  <dl class="typelist inheritedMembers">
    <dt>Inherited Members</dt>
    <dd>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)">object.Equals(object)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)">object.Equals(object, object)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object.gethashcode">object.GetHashCode()</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object.gettype">object.GetType()</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone">object.MemberwiseClone()</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object.referenceequals">object.ReferenceEquals(object, object)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object.tostring">object.ToString()</a>
    </div>
  </dd></dl>






  <h2 class="section" id="properties">Properties
</h2>


  <a id="HotPreview_SharedModel_App_PreviewApplication_AdditionalAppAssemblies_" data-uid="HotPreview.SharedModel.App.PreviewApplication.AdditionalAppAssemblies*"></a>

  <h3 id="HotPreview_SharedModel_App_PreviewApplication_AdditionalAppAssemblies" data-uid="HotPreview.SharedModel.App.PreviewApplication.AdditionalAppAssemblies">
  AdditionalAppAssemblies
  <a class="header-action link-secondary" title="View source" href="https://github.com/BretJohnson/hot-preview/blob/main/src/HotPreview.SharedModel/App/PreviewApplication.cs/#L95"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public IEnumerable&lt;string&gt; AdditionalAppAssemblies { get; }</code></pre>
  </div>





  <h4 class="section">Property Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.collections.generic.ienumerable-1">IEnumerable</a>&lt;<a class="xref" href="https://learn.microsoft.com/dotnet/api/system.string">string</a>&gt;</dt>
    <dd></dd>
  </dl>








  <a id="HotPreview_SharedModel_App_PreviewApplication_EnableJsonRpcTracing_" data-uid="HotPreview.SharedModel.App.PreviewApplication.EnableJsonRpcTracing*"></a>

  <h3 id="HotPreview_SharedModel_App_PreviewApplication_EnableJsonRpcTracing" data-uid="HotPreview.SharedModel.App.PreviewApplication.EnableJsonRpcTracing">
  EnableJsonRpcTracing
  <a class="header-action link-secondary" title="View source" href="https://github.com/BretJohnson/hot-preview/blob/main/src/HotPreview.SharedModel/App/PreviewApplication.cs/#L66"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"><p>Gets or sets whether JsonRpc diagnostic tracing is enabled.
When true, JsonRpc communication will be traced to the console.</p>
</div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public bool EnableJsonRpcTracing { get; set; }</code></pre>
  </div>





  <h4 class="section">Property Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.boolean">bool</a></dt>
    <dd></dd>
  </dl>








  <a id="HotPreview_SharedModel_App_PreviewApplication_MainAssembly_" data-uid="HotPreview.SharedModel.App.PreviewApplication.MainAssembly*"></a>

  <h3 id="HotPreview_SharedModel_App_PreviewApplication_MainAssembly" data-uid="HotPreview.SharedModel.App.PreviewApplication.MainAssembly">
  MainAssembly
  <a class="header-action link-secondary" title="View source" href="https://github.com/BretJohnson/hot-preview/blob/main/src/HotPreview.SharedModel/App/PreviewApplication.cs/#L68"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public Assembly? MainAssembly { get; set; }</code></pre>
  </div>





  <h4 class="section">Property Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.reflection.assembly">Assembly</a></dt>
    <dd></dd>
  </dl>








  <a id="HotPreview_SharedModel_App_PreviewApplication_PlatformName_" data-uid="HotPreview.SharedModel.App.PreviewApplication.PlatformName*"></a>

  <h3 id="HotPreview_SharedModel_App_PreviewApplication_PlatformName" data-uid="HotPreview.SharedModel.App.PreviewApplication.PlatformName">
  PlatformName
  <a class="header-action link-secondary" title="View source" href="https://github.com/BretJohnson/hot-preview/blob/main/src/HotPreview.SharedModel/App/PreviewApplication.cs/#L60"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public abstract string PlatformName { get; set; }</code></pre>
  </div>





  <h4 class="section">Property Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.string">string</a></dt>
    <dd></dd>
  </dl>








  <a id="HotPreview_SharedModel_App_PreviewApplication_ProjectPath_" data-uid="HotPreview.SharedModel.App.PreviewApplication.ProjectPath*"></a>

  <h3 id="HotPreview_SharedModel_App_PreviewApplication_ProjectPath" data-uid="HotPreview.SharedModel.App.PreviewApplication.ProjectPath">
  ProjectPath
  <a class="header-action link-secondary" title="View source" href="https://github.com/BretJohnson/hot-preview/blob/main/src/HotPreview.SharedModel/App/PreviewApplication.cs/#L58"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public string? ProjectPath { get; set; }</code></pre>
  </div>





  <h4 class="section">Property Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.string">string</a></dt>
    <dd></dd>
  </dl>








  <a id="HotPreview_SharedModel_App_PreviewApplication_ServiceProvider_" data-uid="HotPreview.SharedModel.App.PreviewApplication.ServiceProvider*"></a>

  <h3 id="HotPreview_SharedModel_App_PreviewApplication_ServiceProvider" data-uid="HotPreview.SharedModel.App.PreviewApplication.ServiceProvider">
  ServiceProvider
  <a class="header-action link-secondary" title="View source" href="https://github.com/BretJohnson/hot-preview/blob/main/src/HotPreview.SharedModel/App/PreviewApplication.cs/#L74"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"><p>The app's service provider, which when present can be used to instantiate
UI components via dependency injection.</p>
</div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public IServiceProvider? ServiceProvider { get; set; }</code></pre>
  </div>





  <h4 class="section">Property Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.iserviceprovider">IServiceProvider</a></dt>
    <dd></dd>
  </dl>








  <a id="HotPreview_SharedModel_App_PreviewApplication_ToolingConnectionString_" data-uid="HotPreview.SharedModel.App.PreviewApplication.ToolingConnectionString*"></a>

  <h3 id="HotPreview_SharedModel_App_PreviewApplication_ToolingConnectionString" data-uid="HotPreview.SharedModel.App.PreviewApplication.ToolingConnectionString">
  ToolingConnectionString
  <a class="header-action link-secondary" title="View source" href="https://github.com/BretJohnson/hot-preview/blob/main/src/HotPreview.SharedModel/App/PreviewApplication.cs/#L56"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public string? ToolingConnectionString { get; set; }</code></pre>
  </div>





  <h4 class="section">Property Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.string">string</a></dt>
    <dd></dd>
  </dl>








  <h2 class="section" id="methods">Methods
</h2>


  <a id="HotPreview_SharedModel_App_PreviewApplication_AddAdditionalAppAssembly_" data-uid="HotPreview.SharedModel.App.PreviewApplication.AddAdditionalAppAssembly*"></a>

  <h3 id="HotPreview_SharedModel_App_PreviewApplication_AddAdditionalAppAssembly_System_String_" data-uid="HotPreview.SharedModel.App.PreviewApplication.AddAdditionalAppAssembly(System.String)">
  AddAdditionalAppAssembly(string)
  <a class="header-action link-secondary" title="View source" href="https://github.com/BretJohnson/hot-preview/blob/main/src/HotPreview.SharedModel/App/PreviewApplication.cs/#L90"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void AddAdditionalAppAssembly(string assemblyName)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>assemblyName</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.string">string</a></dt>
    <dd></dd>
  </dl>












  <a id="HotPreview_SharedModel_App_PreviewApplication_Dispose_" data-uid="HotPreview.SharedModel.App.PreviewApplication.Dispose*"></a>

  <h3 id="HotPreview_SharedModel_App_PreviewApplication_Dispose" data-uid="HotPreview.SharedModel.App.PreviewApplication.Dispose">
  Dispose()
  <a class="header-action link-secondary" title="View source" href="https://github.com/BretJohnson/hot-preview/blob/main/src/HotPreview.SharedModel/App/PreviewApplication.cs/#L117"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void Dispose()</code></pre>
  </div>













  <a id="HotPreview_SharedModel_App_PreviewApplication_Dispose_" data-uid="HotPreview.SharedModel.App.PreviewApplication.Dispose*"></a>

  <h3 id="HotPreview_SharedModel_App_PreviewApplication_Dispose_System_Boolean_" data-uid="HotPreview.SharedModel.App.PreviewApplication.Dispose(System.Boolean)">
  Dispose(bool)
  <a class="header-action link-secondary" title="View source" href="https://github.com/BretJohnson/hot-preview/blob/main/src/HotPreview.SharedModel/App/PreviewApplication.cs/#L109"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">protected virtual void Dispose(bool disposing)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>disposing</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.boolean">bool</a></dt>
    <dd></dd>
  </dl>












  <a id="HotPreview_SharedModel_App_PreviewApplication_GetInstance_" data-uid="HotPreview.SharedModel.App.PreviewApplication.GetInstance*"></a>

  <h3 id="HotPreview_SharedModel_App_PreviewApplication_GetInstance" data-uid="HotPreview.SharedModel.App.PreviewApplication.GetInstance">
  GetInstance()
  <a class="header-action link-secondary" title="View source" href="https://github.com/BretJohnson/hot-preview/blob/main/src/HotPreview.SharedModel/App/PreviewApplication.cs/#L14"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public static PreviewApplication GetInstance()</code></pre>
  </div>


  <h4 class="section">Returns</h4>
  <dl class="parameters">
    <dt><a class="xref" href="HotPreview.SharedModel.App.PreviewApplication.html">PreviewApplication</a></dt>
    <dd></dd>
  </dl>











  <a id="HotPreview_SharedModel_App_PreviewApplication_GetPreviewAppService_" data-uid="HotPreview.SharedModel.App.PreviewApplication.GetPreviewAppService*"></a>

  <h3 id="HotPreview_SharedModel_App_PreviewApplication_GetPreviewAppService" data-uid="HotPreview.SharedModel.App.PreviewApplication.GetPreviewAppService">
  GetPreviewAppService()
  <a class="header-action link-secondary" title="View source" href="https://github.com/BretJohnson/hot-preview/blob/main/src/HotPreview.SharedModel/App/PreviewApplication.cs/#L24"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public abstract PreviewAppService GetPreviewAppService()</code></pre>
  </div>


  <h4 class="section">Returns</h4>
  <dl class="parameters">
    <dt><a class="xref" href="HotPreview.SharedModel.App.PreviewAppService.html">PreviewAppService</a></dt>
    <dd></dd>
  </dl>











  <a id="HotPreview_SharedModel_App_PreviewApplication_GetPreviewNavigator_" data-uid="HotPreview.SharedModel.App.PreviewApplication.GetPreviewNavigator*"></a>

  <h3 id="HotPreview_SharedModel_App_PreviewApplication_GetPreviewNavigator" data-uid="HotPreview.SharedModel.App.PreviewApplication.GetPreviewNavigator">
  GetPreviewNavigator()
  <a class="header-action link-secondary" title="View source" href="https://github.com/BretJohnson/hot-preview/blob/main/src/HotPreview.SharedModel/App/PreviewApplication.cs/#L26"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public abstract IPreviewNavigator GetPreviewNavigator()</code></pre>
  </div>


  <h4 class="section">Returns</h4>
  <dl class="parameters">
    <dt><a class="xref" href="HotPreview.SharedModel.App.IPreviewNavigator.html">IPreviewNavigator</a></dt>
    <dd></dd>
  </dl>











  <a id="HotPreview_SharedModel_App_PreviewApplication_GetPreviewsManager_" data-uid="HotPreview.SharedModel.App.PreviewApplication.GetPreviewsManager*"></a>

  <h3 id="HotPreview_SharedModel_App_PreviewApplication_GetPreviewsManager" data-uid="HotPreview.SharedModel.App.PreviewApplication.GetPreviewsManager">
  GetPreviewsManager()
  <a class="header-action link-secondary" title="View source" href="https://github.com/BretJohnson/hot-preview/blob/main/src/HotPreview.SharedModel/App/PreviewApplication.cs/#L22"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public abstract PreviewsManagerReflection GetPreviewsManager()</code></pre>
  </div>


  <h4 class="section">Returns</h4>
  <dl class="parameters">
    <dt><a class="xref" href="HotPreview.SharedModel.App.PreviewsManagerReflection.html">PreviewsManagerReflection</a></dt>
    <dd></dd>
  </dl>











  <a id="HotPreview_SharedModel_App_PreviewApplication_GetRequiredService_" data-uid="HotPreview.SharedModel.App.PreviewApplication.GetRequiredService*"></a>

  <h3 id="HotPreview_SharedModel_App_PreviewApplication_GetRequiredService__1" data-uid="HotPreview.SharedModel.App.PreviewApplication.GetRequiredService``1">
  GetRequiredService&lt;TService&gt;()
  <a class="header-action link-secondary" title="View source" href="https://github.com/BretJohnson/hot-preview/blob/main/src/HotPreview.SharedModel/App/PreviewApplication.cs/#L76"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public TService GetRequiredService&lt;TService&gt;() where TService : class</code></pre>
  </div>


  <h4 class="section">Returns</h4>
  <dl class="parameters">
    <dt><span class="xref">TService</span></dt>
    <dd></dd>
  </dl>

  <h4 class="section">Type Parameters</h4>
  <dl class="parameters">
    <dt><code>TService</code></dt>
    <dd></dd>
  </dl>










  <a id="HotPreview_SharedModel_App_PreviewApplication_InitInstance_" data-uid="HotPreview.SharedModel.App.PreviewApplication.InitInstance*"></a>

  <h3 id="HotPreview_SharedModel_App_PreviewApplication_InitInstance_HotPreview_SharedModel_App_PreviewApplication_" data-uid="HotPreview.SharedModel.App.PreviewApplication.InitInstance(HotPreview.SharedModel.App.PreviewApplication)">
  InitInstance(PreviewApplication)
  <a class="header-action link-secondary" title="View source" href="https://github.com/BretJohnson/hot-preview/blob/main/src/HotPreview.SharedModel/App/PreviewApplication.cs/#L17"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">protected static void InitInstance(PreviewApplication instance)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>instance</code> <a class="xref" href="HotPreview.SharedModel.App.PreviewApplication.html">PreviewApplication</a></dt>
    <dd></dd>
  </dl>












  <a id="HotPreview_SharedModel_App_PreviewApplication_StartToolingConnection_" data-uid="HotPreview.SharedModel.App.PreviewApplication.StartToolingConnection*"></a>

  <h3 id="HotPreview_SharedModel_App_PreviewApplication_StartToolingConnection" data-uid="HotPreview.SharedModel.App.PreviewApplication.StartToolingConnection">
  StartToolingConnection()
  <a class="header-action link-secondary" title="View source" href="https://github.com/BretJohnson/hot-preview/blob/main/src/HotPreview.SharedModel/App/PreviewApplication.cs/#L28"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void StartToolingConnection()</code></pre>
  </div>













  <a id="HotPreview_SharedModel_App_PreviewApplication_StopToolingConnection_" data-uid="HotPreview.SharedModel.App.PreviewApplication.StopToolingConnection*"></a>

  <h3 id="HotPreview_SharedModel_App_PreviewApplication_StopToolingConnection" data-uid="HotPreview.SharedModel.App.PreviewApplication.StopToolingConnection">
  StopToolingConnection()
  <a class="header-action link-secondary" title="View source" href="https://github.com/BretJohnson/hot-preview/blob/main/src/HotPreview.SharedModel/App/PreviewApplication.cs/#L47"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void StopToolingConnection()</code></pre>
  </div>













  <a id="HotPreview_SharedModel_App_PreviewApplication_TransformConnectionStringForPlatform_" data-uid="HotPreview.SharedModel.App.PreviewApplication.TransformConnectionStringForPlatform*"></a>

  <h3 id="HotPreview_SharedModel_App_PreviewApplication_TransformConnectionStringForPlatform_System_String_" data-uid="HotPreview.SharedModel.App.PreviewApplication.TransformConnectionStringForPlatform(System.String)">
  TransformConnectionStringForPlatform(string)
  <a class="header-action link-secondary" title="View source" href="https://github.com/BretJohnson/hot-preview/blob/main/src/HotPreview.SharedModel/App/PreviewApplication.cs/#L103"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"><p>Transforms a tooling connection string to be appropriate for the current platform.
By default, returns the input string unchanged. Platform-specific overrides can adjust
the connection string (such as IP addresses or ports) to match the requirements or conventions
of the target platform or device.</p>
</div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public virtual string TransformConnectionStringForPlatform(string connectionString)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>connectionString</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.string">string</a></dt>
    <dd></dd>
  </dl>

  <h4 class="section">Returns</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.string">string</a></dt>
    <dd></dd>
  </dl>












</article>

        <div class="contribution d-print-none">
          <a href="https://github.com/BretJohnson/hot-preview/blob/main/src/HotPreview.SharedModel/App/PreviewApplication.cs/#L7" class="edit-link">Edit this page</a>
        </div>


      </div>

      <div class="affix">
        <nav id="affix"></nav>
      </div>
    </main>

    <div class="container-xxl search-results" id="search-results"></div>

    <footer class="border-top text-secondary">
      <div class="container-xxl">
        <div class="flex-fill">
          <span>Made with <a href="https://dotnet.github.io/docfx">docfx</a></span>
        </div>
      </div>
    </footer>
  </body>
</html>
