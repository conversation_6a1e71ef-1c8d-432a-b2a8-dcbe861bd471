<!DOCTYPE html>
<html>
  <head>
    <meta charset="utf-8">
      <title>Class UIComponentReflection | Hot Preview Documentation </title>
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <meta name="title" content="Class UIComponentReflection | Hot Preview Documentation ">
      
      
      <link rel="icon" href="../favicon.ico">
      <link rel="stylesheet" href="../public/docfx.min.css">
      <link rel="stylesheet" href="../public/main.css">
      <meta name="docfx:navrel" content="../toc.html">
      <meta name="docfx:tocrel" content="toc.html">
      
      <meta name="docfx:rel" content="../">
      
      
      <meta name="docfx:docurl" content="https://github.com/BretJohnson/hot-preview/new/main/apiSpec/new?filename=HotPreview_SharedModel_App_UIComponentReflection.md&amp;value=---%0Auid%3A%20HotPreview.SharedModel.App.UIComponentReflection%0Asummary%3A%20&#39;*You%20can%20override%20summary%20for%20the%20API%20here%20using%20*MARKDOWN*%20syntax&#39;%0A---%0A%0A*Please%20type%20below%20more%20information%20about%20this%20API%3A*%0A%0A">
      <meta name="loc:inThisArticle" content="In this article">
      <meta name="loc:searchResultsCount" content="{count} results for &quot;{query}&quot;">
      <meta name="loc:searchNoResults" content="No results for &quot;{query}&quot;">
      <meta name="loc:tocFilter" content="Filter by title">
      <meta name="loc:nextArticle" content="Next">
      <meta name="loc:prevArticle" content="Previous">
      <meta name="loc:themeLight" content="Light">
      <meta name="loc:themeDark" content="Dark">
      <meta name="loc:themeAuto" content="Auto">
      <meta name="loc:changeTheme" content="Change theme">
      <meta name="loc:copy" content="Copy">
      <meta name="loc:downloadPdf" content="Download PDF">

      <script type="module" src="./../public/docfx.min.js"></script>

      <script>
        const theme = localStorage.getItem('theme') || 'auto'
        document.documentElement.setAttribute('data-bs-theme', theme === 'auto' ? (window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light') : theme)
      </script>

  </head>

  <body class="tex2jax_ignore" data-layout="" data-yaml-mime="ManagedReference">
    <header class="bg-body border-bottom">
      <nav id="autocollapse" class="navbar navbar-expand-md" role="navigation">
        <div class="container-xxl flex-nowrap">
          <a class="navbar-brand" href="../index.html">
            <img id="logo" class="svg" src="../logo.svg" alt="Hot Preview">
            Hot Preview
          </a>
          <button class="btn btn-lg d-md-none border-0" type="button" data-bs-toggle="collapse" data-bs-target="#navpanel" aria-controls="navpanel" aria-expanded="false" aria-label="Toggle navigation">
            <i class="bi bi-three-dots"></i>
          </button>
          <div class="collapse navbar-collapse" id="navpanel">
            <div id="navbar">
              <form class="search" role="search" id="search">
                <i class="bi bi-search"></i>
                <input class="form-control" id="search-query" type="search" disabled placeholder="Search" autocomplete="off" aria-label="Search">
              </form>
            </div>
          </div>
        </div>
      </nav>
    </header>

    <main class="container-xxl">
      <div class="toc-offcanvas">
        <div class="offcanvas-md offcanvas-start" tabindex="-1" id="tocOffcanvas" aria-labelledby="tocOffcanvasLabel">
          <div class="offcanvas-header">
            <h5 class="offcanvas-title" id="tocOffcanvasLabel">Table of Contents</h5>
            <button type="button" class="btn-close" data-bs-dismiss="offcanvas" data-bs-target="#tocOffcanvas" aria-label="Close"></button>
          </div>
          <div class="offcanvas-body">
            <nav class="toc" id="toc"></nav>
          </div>
        </div>
      </div>

      <div class="content">
        <div class="actionbar">
          <button class="btn btn-lg border-0 d-md-none" type="button" data-bs-toggle="offcanvas" data-bs-target="#tocOffcanvas" aria-controls="tocOffcanvas" aria-expanded="false" aria-label="Show table of contents">
            <i class="bi bi-list"></i>
          </button>

          <nav id="breadcrumb"></nav>
        </div>

        <article data-uid="HotPreview.SharedModel.App.UIComponentReflection">



  <h1 id="HotPreview_SharedModel_App_UIComponentReflection" data-uid="HotPreview.SharedModel.App.UIComponentReflection" class="text-break">
Class UIComponentReflection  <a class="header-action link-secondary" title="View source" href="https://github.com/BretJohnson/hot-preview/blob/main/src/HotPreview.SharedModel/App/UIComponentReflection.cs/#L8"><i class="bi bi-code-slash"></i></a>
  </h1>

  <div class="facts text-secondary">
    <dl><dt>Namespace</dt><dd><a class="xref" href="HotPreview.html">HotPreview</a>.<a class="xref" href="HotPreview.SharedModel.html">SharedModel</a>.<a class="xref" href="HotPreview.SharedModel.App.html">App</a></dd></dl>
  <dl><dt>Assembly</dt><dd>HotPreview.SharedModel.dll</dd></dl>
  </div>

  <div class="markdown summary"></div>
  <div class="markdown conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public class UIComponentReflection : UIComponentBase&lt;PreviewReflection&gt;</code></pre>
  </div>




  <dl class="typelist inheritance">
    <dt>Inheritance</dt>
    <dd>
      <div><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object">object</a></div>
      <div><a class="xref" href="HotPreview.SharedModel.UIComponentBase-1.html">UIComponentBase</a>&lt;<a class="xref" href="HotPreview.SharedModel.App.PreviewReflection.html">PreviewReflection</a>&gt;</div>
      <div><span class="xref">UIComponentReflection</span></div>
    </dd>
  </dl>



  <dl class="typelist inheritedMembers">
    <dt>Inherited Members</dt>
    <dd>
    <div>
      <a class="xref" href="HotPreview.SharedModel.UIComponentBase-1.html#HotPreview_SharedModel_UIComponentBase_1_Category">UIComponentBase&lt;PreviewReflection&gt;.Category</a>
    </div>
    <div>
      <a class="xref" href="HotPreview.SharedModel.UIComponentBase-1.html#HotPreview_SharedModel_UIComponentBase_1_Kind">UIComponentBase&lt;PreviewReflection&gt;.Kind</a>
    </div>
    <div>
      <a class="xref" href="HotPreview.SharedModel.UIComponentBase-1.html#HotPreview_SharedModel_UIComponentBase_1_DisplayName">UIComponentBase&lt;PreviewReflection&gt;.DisplayName</a>
    </div>
    <div>
      <a class="xref" href="HotPreview.SharedModel.UIComponentBase-1.html#HotPreview_SharedModel_UIComponentBase_1_DisplayNameOverride">UIComponentBase&lt;PreviewReflection&gt;.DisplayNameOverride</a>
    </div>
    <div>
      <a class="xref" href="HotPreview.SharedModel.UIComponentBase-1.html#HotPreview_SharedModel_UIComponentBase_1_PathIcon">UIComponentBase&lt;PreviewReflection&gt;.PathIcon</a>
    </div>
    <div>
      <a class="xref" href="HotPreview.SharedModel.UIComponentBase-1.html#HotPreview_SharedModel_UIComponentBase_1_HasPreview">UIComponentBase&lt;PreviewReflection&gt;.HasPreview</a>
    </div>
    <div>
      <a class="xref" href="HotPreview.SharedModel.UIComponentBase-1.html#HotPreview_SharedModel_UIComponentBase_1_HasNoPreviews">UIComponentBase&lt;PreviewReflection&gt;.HasNoPreviews</a>
    </div>
    <div>
      <a class="xref" href="HotPreview.SharedModel.UIComponentBase-1.html#HotPreview_SharedModel_UIComponentBase_1_HasSinglePreview">UIComponentBase&lt;PreviewReflection&gt;.HasSinglePreview</a>
    </div>
    <div>
      <a class="xref" href="HotPreview.SharedModel.UIComponentBase-1.html#HotPreview_SharedModel_UIComponentBase_1_HasMultiplePreviews">UIComponentBase&lt;PreviewReflection&gt;.HasMultiplePreviews</a>
    </div>
    <div>
      <a class="xref" href="HotPreview.SharedModel.UIComponentBase-1.html#HotPreview_SharedModel_UIComponentBase_1_Previews">UIComponentBase&lt;PreviewReflection&gt;.Previews</a>
    </div>
    <div>
      <a class="xref" href="HotPreview.SharedModel.UIComponentBase-1.html#HotPreview_SharedModel_UIComponentBase_1_GetPreview_System_String_">UIComponentBase&lt;PreviewReflection&gt;.GetPreview(string)</a>
    </div>
    <div>
      <a class="xref" href="HotPreview.SharedModel.UIComponentBase-1.html#HotPreview_SharedModel_UIComponentBase_1_DefaultPreview">UIComponentBase&lt;PreviewReflection&gt;.DefaultPreview</a>
    </div>
    <div>
      <a class="xref" href="HotPreview.SharedModel.UIComponentBase-1.html#HotPreview_SharedModel_UIComponentBase_1_IsAutoGenerated">UIComponentBase&lt;PreviewReflection&gt;.IsAutoGenerated</a>
    </div>
    <div>
      <a class="xref" href="HotPreview.SharedModel.UIComponentBase-1.html#HotPreview_SharedModel_UIComponentBase_1_GetUpdatedPreviews__0_">UIComponentBase&lt;PreviewReflection&gt;.GetUpdatedPreviews(PreviewReflection)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)">object.Equals(object)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)">object.Equals(object, object)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object.gethashcode">object.GetHashCode()</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object.gettype">object.GetType()</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone">object.MemberwiseClone()</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object.referenceequals">object.ReferenceEquals(object, object)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object.tostring">object.ToString()</a>
    </div>
  </dd></dl>






  <h2 class="section" id="constructors">Constructors
</h2>


  <a id="HotPreview_SharedModel_App_UIComponentReflection__ctor_" data-uid="HotPreview.SharedModel.App.UIComponentReflection.#ctor*"></a>

  <h3 id="HotPreview_SharedModel_App_UIComponentReflection__ctor_System_Type_HotPreview_SharedModel_UIComponentKind_System_String_System_Collections_Generic_IReadOnlyList_HotPreview_SharedModel_App_PreviewReflection__" data-uid="HotPreview.SharedModel.App.UIComponentReflection.#ctor(System.Type,HotPreview.SharedModel.UIComponentKind,System.String,System.Collections.Generic.IReadOnlyList{HotPreview.SharedModel.App.PreviewReflection})">
  UIComponentReflection(Type, UIComponentKind, string?, IReadOnlyList&lt;PreviewReflection&gt;)
  <a class="header-action link-secondary" title="View source" href="https://github.com/BretJohnson/hot-preview/blob/main/src/HotPreview.SharedModel/App/UIComponentReflection.cs/#L8"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public UIComponentReflection(Type type, UIComponentKind kind, string? displayNameOverride, IReadOnlyList&lt;PreviewReflection&gt; previews)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>type</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.type">Type</a></dt>
    <dd></dd>
    <dt><code>kind</code> <a class="xref" href="HotPreview.SharedModel.UIComponentKind.html">UIComponentKind</a></dt>
    <dd></dd>
    <dt><code>displayNameOverride</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.string">string</a></dt>
    <dd></dd>
    <dt><code>previews</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.collections.generic.ireadonlylist-1">IReadOnlyList</a>&lt;<a class="xref" href="HotPreview.SharedModel.App.PreviewReflection.html">PreviewReflection</a>&gt;</dt>
    <dd></dd>
  </dl>












  <h2 class="section" id="properties">Properties
</h2>


  <a id="HotPreview_SharedModel_App_UIComponentReflection_Name_" data-uid="HotPreview.SharedModel.App.UIComponentReflection.Name*"></a>

  <h3 id="HotPreview_SharedModel_App_UIComponentReflection_Name" data-uid="HotPreview.SharedModel.App.UIComponentReflection.Name">
  Name
  <a class="header-action link-secondary" title="View source" href="https://github.com/BretJohnson/hot-preview/blob/main/src/HotPreview.SharedModel/App/UIComponentReflection.cs/#L13"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"><p>Name is intended to be what's used by the code to identify the component. It's the component's
full qualified type name and is unique.</p>
</div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public override string Name { get; }</code></pre>
  </div>





  <h4 class="section">Property Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.string">string</a></dt>
    <dd></dd>
  </dl>








  <a id="HotPreview_SharedModel_App_UIComponentReflection_Type_" data-uid="HotPreview.SharedModel.App.UIComponentReflection.Type*"></a>

  <h3 id="HotPreview_SharedModel_App_UIComponentReflection_Type" data-uid="HotPreview.SharedModel.App.UIComponentReflection.Type">
  Type
  <a class="header-action link-secondary" title="View source" href="https://github.com/BretJohnson/hot-preview/blob/main/src/HotPreview.SharedModel/App/UIComponentReflection.cs/#L11"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public Type Type { get; }</code></pre>
  </div>





  <h4 class="section">Property Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.type">Type</a></dt>
    <dd></dd>
  </dl>








  <h2 class="section" id="methods">Methods
</h2>


  <a id="HotPreview_SharedModel_App_UIComponentReflection_GetUIComponentInfo_" data-uid="HotPreview.SharedModel.App.UIComponentReflection.GetUIComponentInfo*"></a>

  <h3 id="HotPreview_SharedModel_App_UIComponentReflection_GetUIComponentInfo" data-uid="HotPreview.SharedModel.App.UIComponentReflection.GetUIComponentInfo">
  GetUIComponentInfo()
  <a class="header-action link-secondary" title="View source" href="https://github.com/BretJohnson/hot-preview/blob/main/src/HotPreview.SharedModel/App/UIComponentReflection.cs/#L22"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"><p>Gets the UI component information including name, display name, and preview information.</p>
</div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public UIComponentInfo GetUIComponentInfo()</code></pre>
  </div>


  <h4 class="section">Returns</h4>
  <dl class="parameters">
    <dt><a class="xref" href="HotPreview.SharedModel.Protocol.UIComponentInfo.html">UIComponentInfo</a></dt>
    <dd><p>A UIComponentInfo record with the component details, for use in the JSON RPC protocol</p>
</dd>
  </dl>











  <a id="HotPreview_SharedModel_App_UIComponentReflection_WithAddedPreview_" data-uid="HotPreview.SharedModel.App.UIComponentReflection.WithAddedPreview*"></a>

  <h3 id="HotPreview_SharedModel_App_UIComponentReflection_WithAddedPreview_HotPreview_SharedModel_App_PreviewReflection_" data-uid="HotPreview.SharedModel.App.UIComponentReflection.WithAddedPreview(HotPreview.SharedModel.App.PreviewReflection)">
  WithAddedPreview(PreviewReflection)
  <a class="header-action link-secondary" title="View source" href="https://github.com/BretJohnson/hot-preview/blob/main/src/HotPreview.SharedModel/App/UIComponentReflection.cs/#L15"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"><p>Creates a copy of this UI component with an additional preview.
If the new preview is not auto-generated, removes any auto-generated previews from the result.</p>
</div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public override UIComponentBase&lt;PreviewReflection&gt; WithAddedPreview(PreviewReflection preview)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>preview</code> <a class="xref" href="HotPreview.SharedModel.App.PreviewReflection.html">PreviewReflection</a></dt>
    <dd><p>The preview to add</p>
</dd>
  </dl>

  <h4 class="section">Returns</h4>
  <dl class="parameters">
    <dt><a class="xref" href="HotPreview.SharedModel.UIComponentBase-1.html">UIComponentBase</a>&lt;<a class="xref" href="HotPreview.SharedModel.App.PreviewReflection.html">PreviewReflection</a>&gt;</dt>
    <dd><p>A new UI component instance with the added preview</p>
</dd>
  </dl>












</article>

        <div class="contribution d-print-none">
          <a href="https://github.com/BretJohnson/hot-preview/blob/main/src/HotPreview.SharedModel/App/UIComponentReflection.cs/#L8" class="edit-link">Edit this page</a>
        </div>


      </div>

      <div class="affix">
        <nav id="affix"></nav>
      </div>
    </main>

    <div class="container-xxl search-results" id="search-results"></div>

    <footer class="border-top text-secondary">
      <div class="container-xxl">
        <div class="flex-fill">
          <span>Made with <a href="https://dotnet.github.io/docfx">docfx</a></span>
        </div>
      </div>
    </footer>
  </body>
</html>
