{"source_base_path": "Q:/src/hot-preview/docs", "xrefmap": "xrefmap.yml", "files": [{"type": "Resource", "output": {"resource": {"relative_path": "index.json"}}}, {"type": "ManagedReference", "source_relative_path": "api/HotPreview.AutoGeneratePreviewAttribute.yml", "output": {".html": {"relative_path": "api/HotPreview.AutoGeneratePreviewAttribute.html"}}, "version": "", "Uid": null, "IsMRef": true, "Title": "HotPreview.AutoGeneratePreviewAttribute", "Summary": "<p sourcefile=\"api/HotPreview.AutoGeneratePreviewAttribute.yml\" sourcestartlinenumber=\"1\">Controls whether auto-generated previews should be created for a UI component.</p>\n"}, {"type": "ManagedReference", "source_relative_path": "api/HotPreview.ControlUIComponentBaseTypeAttribute.yml", "output": {".html": {"relative_path": "api/HotPreview.ControlUIComponentBaseTypeAttribute.html"}}, "version": "", "Uid": null, "IsMRef": true, "Title": "HotPreview.ControlUIComponentBaseTypeAttribute", "Summary": "<p sourcefile=\"api/HotPreview.ControlUIComponentBaseTypeAttribute.yml\" sourcestartlinenumber=\"1\">Specifies the base type for control UI components on a specific platform.</p>\n"}, {"type": "ManagedReference", "source_relative_path": "api/HotPreview.NameUtilities.yml", "output": {".html": {"relative_path": "api/HotPreview.NameUtilities.html"}}, "version": "", "Uid": null, "IsMRef": true, "Title": "HotPreview.NameUtilities", "Summary": null}, {"type": "ManagedReference", "source_relative_path": "api/HotPreview.PageUIComponentBaseTypeAttribute.yml", "output": {".html": {"relative_path": "api/HotPreview.PageUIComponentBaseTypeAttribute.html"}}, "version": "", "Uid": null, "IsMRef": true, "Title": "HotPreview.PageUIComponentBaseTypeAttribute", "Summary": "<p sourcefile=\"api/HotPreview.PageUIComponentBaseTypeAttribute.yml\" sourcestartlinenumber=\"1\">Specifies the base type for page UI components on a specific platform.</p>\n"}, {"type": "ManagedReference", "source_relative_path": "api/HotPreview.PreviewAttribute-1.yml", "output": {".html": {"relative_path": "api/HotPreview.PreviewAttribute-1.html"}}, "version": "", "Uid": null, "IsMRef": true, "Title": "HotPreview.PreviewAttribute<TUIComponent>", "Summary": "<p sourcefile=\"api/HotPreview.PreviewAttribute-1.yml\" sourcestartlinenumber=\"1\">Specifies that this static method creates a preview for a UI component with an explicitly specified UI component\ntype.</p>\n"}, {"type": "ManagedReference", "source_relative_path": "api/HotPreview.PreviewAttribute.yml", "output": {".html": {"relative_path": "api/HotPreview.PreviewAttribute.html"}}, "version": "", "Uid": null, "IsMRef": true, "Title": "HotPreview.PreviewAttribute", "Summary": "<p sourcefile=\"api/HotPreview.PreviewAttribute.yml\" sourcestartlinenumber=\"1\">Specifies that this static method creates a preview for a UI component.</p>\n"}, {"type": "ManagedReference", "source_relative_path": "api/HotPreview.PreviewCommandAttribute.yml", "output": {".html": {"relative_path": "api/HotPreview.PreviewCommandAttribute.html"}}, "version": "", "Uid": null, "IsMRef": true, "Title": "HotPreview.PreviewCommandAttribute", "Summary": "<p sourcefile=\"api/HotPreview.PreviewCommandAttribute.yml\" sourcestartlinenumber=\"1\">Designates this method as a command that can be executed from the DevTools UI or as an MCP tool command.</p>\n"}, {"type": "ManagedReference", "source_relative_path": "api/HotPreview.PreviewMode.yml", "output": {".html": {"relative_path": "api/HotPreview.PreviewMode.html"}}, "version": "", "Uid": null, "IsMRef": true, "Title": "HotPreview.PreviewMode", "Summary": null}, {"type": "ManagedReference", "source_relative_path": "api/HotPreview.PreviewssAttribute.yml", "output": {".html": {"relative_path": "api/HotPreview.PreviewssAttribute.html"}}, "version": "", "Uid": null, "IsMRef": true, "Title": "HotPreview.PreviewssAttribute", "Summary": "<p sourcefile=\"api/HotPreview.PreviewssAttribute.yml\" sourcestartlinenumber=\"1\">An attribute that specifies metadata for UI component that has previews.\nIt can be used explicitly specify a Title, overriding the default title\nof the type name.</p>\n"}, {"type": "ManagedReference", "source_relative_path": "api/HotPreview.RoutePreview-1.yml", "output": {".html": {"relative_path": "api/HotPreview.RoutePreview-1.html"}}, "version": "", "Uid": null, "IsMRef": true, "Title": "HotPreview.RoutePreview<T>", "Summary": "<p sourcefile=\"api/HotPreview.RoutePreview-1.yml\" sourcestartlinenumber=\"1\">Represents a strongly-typed preview for a route-based navigation.</p>\n"}, {"type": "ManagedReference", "source_relative_path": "api/HotPreview.RoutePreview.yml", "output": {".html": {"relative_path": "api/HotPreview.RoutePreview.html"}}, "version": "", "Uid": null, "IsMRef": true, "Title": "HotPreview.RoutePreview", "Summary": "<p sourcefile=\"api/HotPreview.RoutePreview.yml\" sourcestartlinenumber=\"1\">Represents a preview for a route-based navigation.</p>\n"}, {"type": "ManagedReference", "source_relative_path": "api/HotPreview.SharedModel.App.GetPreviewsViaReflection.yml", "output": {".html": {"relative_path": "api/HotPreview.SharedModel.App.GetPreviewsViaReflection.html"}}, "version": "", "Uid": null, "IsMRef": true, "Title": "HotPreview.SharedModel.App.GetPreviewsViaReflection", "Summary": null}, {"type": "ManagedReference", "source_relative_path": "api/HotPreview.SharedModel.App.IPreviewNavigator.yml", "output": {".html": {"relative_path": "api/HotPreview.SharedModel.App.IPreviewNavigator.html"}}, "version": "", "Uid": null, "IsMRef": true, "Title": "HotPreview.SharedModel.App.IPreviewNavigator", "Summary": null}, {"type": "ManagedReference", "source_relative_path": "api/HotPreview.SharedModel.App.PreviewAppService.yml", "output": {".html": {"relative_path": "api/HotPreview.SharedModel.App.PreviewAppService.html"}}, "version": "", "Uid": null, "IsMRef": true, "Title": "HotPreview.SharedModel.App.PreviewAppService", "Summary": null}, {"type": "ManagedReference", "source_relative_path": "api/HotPreview.SharedModel.App.PreviewApplication.yml", "output": {".html": {"relative_path": "api/HotPreview.SharedModel.App.PreviewApplication.html"}}, "version": "", "Uid": null, "IsMRef": true, "Title": "HotPreview.SharedModel.App.PreviewApplication", "Summary": null}, {"type": "ManagedReference", "source_relative_path": "api/HotPreview.SharedModel.App.PreviewClassReflection.yml", "output": {".html": {"relative_path": "api/HotPreview.SharedModel.App.PreviewClassReflection.html"}}, "version": "", "Uid": null, "IsMRef": true, "Title": "HotPreview.SharedModel.App.PreviewClassReflection", "Summary": null}, {"type": "ManagedReference", "source_relative_path": "api/HotPreview.SharedModel.App.PreviewCommandReflection.yml", "output": {".html": {"relative_path": "api/HotPreview.SharedModel.App.PreviewCommandReflection.html"}}, "version": "", "Uid": null, "IsMRef": true, "Title": "HotPreview.SharedModel.App.PreviewCommandReflection", "Summary": "<p sourcefile=\"api/HotPreview.SharedModel.App.PreviewCommandReflection.yml\" sourcestartlinenumber=\"1\">Reflection-based command implementation for static methods marked with [PreviewCommand].</p>\n"}, {"type": "ManagedReference", "source_relative_path": "api/HotPreview.SharedModel.App.PreviewReflection.yml", "output": {".html": {"relative_path": "api/HotPreview.SharedModel.App.PreviewReflection.html"}}, "version": "", "Uid": null, "IsMRef": true, "Title": "HotPreview.SharedModel.App.PreviewReflection", "Summary": null}, {"type": "ManagedReference", "source_relative_path": "api/HotPreview.SharedModel.App.PreviewStaticMethodReflection.yml", "output": {".html": {"relative_path": "api/HotPreview.SharedModel.App.PreviewStaticMethodReflection.html"}}, "version": "", "Uid": null, "IsMRef": true, "Title": "HotPreview.SharedModel.App.PreviewStaticMethodReflection", "Summary": null}, {"type": "ManagedReference", "source_relative_path": "api/HotPreview.SharedModel.App.PreviewsManagerReflection.yml", "output": {".html": {"relative_path": "api/HotPreview.SharedModel.App.PreviewsManagerReflection.html"}}, "version": "", "Uid": null, "IsMRef": true, "Title": "HotPreview.SharedModel.App.PreviewsManagerReflection", "Summary": null}, {"type": "ManagedReference", "source_relative_path": "api/HotPreview.SharedModel.App.ToolingAppClientConnection.yml", "output": {".html": {"relative_path": "api/HotPreview.SharedModel.App.ToolingAppClientConnection.html"}}, "version": "", "Uid": null, "IsMRef": true, "Title": "HotPreview.SharedModel.App.ToolingAppClientConnection", "Summary": null}, {"type": "ManagedReference", "source_relative_path": "api/HotPreview.SharedModel.App.UIComponentPreviewPairReflection.yml", "output": {".html": {"relative_path": "api/HotPreview.SharedModel.App.UIComponentPreviewPairReflection.html"}}, "version": "", "Uid": null, "IsMRef": true, "Title": "HotPreview.SharedModel.App.UIComponentPreviewPairReflection", "Summary": null}, {"type": "ManagedReference", "source_relative_path": "api/HotPreview.SharedModel.App.UIComponentReflection.yml", "output": {".html": {"relative_path": "api/HotPreview.SharedModel.App.UIComponentReflection.html"}}, "version": "", "Uid": null, "IsMRef": true, "Title": "HotPreview.SharedModel.App.UIComponentReflection", "Summary": null}, {"type": "ManagedReference", "source_relative_path": "api/HotPreview.SharedModel.App.yml", "output": {".html": {"relative_path": "api/HotPreview.SharedModel.App.html"}}, "version": "", "Uid": null, "IsMRef": true, "Title": "HotPreview.SharedModel.App", "Summary": null}, {"type": "ManagedReference", "source_relative_path": "api/HotPreview.SharedModel.IUIComponentExclusionFilter.yml", "output": {".html": {"relative_path": "api/HotPreview.SharedModel.IUIComponentExclusionFilter.html"}}, "version": "", "Uid": null, "IsMRef": true, "Title": "HotPreview.SharedModel.IUIComponentExclusionFilter", "Summary": null}, {"type": "ManagedReference", "source_relative_path": "api/HotPreview.SharedModel.PreviewBase.yml", "output": {".html": {"relative_path": "api/HotPreview.SharedModel.PreviewBase.html"}}, "version": "", "Uid": null, "IsMRef": true, "Title": "HotPreview.SharedModel.PreviewBase", "Summary": null}, {"type": "ManagedReference", "source_relative_path": "api/HotPreview.SharedModel.PreviewCommandBase.yml", "output": {".html": {"relative_path": "api/HotPreview.SharedModel.PreviewCommandBase.html"}}, "version": "", "Uid": null, "IsMRef": true, "Title": "HotPreview.SharedModel.PreviewCommandBase", "Summary": "<p sourcefile=\"api/HotPreview.SharedModel.PreviewCommandBase.yml\" sourcestartlinenumber=\"1\">Base class for command reflection, representing commands that can be executed from the DevTools UI.</p>\n"}, {"type": "ManagedReference", "source_relative_path": "api/HotPreview.SharedModel.PreviewNotFoundException.yml", "output": {".html": {"relative_path": "api/HotPreview.SharedModel.PreviewNotFoundException.html"}}, "version": "", "Uid": null, "IsMRef": true, "Title": "HotPreview.SharedModel.PreviewNotFoundException", "Summary": null}, {"type": "ManagedReference", "source_relative_path": "api/HotPreview.SharedModel.PreviewsManagerBase-3.yml", "output": {".html": {"relative_path": "api/HotPreview.SharedModel.PreviewsManagerBase-3.html"}}, "version": "", "Uid": null, "IsMRef": true, "Title": "HotPreview.SharedModel.PreviewsManagerBase<TUIComponent, TPreview, TCommand>", "Summary": null}, {"type": "ManagedReference", "source_relative_path": "api/HotPreview.SharedModel.PreviewsManagerBuilderBase-3.yml", "output": {".html": {"relative_path": "api/HotPreview.SharedModel.PreviewsManagerBuilderBase-3.html"}}, "version": "", "Uid": null, "IsMRef": true, "Title": "HotPreview.SharedModel.PreviewsManagerBuilderBase<TUIComponent, TPreview, TCommand>", "Summary": "<p sourcefile=\"api/HotPreview.SharedModel.PreviewsManagerBuilderBase-3.yml\" sourcestartlinenumber=\"1\">A builder class for constructing PreviewsManager instances.\nThis class provides mutable operations to build up the state before creating an immutable manager.</p>\n"}, {"type": "ManagedReference", "source_relative_path": "api/HotPreview.SharedModel.Protocol.IPreviewAppControllerService.yml", "output": {".html": {"relative_path": "api/HotPreview.SharedModel.Protocol.IPreviewAppControllerService.html"}}, "version": "", "Uid": null, "IsMRef": true, "Title": "HotPreview.SharedModel.Protocol.IPreviewAppControllerService", "Summary": null}, {"type": "ManagedReference", "source_relative_path": "api/HotPreview.SharedModel.Protocol.IPreviewAppService.yml", "output": {".html": {"relative_path": "api/HotPreview.SharedModel.Protocol.IPreviewAppService.html"}}, "version": "", "Uid": null, "IsMRef": true, "Title": "HotPreview.SharedModel.Protocol.IPreviewAppService", "Summary": null}, {"type": "ManagedReference", "source_relative_path": "api/HotPreview.SharedModel.Protocol.PreviewCommandInfo.yml", "output": {".html": {"relative_path": "api/HotPreview.SharedModel.Protocol.PreviewCommandInfo.html"}}, "version": "", "Uid": null, "IsMRef": true, "Title": "HotPreview.SharedModel.Protocol.PreviewCommandInfo", "Summary": null}, {"type": "ManagedReference", "source_relative_path": "api/HotPreview.SharedModel.Protocol.PreviewInfo.yml", "output": {".html": {"relative_path": "api/HotPreview.SharedModel.Protocol.PreviewInfo.html"}}, "version": "", "Uid": null, "IsMRef": true, "Title": "HotPreview.SharedModel.Protocol.PreviewInfo", "Summary": null}, {"type": "ManagedReference", "source_relative_path": "api/HotPreview.SharedModel.Protocol.PreviewTypeInfo.yml", "output": {".html": {"relative_path": "api/HotPreview.SharedModel.Protocol.PreviewTypeInfo.html"}}, "version": "", "Uid": null, "IsMRef": true, "Title": "HotPreview.SharedModel.Protocol.PreviewTypeInfo", "Summary": null}, {"type": "ManagedReference", "source_relative_path": "api/HotPreview.SharedModel.Protocol.UIComponentInfo.yml", "output": {".html": {"relative_path": "api/HotPreview.SharedModel.Protocol.UIComponentInfo.html"}}, "version": "", "Uid": null, "IsMRef": true, "Title": "HotPreview.SharedModel.Protocol.UIComponentInfo", "Summary": null}, {"type": "ManagedReference", "source_relative_path": "api/HotPreview.SharedModel.Protocol.UIComponentKindInfo.yml", "output": {".html": {"relative_path": "api/HotPreview.SharedModel.Protocol.UIComponentKindInfo.html"}}, "version": "", "Uid": null, "IsMRef": true, "Title": "HotPreview.SharedModel.Protocol.UIComponentKindInfo", "Summary": null}, {"type": "ManagedReference", "source_relative_path": "api/HotPreview.SharedModel.Protocol.yml", "output": {".html": {"relative_path": "api/HotPreview.SharedModel.Protocol.html"}}, "version": "", "Uid": null, "IsMRef": true, "Title": "HotPreview.SharedModel.Protocol", "Summary": null}, {"type": "ManagedReference", "source_relative_path": "api/HotPreview.SharedModel.StringUtilities.yml", "output": {".html": {"relative_path": "api/HotPreview.SharedModel.StringUtilities.html"}}, "version": "", "Uid": null, "IsMRef": true, "Title": "HotPreview.SharedModel.StringUtilities", "Summary": null}, {"type": "ManagedReference", "source_relative_path": "api/HotPreview.SharedModel.UIComponentBase-1.yml", "output": {".html": {"relative_path": "api/HotPreview.SharedModel.UIComponentBase-1.html"}}, "version": "", "Uid": null, "IsMRef": true, "Title": "HotPreview.SharedModel.UIComponentBase<TPreview>", "Summary": null}, {"type": "ManagedReference", "source_relative_path": "api/HotPreview.SharedModel.UIComponentBaseTypes.yml", "output": {".html": {"relative_path": "api/HotPreview.SharedModel.UIComponentBaseTypes.html"}}, "version": "", "Uid": null, "IsMRef": true, "Title": "HotPreview.SharedModel.UIComponentBaseTypes", "Summary": null}, {"type": "ManagedReference", "source_relative_path": "api/HotPreview.SharedModel.UIComponentCategory.yml", "output": {".html": {"relative_path": "api/HotPreview.SharedModel.UIComponentCategory.html"}}, "version": "", "Uid": null, "IsMRef": true, "Title": "HotPreview.SharedModel.UIComponentCategory", "Summary": null}, {"type": "ManagedReference", "source_relative_path": "api/HotPreview.SharedModel.UIComponentKind.yml", "output": {".html": {"relative_path": "api/HotPreview.SharedModel.UIComponentKind.html"}}, "version": "", "Uid": null, "IsMRef": true, "Title": "HotPreview.SharedModel.UIComponentKind", "Summary": null}, {"type": "ManagedReference", "source_relative_path": "api/HotPreview.SharedModel.UIComponentNotFoundException.yml", "output": {".html": {"relative_path": "api/HotPreview.SharedModel.UIComponentNotFoundException.html"}}, "version": "", "Uid": null, "IsMRef": true, "Title": "HotPreview.SharedModel.UIComponentNotFoundException", "Summary": null}, {"type": "ManagedReference", "source_relative_path": "api/HotPreview.SharedModel.UIComponentPreviewPair-2.yml", "output": {".html": {"relative_path": "api/HotPreview.SharedModel.UIComponentPreviewPair-2.html"}}, "version": "", "Uid": null, "IsMRef": true, "Title": "HotPreview.SharedModel.UIComponentPreviewPair<TUIComponent, TPreview>", "Summary": null}, {"type": "ManagedReference", "source_relative_path": "api/HotPreview.SharedModel.yml", "output": {".html": {"relative_path": "api/HotPreview.SharedModel.html"}}, "version": "", "Uid": null, "IsMRef": true, "Title": "HotPreview.SharedModel", "Summary": null}, {"type": "ManagedReference", "source_relative_path": "api/HotPreview.SpecialUIComponentNames.yml", "output": {".html": {"relative_path": "api/HotPreview.SpecialUIComponentNames.html"}}, "version": "", "Uid": null, "IsMRef": true, "Title": "HotPreview.SpecialUIComponentNames", "Summary": null}, {"type": "ManagedReference", "source_relative_path": "api/HotPreview.UIComponentAttribute.yml", "output": {".html": {"relative_path": "api/HotPreview.UIComponentAttribute.html"}}, "version": "", "Uid": null, "IsMRef": true, "Title": "HotPreview.UIComponentAttribute", "Summary": "<p sourcefile=\"api/HotPreview.UIComponentAttribute.yml\" sourcestartlinenumber=\"1\">Specifies that this class is a UI component.</p>\n"}, {"type": "ManagedReference", "source_relative_path": "api/HotPreview.UIComponentCategoryAttribute.yml", "output": {".html": {"relative_path": "api/HotPreview.UIComponentCategoryAttribute.html"}}, "version": "", "Uid": null, "IsMRef": true, "Title": "HotPreview.UIComponentCategoryAttribute", "Summary": "<p sourcefile=\"api/HotPreview.UIComponentCategoryAttribute.yml\" sourcestartlinenumber=\"1\">Specifies the category name for a set of UI components.</p>\n"}, {"type": "ManagedReference", "source_relative_path": "api/HotPreview.yml", "output": {".html": {"relative_path": "api/HotPreview.html"}}, "version": "", "Uid": null, "IsMRef": true, "Title": "HotPreview", "Summary": null}, {"type": "Toc", "source_relative_path": "api/toc.yml", "output": {".html": {"relative_path": "api/toc.html"}, ".json": {"relative_path": "api/toc.json"}}, "version": ""}, {"type": "Conceptual", "source_relative_path": "docs/architecture.md", "output": {".html": {"relative_path": "docs/architecture.html"}}, "version": ""}, {"type": "Conceptual", "source_relative_path": "docs/attributes.md", "output": {".html": {"relative_path": "docs/attributes.html"}}, "version": ""}, {"type": "Conceptual", "source_relative_path": "docs/features.md", "output": {".html": {"relative_path": "docs/features.html"}}, "version": ""}, {"type": "Conceptual", "source_relative_path": "docs/getting-started.md", "output": {".html": {"relative_path": "docs/getting-started.html"}}, "version": ""}, {"type": "Conceptual", "source_relative_path": "docs/samples.md", "output": {".html": {"relative_path": "docs/samples.html"}}, "version": ""}, {"type": "Toc", "source_relative_path": "docs/toc.yml", "output": {".html": {"relative_path": "docs/toc.html"}, ".json": {"relative_path": "docs/toc.json"}}, "version": ""}, {"type": "Conceptual", "source_relative_path": "index.md", "output": {".html": {"relative_path": "index.html"}}, "version": ""}, {"type": "Toc", "source_relative_path": "toc.yml", "output": {".html": {"relative_path": "toc.html"}, ".json": {"relative_path": "toc.json"}}, "version": ""}], "groups": [{"xrefmap": "xrefmap.yml"}]}