
<div id="sidetoggle">
  <div>
    <div class="sidefilter">
      <form class="toc-filter">
        <span class="glyphicon glyphicon-filter filter-icon"></span>
        <span class="glyphicon glyphicon-remove clear-icon" id="toc_filter_clear"></span>
        <input type="text" id="toc_filter_input" placeholder="Filter by title" onkeypress="if(event.keyCode==13) {return false;}">
      </form>
    </div>
    <div class="sidetoc">
      <div class="toc" id="toc">

          <ul class="nav level1">
                <li>
                    <a href="getting-started.html" name="" title="Getting Started">Getting Started</a>
                </li>
                <li>
                    <a href="attributes.html" name="" title="Attributes Reference">Attributes Reference</a>
                </li>
                <li>
                    <a href="features.html" name="" title="Features">Features</a>
                </li>
                <li>
                    <a href="architecture.html" name="" title="Architecture">Architecture</a>
                </li>
                <li>
                    <a href="samples.html" name="" title="Sample Applications">Sample Applications</a>
                </li>
          </ul>
      </div>
    </div>
  </div>
</div>
