<!DOCTYPE html>
<html>
  <head>
    <meta charset="utf-8">
      <title>Class PreviewsManagerBuilderBase&lt;TUIComponent, TPreview, TCommand&gt; | Hot Preview Documentation </title>
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <meta name="title" content="Class PreviewsManagerBuilderBase&lt;TUIComponent, TPreview, TCommand&gt; | Hot Preview Documentation ">
      
      <meta name="description" content="A builder class for constructing PreviewsManager instances. This class provides mutable operations to build up the state before creating an immutable manager.">
      <link rel="icon" href="../favicon.ico">
      <link rel="stylesheet" href="../public/docfx.min.css">
      <link rel="stylesheet" href="../public/main.css">
      <meta name="docfx:navrel" content="../toc.html">
      <meta name="docfx:tocrel" content="toc.html">
      
      <meta name="docfx:rel" content="../">
      
      
      <meta name="docfx:docurl" content="https://github.com/BretJohnson/hot-preview/new/main/apiSpec/new?filename=HotPreview_SharedModel_PreviewsManagerBuilderBase_3.md&amp;value=---%0Auid%3A%20HotPreview.SharedModel.PreviewsManagerBuilderBase%603%0Asummary%3A%20&#39;*You%20can%20override%20summary%20for%20the%20API%20here%20using%20*MARKDOWN*%20syntax&#39;%0A---%0A%0A*Please%20type%20below%20more%20information%20about%20this%20API%3A*%0A%0A">
      <meta name="loc:inThisArticle" content="In this article">
      <meta name="loc:searchResultsCount" content="{count} results for &quot;{query}&quot;">
      <meta name="loc:searchNoResults" content="No results for &quot;{query}&quot;">
      <meta name="loc:tocFilter" content="Filter by title">
      <meta name="loc:nextArticle" content="Next">
      <meta name="loc:prevArticle" content="Previous">
      <meta name="loc:themeLight" content="Light">
      <meta name="loc:themeDark" content="Dark">
      <meta name="loc:themeAuto" content="Auto">
      <meta name="loc:changeTheme" content="Change theme">
      <meta name="loc:copy" content="Copy">
      <meta name="loc:downloadPdf" content="Download PDF">

      <script type="module" src="./../public/docfx.min.js"></script>

      <script>
        const theme = localStorage.getItem('theme') || 'auto'
        document.documentElement.setAttribute('data-bs-theme', theme === 'auto' ? (window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light') : theme)
      </script>

  </head>

  <body class="tex2jax_ignore" data-layout="" data-yaml-mime="ManagedReference">
    <header class="bg-body border-bottom">
      <nav id="autocollapse" class="navbar navbar-expand-md" role="navigation">
        <div class="container-xxl flex-nowrap">
          <a class="navbar-brand" href="../index.html">
            <img id="logo" class="svg" src="../logo.svg" alt="Hot Preview">
            Hot Preview
          </a>
          <button class="btn btn-lg d-md-none border-0" type="button" data-bs-toggle="collapse" data-bs-target="#navpanel" aria-controls="navpanel" aria-expanded="false" aria-label="Toggle navigation">
            <i class="bi bi-three-dots"></i>
          </button>
          <div class="collapse navbar-collapse" id="navpanel">
            <div id="navbar">
              <form class="search" role="search" id="search">
                <i class="bi bi-search"></i>
                <input class="form-control" id="search-query" type="search" disabled placeholder="Search" autocomplete="off" aria-label="Search">
              </form>
            </div>
          </div>
        </div>
      </nav>
    </header>

    <main class="container-xxl">
      <div class="toc-offcanvas">
        <div class="offcanvas-md offcanvas-start" tabindex="-1" id="tocOffcanvas" aria-labelledby="tocOffcanvasLabel">
          <div class="offcanvas-header">
            <h5 class="offcanvas-title" id="tocOffcanvasLabel">Table of Contents</h5>
            <button type="button" class="btn-close" data-bs-dismiss="offcanvas" data-bs-target="#tocOffcanvas" aria-label="Close"></button>
          </div>
          <div class="offcanvas-body">
            <nav class="toc" id="toc"></nav>
          </div>
        </div>
      </div>

      <div class="content">
        <div class="actionbar">
          <button class="btn btn-lg border-0 d-md-none" type="button" data-bs-toggle="offcanvas" data-bs-target="#tocOffcanvas" aria-controls="tocOffcanvas" aria-expanded="false" aria-label="Show table of contents">
            <i class="bi bi-list"></i>
          </button>

          <nav id="breadcrumb"></nav>
        </div>

        <article data-uid="HotPreview.SharedModel.PreviewsManagerBuilderBase`3">



  <h1 id="HotPreview_SharedModel_PreviewsManagerBuilderBase_3" data-uid="HotPreview.SharedModel.PreviewsManagerBuilderBase`3" class="text-break">
Class PreviewsManagerBuilderBase&lt;TUIComponent, TPreview, TCommand&gt;  <a class="header-action link-secondary" title="View source" href="https://github.com/BretJohnson/hot-preview/blob/main/src/HotPreview.SharedModel/PreviewsManagerBuilderBase.cs/#L13"><i class="bi bi-code-slash"></i></a>
  </h1>

  <div class="facts text-secondary">
    <dl><dt>Namespace</dt><dd><a class="xref" href="HotPreview.html">HotPreview</a>.<a class="xref" href="HotPreview.SharedModel.html">SharedModel</a></dd></dl>
  <dl><dt>Assembly</dt><dd>HotPreview.SharedModel.dll</dd></dl>
  </div>

  <div class="markdown summary"><p>A builder class for constructing PreviewsManager instances.
This class provides mutable operations to build up the state before creating an immutable manager.</p>
</div>
  <div class="markdown conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public class PreviewsManagerBuilderBase&lt;TUIComponent, TPreview, TCommand&gt; where TUIComponent : UIComponentBase&lt;TPreview&gt; where TPreview : PreviewBase where TCommand : PreviewCommandBase</code></pre>
  </div>



  <h4 class="section">Type Parameters</h4>
  <dl class="parameters">
    <dt><code>TUIComponent</code></dt>
    <dd><p>The type of UI component</p>
</dd>
    <dt><code>TPreview</code></dt>
    <dd><p>The type of preview</p>
</dd>
    <dt><code>TCommand</code></dt>
    <dd><p>The type of command</p>
</dd>
  </dl>

  <dl class="typelist inheritance">
    <dt>Inheritance</dt>
    <dd>
      <div><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object">object</a></div>
      <div><span class="xref">PreviewsManagerBuilderBase&lt;TUIComponent, TPreview, TCommand&gt;</span></div>
    </dd>
  </dl>


  <dl class="typelist derived">
    <dt>Derived</dt>
    <dd>
      <div><a class="xref" href="HotPreview.SharedModel.App.GetPreviewsViaReflection.html">GetPreviewsViaReflection</a></div>
    </dd>
  </dl>

  <dl class="typelist inheritedMembers">
    <dt>Inherited Members</dt>
    <dd>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)">object.Equals(object)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)">object.Equals(object, object)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object.gethashcode">object.GetHashCode()</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object.gettype">object.GetType()</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone">object.MemberwiseClone()</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object.referenceequals">object.ReferenceEquals(object, object)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object.tostring">object.ToString()</a>
    </div>
  </dd></dl>






  <h2 class="section" id="constructors">Constructors
</h2>


  <a id="HotPreview_SharedModel_PreviewsManagerBuilderBase_3__ctor_" data-uid="HotPreview.SharedModel.PreviewsManagerBuilderBase`3.#ctor*"></a>

  <h3 id="HotPreview_SharedModel_PreviewsManagerBuilderBase_3__ctor" data-uid="HotPreview.SharedModel.PreviewsManagerBuilderBase`3.#ctor">
  PreviewsManagerBuilderBase()
  <a class="header-action link-secondary" title="View source" href="https://github.com/BretJohnson/hot-preview/blob/main/src/HotPreview.SharedModel/PreviewsManagerBuilderBase.cs/#L26"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"><p>Initializes a new instance of the PreviewsManagerBuilderBase class.</p>
</div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public PreviewsManagerBuilderBase()</code></pre>
  </div>













  <h2 class="section" id="fields">Fields
</h2>



  <h3 id="HotPreview_SharedModel_PreviewsManagerBuilderBase_3__baseTypes" data-uid="HotPreview.SharedModel.PreviewsManagerBuilderBase`3._baseTypes">
  _baseTypes
  <a class="header-action link-secondary" title="View source" href="https://github.com/BretJohnson/hot-preview/blob/main/src/HotPreview.SharedModel/PreviewsManagerBuilderBase.cs/#L20"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">protected readonly Dictionary&lt;(UIComponentKind kind, string platform), List&lt;string&gt;&gt; _baseTypes</code></pre>
  </div>




  <h4 class="section">Field Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.collections.generic.dictionary-2">Dictionary</a>&lt;(<a class="xref" href="HotPreview.SharedModel.UIComponentKind.html">UIComponentKind</a> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.valuetuple-hotpreview.sharedmodel.uicomponentkind,system.string-.kind">kind</a>, <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.string">string</a> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.valuetuple-hotpreview.sharedmodel.uicomponentkind,system.string-.platform">platform</a>), <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.collections.generic.list-1">List</a>&lt;<a class="xref" href="https://learn.microsoft.com/dotnet/api/system.string">string</a>&gt;&gt;</dt>
    <dd></dd>
  </dl>










  <h3 id="HotPreview_SharedModel_PreviewsManagerBuilderBase_3__categories" data-uid="HotPreview.SharedModel.PreviewsManagerBuilderBase`3._categories">
  _categories
  <a class="header-action link-secondary" title="View source" href="https://github.com/BretJohnson/hot-preview/blob/main/src/HotPreview.SharedModel/PreviewsManagerBuilderBase.cs/#L19"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">protected readonly Dictionary&lt;string, UIComponentCategory&gt; _categories</code></pre>
  </div>




  <h4 class="section">Field Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.collections.generic.dictionary-2">Dictionary</a>&lt;<a class="xref" href="https://learn.microsoft.com/dotnet/api/system.string">string</a>, <a class="xref" href="HotPreview.SharedModel.UIComponentCategory.html">UIComponentCategory</a>&gt;</dt>
    <dd></dd>
  </dl>










  <h3 id="HotPreview_SharedModel_PreviewsManagerBuilderBase_3__commandsByName" data-uid="HotPreview.SharedModel.PreviewsManagerBuilderBase`3._commandsByName">
  _commandsByName
  <a class="header-action link-secondary" title="View source" href="https://github.com/BretJohnson/hot-preview/blob/main/src/HotPreview.SharedModel/PreviewsManagerBuilderBase.cs/#L21"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">protected readonly Dictionary&lt;string, TCommand&gt; _commandsByName</code></pre>
  </div>




  <h4 class="section">Field Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.collections.generic.dictionary-2">Dictionary</a>&lt;<a class="xref" href="https://learn.microsoft.com/dotnet/api/system.string">string</a>, TCommand&gt;</dt>
    <dd></dd>
  </dl>










  <h3 id="HotPreview_SharedModel_PreviewsManagerBuilderBase_3__uiComponentsByName" data-uid="HotPreview.SharedModel.PreviewsManagerBuilderBase`3._uiComponentsByName">
  _uiComponentsByName
  <a class="header-action link-secondary" title="View source" href="https://github.com/BretJohnson/hot-preview/blob/main/src/HotPreview.SharedModel/PreviewsManagerBuilderBase.cs/#L18"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">protected readonly Dictionary&lt;string, TUIComponent&gt; _uiComponentsByName</code></pre>
  </div>




  <h4 class="section">Field Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.collections.generic.dictionary-2">Dictionary</a>&lt;<a class="xref" href="https://learn.microsoft.com/dotnet/api/system.string">string</a>, TUIComponent&gt;</dt>
    <dd></dd>
  </dl>









  <h2 class="section" id="properties">Properties
</h2>


  <a id="HotPreview_SharedModel_PreviewsManagerBuilderBase_3_Categories_" data-uid="HotPreview.SharedModel.PreviewsManagerBuilderBase`3.Categories*"></a>

  <h3 id="HotPreview_SharedModel_PreviewsManagerBuilderBase_3_Categories" data-uid="HotPreview.SharedModel.PreviewsManagerBuilderBase`3.Categories">
  Categories
  <a class="header-action link-secondary" title="View source" href="https://github.com/BretJohnson/hot-preview/blob/main/src/HotPreview.SharedModel/PreviewsManagerBuilderBase.cs/#L35"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public IReadOnlyDictionary&lt;string, UIComponentCategory&gt; Categories { get; }</code></pre>
  </div>





  <h4 class="section">Property Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.collections.generic.ireadonlydictionary-2">IReadOnlyDictionary</a>&lt;<a class="xref" href="https://learn.microsoft.com/dotnet/api/system.string">string</a>, <a class="xref" href="HotPreview.SharedModel.UIComponentCategory.html">UIComponentCategory</a>&gt;</dt>
    <dd></dd>
  </dl>








  <a id="HotPreview_SharedModel_PreviewsManagerBuilderBase_3_CommandsByName_" data-uid="HotPreview.SharedModel.PreviewsManagerBuilderBase`3.CommandsByName*"></a>

  <h3 id="HotPreview_SharedModel_PreviewsManagerBuilderBase_3_CommandsByName" data-uid="HotPreview.SharedModel.PreviewsManagerBuilderBase`3.CommandsByName">
  CommandsByName
  <a class="header-action link-secondary" title="View source" href="https://github.com/BretJohnson/hot-preview/blob/main/src/HotPreview.SharedModel/PreviewsManagerBuilderBase.cs/#L37"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public IReadOnlyDictionary&lt;string, TCommand&gt; CommandsByName { get; }</code></pre>
  </div>





  <h4 class="section">Property Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.collections.generic.ireadonlydictionary-2">IReadOnlyDictionary</a>&lt;<a class="xref" href="https://learn.microsoft.com/dotnet/api/system.string">string</a>, TCommand&gt;</dt>
    <dd></dd>
  </dl>








  <a id="HotPreview_SharedModel_PreviewsManagerBuilderBase_3_UIComponentsByName_" data-uid="HotPreview.SharedModel.PreviewsManagerBuilderBase`3.UIComponentsByName*"></a>

  <h3 id="HotPreview_SharedModel_PreviewsManagerBuilderBase_3_UIComponentsByName" data-uid="HotPreview.SharedModel.PreviewsManagerBuilderBase`3.UIComponentsByName">
  UIComponentsByName
  <a class="header-action link-secondary" title="View source" href="https://github.com/BretJohnson/hot-preview/blob/main/src/HotPreview.SharedModel/PreviewsManagerBuilderBase.cs/#L33"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public IReadOnlyDictionary&lt;string, TUIComponent&gt; UIComponentsByName { get; }</code></pre>
  </div>





  <h4 class="section">Property Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.collections.generic.ireadonlydictionary-2">IReadOnlyDictionary</a>&lt;<a class="xref" href="https://learn.microsoft.com/dotnet/api/system.string">string</a>, TUIComponent&gt;</dt>
    <dd></dd>
  </dl>








  <h2 class="section" id="methods">Methods
</h2>


  <a id="HotPreview_SharedModel_PreviewsManagerBuilderBase_3_AddCategory_" data-uid="HotPreview.SharedModel.PreviewsManagerBuilderBase`3.AddCategory*"></a>

  <h3 id="HotPreview_SharedModel_PreviewsManagerBuilderBase_3_AddCategory_HotPreview_SharedModel_UIComponentCategory_" data-uid="HotPreview.SharedModel.PreviewsManagerBuilderBase`3.AddCategory(HotPreview.SharedModel.UIComponentCategory)">
  AddCategory(UIComponentCategory)
  <a class="header-action link-secondary" title="View source" href="https://github.com/BretJohnson/hot-preview/blob/main/src/HotPreview.SharedModel/PreviewsManagerBuilderBase.cs/#L111"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"><p>Adds a category to the builder.</p>
</div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void AddCategory(UIComponentCategory category)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>category</code> <a class="xref" href="HotPreview.SharedModel.UIComponentCategory.html">UIComponentCategory</a></dt>
    <dd><p>The category to add</p>
</dd>
  </dl>












  <a id="HotPreview_SharedModel_PreviewsManagerBuilderBase_3_AddCommand_" data-uid="HotPreview.SharedModel.PreviewsManagerBuilderBase`3.AddCommand*"></a>

  <h3 id="HotPreview_SharedModel_PreviewsManagerBuilderBase_3_AddCommand__2_" data-uid="HotPreview.SharedModel.PreviewsManagerBuilderBase`3.AddCommand(`2)">
  AddCommand(TCommand)
  <a class="header-action link-secondary" title="View source" href="https://github.com/BretJohnson/hot-preview/blob/main/src/HotPreview.SharedModel/PreviewsManagerBuilderBase.cs/#L90"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"><p>Adds a command to the builder.</p>
</div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void AddCommand(TCommand command)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>command</code> <span class="xref">TCommand</span></dt>
    <dd><p>The command to add</p>
</dd>
  </dl>









  <h4 class="section">Exceptions</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.argumentexception">ArgumentException</a></dt>
    <dd><p>Thrown when a command with the same name already exists</p>
</dd>
  </dl>



  <a id="HotPreview_SharedModel_PreviewsManagerBuilderBase_3_AddOrUpdateCategory_" data-uid="HotPreview.SharedModel.PreviewsManagerBuilderBase`3.AddOrUpdateCategory*"></a>

  <h3 id="HotPreview_SharedModel_PreviewsManagerBuilderBase_3_AddOrUpdateCategory_System_String_System_Collections_Generic_IReadOnlyList_System_String__" data-uid="HotPreview.SharedModel.PreviewsManagerBuilderBase`3.AddOrUpdateCategory(System.String,System.Collections.Generic.IReadOnlyList{System.String})">
  AddOrUpdateCategory(string, IReadOnlyList&lt;string&gt;)
  <a class="header-action link-secondary" title="View source" href="https://github.com/BretJohnson/hot-preview/blob/main/src/HotPreview.SharedModel/PreviewsManagerBuilderBase.cs/#L120"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"><p>Adds a category if it doesn't exist, or updates it with the additional uiComponentNames if it does.</p>
</div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void AddOrUpdateCategory(string categoryName, IReadOnlyList&lt;string&gt; uiComponentNames)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>categoryName</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.string">string</a></dt>
    <dd><p>The name of the category</p>
</dd>
    <dt><code>uiComponentNames</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.collections.generic.ireadonlylist-1">IReadOnlyList</a>&lt;<a class="xref" href="https://learn.microsoft.com/dotnet/api/system.string">string</a>&gt;</dt>
    <dd></dd>
  </dl>












  <a id="HotPreview_SharedModel_PreviewsManagerBuilderBase_3_AddOrUpdateCommand_" data-uid="HotPreview.SharedModel.PreviewsManagerBuilderBase`3.AddOrUpdateCommand*"></a>

  <h3 id="HotPreview_SharedModel_PreviewsManagerBuilderBase_3_AddOrUpdateCommand__2_" data-uid="HotPreview.SharedModel.PreviewsManagerBuilderBase`3.AddOrUpdateCommand(`2)">
  AddOrUpdateCommand(TCommand)
  <a class="header-action link-secondary" title="View source" href="https://github.com/BretJohnson/hot-preview/blob/main/src/HotPreview.SharedModel/PreviewsManagerBuilderBase.cs/#L102"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"><p>Adds or updates a command in the builder.</p>
</div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void AddOrUpdateCommand(TCommand command)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>command</code> <span class="xref">TCommand</span></dt>
    <dd><p>The command to add or update</p>
</dd>
  </dl>












  <a id="HotPreview_SharedModel_PreviewsManagerBuilderBase_3_AddOrUpdateUIComponent_" data-uid="HotPreview.SharedModel.PreviewsManagerBuilderBase`3.AddOrUpdateUIComponent*"></a>

  <h3 id="HotPreview_SharedModel_PreviewsManagerBuilderBase_3_AddOrUpdateUIComponent__0_" data-uid="HotPreview.SharedModel.PreviewsManagerBuilderBase`3.AddOrUpdateUIComponent(`0)">
  AddOrUpdateUIComponent(TUIComponent)
  <a class="header-action link-secondary" title="View source" href="https://github.com/BretJohnson/hot-preview/blob/main/src/HotPreview.SharedModel/PreviewsManagerBuilderBase.cs/#L80"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"><p>Adds or updates a UI component in the builder.</p>
</div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void AddOrUpdateUIComponent(TUIComponent component)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>component</code> <span class="xref">TUIComponent</span></dt>
    <dd><p>The component to add or update</p>
</dd>
  </dl>












  <a id="HotPreview_SharedModel_PreviewsManagerBuilderBase_3_AddUIComponent_" data-uid="HotPreview.SharedModel.PreviewsManagerBuilderBase`3.AddUIComponent*"></a>

  <h3 id="HotPreview_SharedModel_PreviewsManagerBuilderBase_3_AddUIComponent__0_" data-uid="HotPreview.SharedModel.PreviewsManagerBuilderBase`3.AddUIComponent(`0)">
  AddUIComponent(TUIComponent)
  <a class="header-action link-secondary" title="View source" href="https://github.com/BretJohnson/hot-preview/blob/main/src/HotPreview.SharedModel/PreviewsManagerBuilderBase.cs/#L68"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"><p>Adds a UI component to the builder.</p>
</div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void AddUIComponent(TUIComponent component)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>component</code> <span class="xref">TUIComponent</span></dt>
    <dd><p>The component to add</p>
</dd>
  </dl>









  <h4 class="section">Exceptions</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.argumentexception">ArgumentException</a></dt>
    <dd><p>Thrown when a component with the same name already exists</p>
</dd>
  </dl>



  <a id="HotPreview_SharedModel_PreviewsManagerBuilderBase_3_AddUIComponentBaseType_" data-uid="HotPreview.SharedModel.PreviewsManagerBuilderBase`3.AddUIComponentBaseType*"></a>

  <h3 id="HotPreview_SharedModel_PreviewsManagerBuilderBase_3_AddUIComponentBaseType_HotPreview_SharedModel_UIComponentKind_System_String_System_String_" data-uid="HotPreview.SharedModel.PreviewsManagerBuilderBase`3.AddUIComponentBaseType(HotPreview.SharedModel.UIComponentKind,System.String,System.String)">
  AddUIComponentBaseType(UIComponentKind, string, string)
  <a class="header-action link-secondary" title="View source" href="https://github.com/BretJohnson/hot-preview/blob/main/src/HotPreview.SharedModel/PreviewsManagerBuilderBase.cs/#L45"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"><p>Adds a UI component base type for the specified kind and platform.</p>
</div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void AddUIComponentBaseType(UIComponentKind kind, string platform, string baseType)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>kind</code> <a class="xref" href="HotPreview.SharedModel.UIComponentKind.html">UIComponentKind</a></dt>
    <dd><p>The kind of UI component (Page or Control)</p>
</dd>
    <dt><code>platform</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.string">string</a></dt>
    <dd><p>The platform name</p>
</dd>
    <dt><code>baseType</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.string">string</a></dt>
    <dd><p>The base type name</p>
</dd>
  </dl>












  <a id="HotPreview_SharedModel_PreviewsManagerBuilderBase_3_GetCommand_" data-uid="HotPreview.SharedModel.PreviewsManagerBuilderBase`3.GetCommand*"></a>

  <h3 id="HotPreview_SharedModel_PreviewsManagerBuilderBase_3_GetCommand_System_String_" data-uid="HotPreview.SharedModel.PreviewsManagerBuilderBase`3.GetCommand(System.String)">
  GetCommand(string)
  <a class="header-action link-secondary" title="View source" href="https://github.com/BretJohnson/hot-preview/blob/main/src/HotPreview.SharedModel/PreviewsManagerBuilderBase.cs/#L150"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"><p>Gets a command by name.</p>
</div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public TCommand? GetCommand(string name)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>name</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.string">string</a></dt>
    <dd><p>The name of the command</p>
</dd>
  </dl>

  <h4 class="section">Returns</h4>
  <dl class="parameters">
    <dt><span class="xref">TCommand</span></dt>
    <dd><p>The command if found, otherwise null</p>
</dd>
  </dl>











  <a id="HotPreview_SharedModel_PreviewsManagerBuilderBase_3_GetUIComponent_" data-uid="HotPreview.SharedModel.PreviewsManagerBuilderBase`3.GetUIComponent*"></a>

  <h3 id="HotPreview_SharedModel_PreviewsManagerBuilderBase_3_GetUIComponent_System_String_" data-uid="HotPreview.SharedModel.PreviewsManagerBuilderBase`3.GetUIComponent(System.String)">
  GetUIComponent(string)
  <a class="header-action link-secondary" title="View source" href="https://github.com/BretJohnson/hot-preview/blob/main/src/HotPreview.SharedModel/PreviewsManagerBuilderBase.cs/#L139"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"><p>Gets a UI component by name.</p>
</div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public TUIComponent? GetUIComponent(string name)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>name</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.string">string</a></dt>
    <dd><p>The name of the component</p>
</dd>
  </dl>

  <h4 class="section">Returns</h4>
  <dl class="parameters">
    <dt><span class="xref">TUIComponent</span></dt>
    <dd><p>The component if found, otherwise null</p>
</dd>
  </dl>











  <a id="HotPreview_SharedModel_PreviewsManagerBuilderBase_3_IsUIComponentBaseType_" data-uid="HotPreview.SharedModel.PreviewsManagerBuilderBase`3.IsUIComponentBaseType*"></a>

  <h3 id="HotPreview_SharedModel_PreviewsManagerBuilderBase_3_IsUIComponentBaseType_System_String_HotPreview_SharedModel_UIComponentKind__" data-uid="HotPreview.SharedModel.PreviewsManagerBuilderBase`3.IsUIComponentBaseType(System.String,HotPreview.SharedModel.UIComponentKind@)">
  IsUIComponentBaseType(string, out UIComponentKind)
  <a class="header-action link-secondary" title="View source" href="https://github.com/BretJohnson/hot-preview/blob/main/src/HotPreview.SharedModel/PreviewsManagerBuilderBase.cs/#L162"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"><p>Checks if a type name is a registered UI component base type.</p>
</div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public bool IsUIComponentBaseType(string typeName, out UIComponentKind kind)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>typeName</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.string">string</a></dt>
    <dd><p>The type name to check</p>
</dd>
    <dt><code>kind</code> <a class="xref" href="HotPreview.SharedModel.UIComponentKind.html">UIComponentKind</a></dt>
    <dd><p>The UI component kind if found</p>
</dd>
  </dl>

  <h4 class="section">Returns</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.boolean">bool</a></dt>
    <dd><p>True if the type is a registered base type, false otherwise</p>
</dd>
  </dl>











  <a id="HotPreview_SharedModel_PreviewsManagerBuilderBase_3_Validate_" data-uid="HotPreview.SharedModel.PreviewsManagerBuilderBase`3.Validate*"></a>

  <h3 id="HotPreview_SharedModel_PreviewsManagerBuilderBase_3_Validate" data-uid="HotPreview.SharedModel.PreviewsManagerBuilderBase`3.Validate">
  Validate()
  <a class="header-action link-secondary" title="View source" href="https://github.com/BretJohnson/hot-preview/blob/main/src/HotPreview.SharedModel/PreviewsManagerBuilderBase.cs/#L181"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"><p>Validates the builder state before building.</p>
</div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void Validate()</code></pre>
  </div>










  <h4 class="section">Exceptions</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.invalidoperationexception">InvalidOperationException</a></dt>
    <dd><p>Thrown when the builder state is invalid</p>
</dd>
  </dl>




</article>

        <div class="contribution d-print-none">
          <a href="https://github.com/BretJohnson/hot-preview/blob/main/src/HotPreview.SharedModel/PreviewsManagerBuilderBase.cs/#L13" class="edit-link">Edit this page</a>
        </div>


      </div>

      <div class="affix">
        <nav id="affix"></nav>
      </div>
    </main>

    <div class="container-xxl search-results" id="search-results"></div>

    <footer class="border-top text-secondary">
      <div class="container-xxl">
        <div class="flex-fill">
          <span>Made with <a href="https://dotnet.github.io/docfx">docfx</a></span>
        </div>
      </div>
    </footer>
  </body>
</html>
