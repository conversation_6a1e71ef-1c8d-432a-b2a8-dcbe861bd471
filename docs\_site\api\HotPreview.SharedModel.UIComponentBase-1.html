<!DOCTYPE html>
<html>
  <head>
    <meta charset="utf-8">
      <title>Class UIComponentBase&lt;TPreview&gt; | Hot Preview Documentation </title>
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <meta name="title" content="Class UIComponentBase&lt;TPreview&gt; | Hot Preview Documentation ">
      
      
      <link rel="icon" href="../favicon.ico">
      <link rel="stylesheet" href="../public/docfx.min.css">
      <link rel="stylesheet" href="../public/main.css">
      <meta name="docfx:navrel" content="../toc.html">
      <meta name="docfx:tocrel" content="toc.html">
      
      <meta name="docfx:rel" content="../">
      
      
      <meta name="docfx:docurl" content="https://github.com/BretJohnson/hot-preview/new/main/apiSpec/new?filename=HotPreview_SharedModel_UIComponentBase_1.md&amp;value=---%0Auid%3A%20HotPreview.SharedModel.UIComponentBase%601%0Asummary%3A%20&#39;*You%20can%20override%20summary%20for%20the%20API%20here%20using%20*MARKDOWN*%20syntax&#39;%0A---%0A%0A*Please%20type%20below%20more%20information%20about%20this%20API%3A*%0A%0A">
      <meta name="loc:inThisArticle" content="In this article">
      <meta name="loc:searchResultsCount" content="{count} results for &quot;{query}&quot;">
      <meta name="loc:searchNoResults" content="No results for &quot;{query}&quot;">
      <meta name="loc:tocFilter" content="Filter by title">
      <meta name="loc:nextArticle" content="Next">
      <meta name="loc:prevArticle" content="Previous">
      <meta name="loc:themeLight" content="Light">
      <meta name="loc:themeDark" content="Dark">
      <meta name="loc:themeAuto" content="Auto">
      <meta name="loc:changeTheme" content="Change theme">
      <meta name="loc:copy" content="Copy">
      <meta name="loc:downloadPdf" content="Download PDF">

      <script type="module" src="./../public/docfx.min.js"></script>

      <script>
        const theme = localStorage.getItem('theme') || 'auto'
        document.documentElement.setAttribute('data-bs-theme', theme === 'auto' ? (window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light') : theme)
      </script>

  </head>

  <body class="tex2jax_ignore" data-layout="" data-yaml-mime="ManagedReference">
    <header class="bg-body border-bottom">
      <nav id="autocollapse" class="navbar navbar-expand-md" role="navigation">
        <div class="container-xxl flex-nowrap">
          <a class="navbar-brand" href="../index.html">
            <img id="logo" class="svg" src="../logo.svg" alt="Hot Preview">
            Hot Preview
          </a>
          <button class="btn btn-lg d-md-none border-0" type="button" data-bs-toggle="collapse" data-bs-target="#navpanel" aria-controls="navpanel" aria-expanded="false" aria-label="Toggle navigation">
            <i class="bi bi-three-dots"></i>
          </button>
          <div class="collapse navbar-collapse" id="navpanel">
            <div id="navbar">
              <form class="search" role="search" id="search">
                <i class="bi bi-search"></i>
                <input class="form-control" id="search-query" type="search" disabled placeholder="Search" autocomplete="off" aria-label="Search">
              </form>
            </div>
          </div>
        </div>
      </nav>
    </header>

    <main class="container-xxl">
      <div class="toc-offcanvas">
        <div class="offcanvas-md offcanvas-start" tabindex="-1" id="tocOffcanvas" aria-labelledby="tocOffcanvasLabel">
          <div class="offcanvas-header">
            <h5 class="offcanvas-title" id="tocOffcanvasLabel">Table of Contents</h5>
            <button type="button" class="btn-close" data-bs-dismiss="offcanvas" data-bs-target="#tocOffcanvas" aria-label="Close"></button>
          </div>
          <div class="offcanvas-body">
            <nav class="toc" id="toc"></nav>
          </div>
        </div>
      </div>

      <div class="content">
        <div class="actionbar">
          <button class="btn btn-lg border-0 d-md-none" type="button" data-bs-toggle="offcanvas" data-bs-target="#tocOffcanvas" aria-controls="tocOffcanvas" aria-expanded="false" aria-label="Show table of contents">
            <i class="bi bi-list"></i>
          </button>

          <nav id="breadcrumb"></nav>
        </div>

        <article data-uid="HotPreview.SharedModel.UIComponentBase`1">



  <h1 id="HotPreview_SharedModel_UIComponentBase_1" data-uid="HotPreview.SharedModel.UIComponentBase`1" class="text-break">
Class UIComponentBase&lt;TPreview&gt;  <a class="header-action link-secondary" title="View source" href="https://github.com/BretJohnson/hot-preview/blob/main/src/HotPreview.SharedModel/UIComponentBase.cs/#L7"><i class="bi bi-code-slash"></i></a>
  </h1>

  <div class="facts text-secondary">
    <dl><dt>Namespace</dt><dd><a class="xref" href="HotPreview.html">HotPreview</a>.<a class="xref" href="HotPreview.SharedModel.html">SharedModel</a></dd></dl>
  <dl><dt>Assembly</dt><dd>HotPreview.SharedModel.dll</dd></dl>
  </div>

  <div class="markdown summary"></div>
  <div class="markdown conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public abstract class UIComponentBase&lt;TPreview&gt; where TPreview : PreviewBase</code></pre>
  </div>



  <h4 class="section">Type Parameters</h4>
  <dl class="parameters">
    <dt><code>TPreview</code></dt>
    <dd></dd>
  </dl>

  <dl class="typelist inheritance">
    <dt>Inheritance</dt>
    <dd>
      <div><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object">object</a></div>
      <div><span class="xref">UIComponentBase&lt;TPreview&gt;</span></div>
    </dd>
  </dl>


  <dl class="typelist derived">
    <dt>Derived</dt>
    <dd>
      <div><a class="xref" href="HotPreview.SharedModel.App.UIComponentReflection.html">UIComponentReflection</a></div>
    </dd>
  </dl>

  <dl class="typelist inheritedMembers">
    <dt>Inherited Members</dt>
    <dd>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)">object.Equals(object)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)">object.Equals(object, object)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object.gethashcode">object.GetHashCode()</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object.gettype">object.GetType()</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone">object.MemberwiseClone()</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object.referenceequals">object.ReferenceEquals(object, object)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object.tostring">object.ToString()</a>
    </div>
  </dd></dl>






  <h2 class="section" id="constructors">Constructors
</h2>


  <a id="HotPreview_SharedModel_UIComponentBase_1__ctor_" data-uid="HotPreview.SharedModel.UIComponentBase`1.#ctor*"></a>

  <h3 id="HotPreview_SharedModel_UIComponentBase_1__ctor_HotPreview_SharedModel_UIComponentKind_System_String_System_Collections_Generic_IReadOnlyList__0__" data-uid="HotPreview.SharedModel.UIComponentBase`1.#ctor(HotPreview.SharedModel.UIComponentKind,System.String,System.Collections.Generic.IReadOnlyList{`0})">
  UIComponentBase(UIComponentKind, string?, IReadOnlyList&lt;TPreview&gt;)
  <a class="header-action link-secondary" title="View source" href="https://github.com/BretJohnson/hot-preview/blob/main/src/HotPreview.SharedModel/UIComponentBase.cs/#L7"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">protected UIComponentBase(UIComponentKind kind, string? displayNameOverride, IReadOnlyList&lt;TPreview&gt; previews)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>kind</code> <a class="xref" href="HotPreview.SharedModel.UIComponentKind.html">UIComponentKind</a></dt>
    <dd></dd>
    <dt><code>displayNameOverride</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.string">string</a></dt>
    <dd></dd>
    <dt><code>previews</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.collections.generic.ireadonlylist-1">IReadOnlyList</a>&lt;TPreview&gt;</dt>
    <dd></dd>
  </dl>












  <h2 class="section" id="properties">Properties
</h2>


  <a id="HotPreview_SharedModel_UIComponentBase_1_Category_" data-uid="HotPreview.SharedModel.UIComponentBase`1.Category*"></a>

  <h3 id="HotPreview_SharedModel_UIComponentBase_1_Category" data-uid="HotPreview.SharedModel.UIComponentBase`1.Category">
  Category
  <a class="header-action link-secondary" title="View source" href="https://github.com/BretJohnson/hot-preview/blob/main/src/HotPreview.SharedModel/UIComponentBase.cs/#L17"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public UIComponentCategory? Category { get; }</code></pre>
  </div>





  <h4 class="section">Property Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="HotPreview.SharedModel.UIComponentCategory.html">UIComponentCategory</a></dt>
    <dd></dd>
  </dl>








  <a id="HotPreview_SharedModel_UIComponentBase_1_DefaultPreview_" data-uid="HotPreview.SharedModel.UIComponentBase`1.DefaultPreview*"></a>

  <h3 id="HotPreview_SharedModel_UIComponentBase_1_DefaultPreview" data-uid="HotPreview.SharedModel.UIComponentBase`1.DefaultPreview">
  DefaultPreview
  <a class="header-action link-secondary" title="View source" href="https://github.com/BretJohnson/hot-preview/blob/main/src/HotPreview.SharedModel/UIComponentBase.cs/#L71"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public TPreview DefaultPreview { get; }</code></pre>
  </div>





  <h4 class="section">Property Value</h4>
  <dl class="parameters">
    <dt><span class="xref">TPreview</span></dt>
    <dd></dd>
  </dl>








  <a id="HotPreview_SharedModel_UIComponentBase_1_DisplayName_" data-uid="HotPreview.SharedModel.UIComponentBase`1.DisplayName*"></a>

  <h3 id="HotPreview_SharedModel_UIComponentBase_1_DisplayName" data-uid="HotPreview.SharedModel.UIComponentBase`1.DisplayName">
  DisplayName
  <a class="header-action link-secondary" title="View source" href="https://github.com/BretJohnson/hot-preview/blob/main/src/HotPreview.SharedModel/UIComponentBase.cs/#L26"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"><p>DisplayName is intended to be what's shown in the UI to identify the component. It can contain spaces and
isn't necessarily unique. It defaults to the class name (with no namespace qualifier) but can be
overridden by the developer.</p>
</div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public string DisplayName { get; }</code></pre>
  </div>





  <h4 class="section">Property Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.string">string</a></dt>
    <dd></dd>
  </dl>








  <a id="HotPreview_SharedModel_UIComponentBase_1_DisplayNameOverride_" data-uid="HotPreview.SharedModel.UIComponentBase`1.DisplayNameOverride*"></a>

  <h3 id="HotPreview_SharedModel_UIComponentBase_1_DisplayNameOverride" data-uid="HotPreview.SharedModel.UIComponentBase`1.DisplayNameOverride">
  DisplayNameOverride
  <a class="header-action link-secondary" title="View source" href="https://github.com/BretJohnson/hot-preview/blob/main/src/HotPreview.SharedModel/UIComponentBase.cs/#L28"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public string? DisplayNameOverride { get; }</code></pre>
  </div>





  <h4 class="section">Property Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.string">string</a></dt>
    <dd></dd>
  </dl>








  <a id="HotPreview_SharedModel_UIComponentBase_1_HasMultiplePreviews_" data-uid="HotPreview.SharedModel.UIComponentBase`1.HasMultiplePreviews*"></a>

  <h3 id="HotPreview_SharedModel_UIComponentBase_1_HasMultiplePreviews" data-uid="HotPreview.SharedModel.UIComponentBase`1.HasMultiplePreviews">
  HasMultiplePreviews
  <a class="header-action link-secondary" title="View source" href="https://github.com/BretJohnson/hot-preview/blob/main/src/HotPreview.SharedModel/UIComponentBase.cs/#L54"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public bool HasMultiplePreviews { get; }</code></pre>
  </div>





  <h4 class="section">Property Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.boolean">bool</a></dt>
    <dd></dd>
  </dl>








  <a id="HotPreview_SharedModel_UIComponentBase_1_HasNoPreviews_" data-uid="HotPreview.SharedModel.UIComponentBase`1.HasNoPreviews*"></a>

  <h3 id="HotPreview_SharedModel_UIComponentBase_1_HasNoPreviews" data-uid="HotPreview.SharedModel.UIComponentBase`1.HasNoPreviews">
  HasNoPreviews
  <a class="header-action link-secondary" title="View source" href="https://github.com/BretJohnson/hot-preview/blob/main/src/HotPreview.SharedModel/UIComponentBase.cs/#L50"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public bool HasNoPreviews { get; }</code></pre>
  </div>





  <h4 class="section">Property Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.boolean">bool</a></dt>
    <dd></dd>
  </dl>








  <a id="HotPreview_SharedModel_UIComponentBase_1_HasPreview_" data-uid="HotPreview.SharedModel.UIComponentBase`1.HasPreview*"></a>

  <h3 id="HotPreview_SharedModel_UIComponentBase_1_HasPreview" data-uid="HotPreview.SharedModel.UIComponentBase`1.HasPreview">
  HasPreview
  <a class="header-action link-secondary" title="View source" href="https://github.com/BretJohnson/hot-preview/blob/main/src/HotPreview.SharedModel/UIComponentBase.cs/#L48"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public bool HasPreview { get; }</code></pre>
  </div>





  <h4 class="section">Property Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.boolean">bool</a></dt>
    <dd></dd>
  </dl>








  <a id="HotPreview_SharedModel_UIComponentBase_1_HasSinglePreview_" data-uid="HotPreview.SharedModel.UIComponentBase`1.HasSinglePreview*"></a>

  <h3 id="HotPreview_SharedModel_UIComponentBase_1_HasSinglePreview" data-uid="HotPreview.SharedModel.UIComponentBase`1.HasSinglePreview">
  HasSinglePreview
  <a class="header-action link-secondary" title="View source" href="https://github.com/BretJohnson/hot-preview/blob/main/src/HotPreview.SharedModel/UIComponentBase.cs/#L52"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public bool HasSinglePreview { get; }</code></pre>
  </div>





  <h4 class="section">Property Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.boolean">bool</a></dt>
    <dd></dd>
  </dl>








  <a id="HotPreview_SharedModel_UIComponentBase_1_IsAutoGenerated_" data-uid="HotPreview.SharedModel.UIComponentBase`1.IsAutoGenerated*"></a>

  <h3 id="HotPreview_SharedModel_UIComponentBase_1_IsAutoGenerated" data-uid="HotPreview.SharedModel.UIComponentBase`1.IsAutoGenerated">
  IsAutoGenerated
  <a class="header-action link-secondary" title="View source" href="https://github.com/BretJohnson/hot-preview/blob/main/src/HotPreview.SharedModel/UIComponentBase.cs/#L94"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public bool IsAutoGenerated { get; }</code></pre>
  </div>





  <h4 class="section">Property Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.boolean">bool</a></dt>
    <dd></dd>
  </dl>








  <a id="HotPreview_SharedModel_UIComponentBase_1_Kind_" data-uid="HotPreview.SharedModel.UIComponentBase`1.Kind*"></a>

  <h3 id="HotPreview_SharedModel_UIComponentBase_1_Kind" data-uid="HotPreview.SharedModel.UIComponentBase`1.Kind">
  Kind
  <a class="header-action link-secondary" title="View source" href="https://github.com/BretJohnson/hot-preview/blob/main/src/HotPreview.SharedModel/UIComponentBase.cs/#L19"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public UIComponentKind Kind { get; }</code></pre>
  </div>





  <h4 class="section">Property Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="HotPreview.SharedModel.UIComponentKind.html">UIComponentKind</a></dt>
    <dd></dd>
  </dl>








  <a id="HotPreview_SharedModel_UIComponentBase_1_Name_" data-uid="HotPreview.SharedModel.UIComponentBase`1.Name*"></a>

  <h3 id="HotPreview_SharedModel_UIComponentBase_1_Name" data-uid="HotPreview.SharedModel.UIComponentBase`1.Name">
  Name
  <a class="header-action link-secondary" title="View source" href="https://github.com/BretJohnson/hot-preview/blob/main/src/HotPreview.SharedModel/UIComponentBase.cs/#L15"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"><p>Name is intended to be what's used by the code to identify the component. It's the component's
full qualified type name and is unique.</p>
</div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public abstract string Name { get; }</code></pre>
  </div>





  <h4 class="section">Property Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.string">string</a></dt>
    <dd></dd>
  </dl>








  <a id="HotPreview_SharedModel_UIComponentBase_1_PathIcon_" data-uid="HotPreview.SharedModel.UIComponentBase`1.PathIcon*"></a>

  <h3 id="HotPreview_SharedModel_UIComponentBase_1_PathIcon" data-uid="HotPreview.SharedModel.UIComponentBase`1.PathIcon">
  PathIcon
  <a class="header-action link-secondary" title="View source" href="https://github.com/BretJohnson/hot-preview/blob/main/src/HotPreview.SharedModel/UIComponentBase.cs/#L38"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"><p>Returns a path that can be used as a monochrome icon (e.g. via WinUI's PathIcon). The path here is assumed t
be in a 24x24 viewport (the convention used by Material icons and most Fluent icons). If you have a path with
a different viewport, you can scale it to 24x24 by applying a scale operation with
<a href="https://yqnn.github.io/svg-path-editor/">https://yqnn.github.io/svg-path-editor/</a> or similar tools.</p>
<p>TODO: Make this customizable with UIComponent attribute.</p>
</div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public string PathIcon { get; }</code></pre>
  </div>





  <h4 class="section">Property Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.string">string</a></dt>
    <dd></dd>
  </dl>








  <a id="HotPreview_SharedModel_UIComponentBase_1_Previews_" data-uid="HotPreview.SharedModel.UIComponentBase`1.Previews*"></a>

  <h3 id="HotPreview_SharedModel_UIComponentBase_1_Previews" data-uid="HotPreview.SharedModel.UIComponentBase`1.Previews">
  Previews
  <a class="header-action link-secondary" title="View source" href="https://github.com/BretJohnson/hot-preview/blob/main/src/HotPreview.SharedModel/UIComponentBase.cs/#L56"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public IReadOnlyList&lt;TPreview&gt; Previews { get; }</code></pre>
  </div>





  <h4 class="section">Property Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.collections.generic.ireadonlylist-1">IReadOnlyList</a>&lt;TPreview&gt;</dt>
    <dd></dd>
  </dl>








  <h2 class="section" id="methods">Methods
</h2>


  <a id="HotPreview_SharedModel_UIComponentBase_1_GetPreview_" data-uid="HotPreview.SharedModel.UIComponentBase`1.GetPreview*"></a>

  <h3 id="HotPreview_SharedModel_UIComponentBase_1_GetPreview_System_String_" data-uid="HotPreview.SharedModel.UIComponentBase`1.GetPreview(System.String)">
  GetPreview(string)
  <a class="header-action link-secondary" title="View source" href="https://github.com/BretJohnson/hot-preview/blob/main/src/HotPreview.SharedModel/UIComponentBase.cs/#L58"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public TPreview? GetPreview(string name)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>name</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.string">string</a></dt>
    <dd></dd>
  </dl>

  <h4 class="section">Returns</h4>
  <dl class="parameters">
    <dt><span class="xref">TPreview</span></dt>
    <dd></dd>
  </dl>











  <a id="HotPreview_SharedModel_UIComponentBase_1_GetUpdatedPreviews_" data-uid="HotPreview.SharedModel.UIComponentBase`1.GetUpdatedPreviews*"></a>

  <h3 id="HotPreview_SharedModel_UIComponentBase_1_GetUpdatedPreviews__0_" data-uid="HotPreview.SharedModel.UIComponentBase`1.GetUpdatedPreviews(`0)">
  GetUpdatedPreviews(TPreview)
  <a class="header-action link-secondary" title="View source" href="https://github.com/BretJohnson/hot-preview/blob/main/src/HotPreview.SharedModel/UIComponentBase.cs/#L99"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"><p>Helper method for subclasses to apply the auto-generated preview removal logic for WithAddedPreview.</p>
</div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">protected IReadOnlyList&lt;TPreview&gt; GetUpdatedPreviews(TPreview newPreview)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>newPreview</code> <span class="xref">TPreview</span></dt>
    <dd></dd>
  </dl>

  <h4 class="section">Returns</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.collections.generic.ireadonlylist-1">IReadOnlyList</a>&lt;TPreview&gt;</dt>
    <dd></dd>
  </dl>











  <a id="HotPreview_SharedModel_UIComponentBase_1_WithAddedPreview_" data-uid="HotPreview.SharedModel.UIComponentBase`1.WithAddedPreview*"></a>

  <h3 id="HotPreview_SharedModel_UIComponentBase_1_WithAddedPreview__0_" data-uid="HotPreview.SharedModel.UIComponentBase`1.WithAddedPreview(`0)">
  WithAddedPreview(TPreview)
  <a class="header-action link-secondary" title="View source" href="https://github.com/BretJohnson/hot-preview/blob/main/src/HotPreview.SharedModel/UIComponentBase.cs/#L92"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"><p>Creates a copy of this UI component with an additional preview.
If the new preview is not auto-generated, removes any auto-generated previews from the result.</p>
</div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public abstract UIComponentBase&lt;TPreview&gt; WithAddedPreview(TPreview preview)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>preview</code> <span class="xref">TPreview</span></dt>
    <dd><p>The preview to add</p>
</dd>
  </dl>

  <h4 class="section">Returns</h4>
  <dl class="parameters">
    <dt><a class="xref" href="HotPreview.SharedModel.UIComponentBase-1.html">UIComponentBase</a>&lt;TPreview&gt;</dt>
    <dd><p>A new UI component instance with the added preview</p>
</dd>
  </dl>












</article>

        <div class="contribution d-print-none">
          <a href="https://github.com/BretJohnson/hot-preview/blob/main/src/HotPreview.SharedModel/UIComponentBase.cs/#L7" class="edit-link">Edit this page</a>
        </div>


      </div>

      <div class="affix">
        <nav id="affix"></nav>
      </div>
    </main>

    <div class="container-xxl search-results" id="search-results"></div>

    <footer class="border-top text-secondary">
      <div class="container-xxl">
        <div class="flex-fill">
          <span>Made with <a href="https://dotnet.github.io/docfx">docfx</a></span>
        </div>
      </div>
    </footer>
  </body>
</html>
