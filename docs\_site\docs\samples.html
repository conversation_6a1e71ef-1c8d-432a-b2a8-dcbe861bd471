<!DOCTYPE html>
<html>
  <head>
    <meta charset="utf-8">
      <title>Sample Applications | Hot Preview Documentation </title>
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <meta name="title" content="Sample Applications | Hot Preview Documentation ">
      
      
      <link rel="icon" href="../favicon.ico">
      <link rel="stylesheet" href="../public/docfx.min.css">
      <link rel="stylesheet" href="../public/main.css">
      <meta name="docfx:navrel" content="../toc.html">
      <meta name="docfx:tocrel" content="toc.html">
      
      <meta name="docfx:rel" content="../">
      
      
      <meta name="docfx:docurl" content="https://github.com/BretJohnson/hot-preview/blob/main/docs/docs/samples.md/#L1">
      <meta name="loc:inThisArticle" content="In this article">
      <meta name="loc:searchResultsCount" content="{count} results for &quot;{query}&quot;">
      <meta name="loc:searchNoResults" content="No results for &quot;{query}&quot;">
      <meta name="loc:tocFilter" content="Filter by title">
      <meta name="loc:nextArticle" content="Next">
      <meta name="loc:prevArticle" content="Previous">
      <meta name="loc:themeLight" content="Light">
      <meta name="loc:themeDark" content="Dark">
      <meta name="loc:themeAuto" content="Auto">
      <meta name="loc:changeTheme" content="Change theme">
      <meta name="loc:copy" content="Copy">
      <meta name="loc:downloadPdf" content="Download PDF">

      <script type="module" src="./../public/docfx.min.js"></script>

      <script>
        const theme = localStorage.getItem('theme') || 'auto'
        document.documentElement.setAttribute('data-bs-theme', theme === 'auto' ? (window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light') : theme)
      </script>

  </head>

  <body class="tex2jax_ignore" data-layout="" data-yaml-mime="">
    <header class="bg-body border-bottom">
      <nav id="autocollapse" class="navbar navbar-expand-md" role="navigation">
        <div class="container-xxl flex-nowrap">
          <a class="navbar-brand" href="../index.html">
            <img id="logo" class="svg" src="../logo.svg" alt="Hot Preview">
            Hot Preview
          </a>
          <button class="btn btn-lg d-md-none border-0" type="button" data-bs-toggle="collapse" data-bs-target="#navpanel" aria-controls="navpanel" aria-expanded="false" aria-label="Toggle navigation">
            <i class="bi bi-three-dots"></i>
          </button>
          <div class="collapse navbar-collapse" id="navpanel">
            <div id="navbar">
              <form class="search" role="search" id="search">
                <i class="bi bi-search"></i>
                <input class="form-control" id="search-query" type="search" disabled placeholder="Search" autocomplete="off" aria-label="Search">
              </form>
            </div>
          </div>
        </div>
      </nav>
    </header>

    <main class="container-xxl">
      <div class="toc-offcanvas">
        <div class="offcanvas-md offcanvas-start" tabindex="-1" id="tocOffcanvas" aria-labelledby="tocOffcanvasLabel">
          <div class="offcanvas-header">
            <h5 class="offcanvas-title" id="tocOffcanvasLabel">Table of Contents</h5>
            <button type="button" class="btn-close" data-bs-dismiss="offcanvas" data-bs-target="#tocOffcanvas" aria-label="Close"></button>
          </div>
          <div class="offcanvas-body">
            <nav class="toc" id="toc"></nav>
          </div>
        </div>
      </div>

      <div class="content">
        <div class="actionbar">
          <button class="btn btn-lg border-0 d-md-none" type="button" data-bs-toggle="offcanvas" data-bs-target="#tocOffcanvas" aria-controls="tocOffcanvas" aria-expanded="false" aria-label="Show table of contents">
            <i class="bi bi-list"></i>
          </button>

          <nav id="breadcrumb"></nav>
        </div>

        <article data-uid="">
<h1 id="sample-applications">Sample Applications</h1>

<p>HotPreview includes several sample applications that demonstrate different features and usage patterns. These samples serve as both learning resources and testing environments for the framework.</p>
<h2 id="available-samples">Available Samples</h2>
<h3 id="e-commerce-maui-application">E-commerce MAUI Application</h3>
<p><strong>Location:</strong> <code>samples/maui/EcommerceMAUI/</code><br>
<strong>Description:</strong> A complete e-commerce mobile application showcasing complex UI components and previews.</p>
<h4 id="features-demonstrated">Features Demonstrated</h4>
<ul>
<li><strong>Product browsing and search</strong></li>
<li><strong>Shopping cart functionality</strong></li>
<li><strong>User authentication flows</strong></li>
<li><strong>Payment and checkout processes</strong></li>
<li><strong>Order tracking</strong></li>
<li><strong>Profile management</strong></li>
</ul>
<h4 id="preview-examples">Preview Examples</h4>
<p>The sample includes extensive preview definitions for various component states:</p>
<p><strong>Card Component Previews:</strong></p>
<pre><code class="lang-csharp">#if PREVIEWS
    [Preview(&quot;No Cards&quot;)]
    public static CardView NoCards() =&gt; new(PreviewData.GetPreviewCards(0));

    [Preview(&quot;Single Card&quot;)]
    public static CardView SingleCard() =&gt; new(PreviewData.GetPreviewCards(1));

    [Preview(&quot;Two Cards&quot;)]
    public static CardView TwoCards() =&gt; new(PreviewData.GetPreviewCards(2));

    [Preview(&quot;Six Cards&quot;)]
    public static CardView SixCards() =&gt; new(PreviewData.GetPreviewCards(6));
#endif
</code></pre>
<p><strong>Cart Component States:</strong></p>
<pre><code class="lang-csharp">#if PREVIEWS
    [Preview(&quot;Empty Cart&quot;)]
    public static CartView EmptyCart() =&gt; new(PreviewData.GetEmptyCart());

    [Preview(&quot;Single Item Cart&quot;)]
    public static CartView SingleItemCart() =&gt; new(PreviewData.GetCart(1));

    [Preview(&quot;Medium Cart&quot;)]
    public static CartView MediumCart() =&gt; new(PreviewData.GetCart(3));

    [Preview(&quot;Large Cart&quot;)]
    public static CartView LargeCart() =&gt; new(PreviewData.GetCart(10));
#endif
</code></pre>
<h4 id="visual-regression-testing">Visual Regression Testing</h4>
<p>The sample includes automated visual testing with snapshot comparisons:</p>
<p><strong>Generated Snapshots:</strong></p>
<ul>
<li><code>EcommerceMAUI.Views.AddNewCardView.png</code></li>
<li><code>EcommerceMAUI.Views.CartView-SingleItemCart.png</code></li>
<li><code>EcommerceMAUI.Views.ProductDetailsView.png</code></li>
<li><code>EcommerceMAUI.Views.HomePageView.png</code></li>
</ul>
<h4 id="running-the-sample">Running the Sample</h4>
<pre><code class="lang-bash">cd samples/maui/EcommerceMAUI
dotnet build
dotnet run --project EcommerceMAUI.csproj
</code></pre>
<h3 id="default-template-with-content">Default Template with Content</h3>
<p><strong>Location:</strong> <code>samples/maui/DefaultTemplateWithContent/</code><br>
<strong>Description:</strong> A project management application demonstrating data-driven UI components.</p>
<h4 id="features-demonstrated-1">Features Demonstrated</h4>
<ul>
<li><strong>Project and task management</strong></li>
<li><strong>Category-based organization</strong></li>
<li><strong>Data visualization with charts</strong></li>
<li><strong>CRUD operations</strong></li>
<li><strong>Tag-based filtering</strong></li>
</ul>
<h4 id="components-included">Components Included</h4>
<ul>
<li><code>ProjectCardView</code> - Project summary cards</li>
<li><code>TaskView</code> - Individual task components</li>
<li><code>CategoryChart</code> - Data visualization</li>
<li><code>TagView</code> - Tag display components</li>
<li><code>AddButton</code> - Action components</li>
</ul>
<h4 id="preview-data-management">Preview Data Management</h4>
<p>The sample shows how to create comprehensive preview data:</p>
<pre><code class="lang-csharp">public static class PreviewData
{
    public static List&lt;Project&gt; GetPreviewProjects(int count)
    {
        return MockData.Projects.Take(count).ToList();
    }

    public static List&lt;ProjectTask&gt; GetPreviewTasks(int count)
    {
        return MockData.Tasks.Take(count).ToList();
    }

    public static CategoryChartData GetPreviewChartData()
    {
        return new CategoryChartData
        {
            Categories = MockData.Categories.Take(5).ToList(),
            Data = GenerateRandomData(5)
        };
    }
}
</code></pre>
<h2 id="common-patterns">Common Patterns</h2>
<h3 id="preview-data-organization">Preview Data Organization</h3>
<p>Both samples demonstrate effective preview data management:</p>
<p><strong>Centralized Data Service:</strong></p>
<pre><code class="lang-csharp">public static class PreviewData
{
    // Static data for consistent previews
    public static readonly Product FeaturedProduct = new()
    {
        Name = &quot;Premium Headphones&quot;,
        Price = 299.99m,
        Description = &quot;High-quality wireless headphones&quot;,
        IsAvailable = true
    };

    // Dynamic data generation
    public static List&lt;Product&gt; GetProducts(int count, bool includeFeatured = false)
    {
        var products = MockData.Products.Take(count).ToList();
        if (includeFeatured)
            products.Insert(0, FeaturedProduct);
        return products;
    }
}
</code></pre>
<h3 id="state-variation-patterns">State Variation Patterns</h3>
<p><strong>Loading and Error States:</strong></p>
<pre><code class="lang-csharp">#if PREVIEWS
    [Preview(&quot;Normal State&quot;)]
    public static ProductView Normal() =&gt; new(PreviewData.FeaturedProduct);

    [Preview(&quot;Loading State&quot;)]
    public static ProductView Loading() =&gt; new(isLoading: true);

    [Preview(&quot;Error State&quot;)]  
    public static ProductView Error() =&gt; new(hasError: true, errorMessage: &quot;Failed to load product&quot;);

    [Preview(&quot;Empty State&quot;)]
    public static ProductView Empty() =&gt; new(isEmpty: true);
#endif
</code></pre>
<h3 id="hierarchical-organization">Hierarchical Organization</h3>
<p><strong>Grouped Previews:</strong></p>
<pre><code class="lang-csharp">#if PREVIEWS
    // Authentication flows
    [Preview(&quot;Auth/Login Form&quot;)]
    public static LoginView LoginForm() =&gt; new();

    [Preview(&quot;Auth/Register Form&quot;)]
    public static RegisterView RegisterForm() =&gt; new();

    [Preview(&quot;Auth/Forgot Password&quot;)]
    public static ForgotPasswordView ForgotPassword() =&gt; new();

    // Shopping features
    [Preview(&quot;Shop/Product List&quot;)]
    public static ProductListView ProductList() =&gt; new(PreviewData.GetProducts(10));

    [Preview(&quot;Shop/Product Details&quot;)]
    public static ProductDetailsView ProductDetails() =&gt; new(PreviewData.FeaturedProduct);
#endif
</code></pre>
<h2 id="testing-integration">Testing Integration</h2>
<h3 id="visual-regression-testing-1">Visual Regression Testing</h3>
<p>Both samples include comprehensive visual testing:</p>
<p><strong>Test Structure:</strong></p>
<pre><code>samples/maui/EcommerceMAUI/
├── snapshots/           # Reference images
├── test-results/        # Test output
└── VisualTests.cs       # Test definitions
</code></pre>
<p><strong>Example Test:</strong></p>
<pre><code class="lang-csharp">[Fact]
public async Task CartView_SingleItem_MatchesSnapshot()
{
    var component = CartView.SingleItemCart();
    var screenshot = await CaptureScreenshot(component);
    
    await VisualRegressionTester.AssertMatchesSnapshot(
        screenshot, 
        &quot;EcommerceMAUI.Views.CartView-SingleItem.png&quot;
    );
}
</code></pre>
<h3 id="unit-testing">Unit Testing</h3>
<p>Integration with xUnit for component testing:</p>
<pre><code class="lang-csharp">[Fact]
public void ProductCard_WithValidProduct_DisplaysCorrectly()
{
    // Arrange
    var product = PreviewData.FeaturedProduct;
    
    // Act
    var card = new ProductCard(product);
    
    // Assert
    Assert.Equal(product.Name, card.ProductName.Text);
    Assert.Equal(product.Price.ToString(&quot;C&quot;), card.Price.Text);
}
</code></pre>
<h2 id="development-workflow">Development Workflow</h2>
<h3 id="iterative-development">Iterative Development</h3>
<p>The samples demonstrate an efficient development workflow:</p>
<ol>
<li><strong>Create Component:</strong> Build basic UI component</li>
<li><strong>Add Preview:</strong> Create initial <code>[Preview]</code> method</li>
<li><strong>Test States:</strong> Add previews for different states</li>
<li><strong>Refine UI:</strong> Use DevTools to iterate quickly</li>
<li><strong>Add Tests:</strong> Create visual regression tests</li>
<li><strong>Document:</strong> Update preview names and organization</li>
</ol>
<h3 id="best-practices-from-samples">Best Practices from Samples</h3>
<p><strong>Naming Conventions:</strong></p>
<ul>
<li>Use descriptive, hierarchical names</li>
<li>Group related previews with &quot;/&quot; delimiters</li>
<li>Include state information in names</li>
</ul>
<p><strong>Data Management:</strong></p>
<ul>
<li>Centralize preview data in dedicated classes</li>
<li>Use realistic data that represents actual usage</li>
<li>Include edge cases and boundary conditions</li>
</ul>
<p><strong>Component Design:</strong></p>
<ul>
<li>Design components to be preview-friendly</li>
<li>Support parameterless constructors where possible</li>
<li>Use dependency injection for complex dependencies</li>
</ul>
<h2 id="running-all-samples">Running All Samples</h2>
<h3 id="build-all-samples">Build All Samples</h3>
<pre><code class="lang-bash">dotnet build samples/HotPreview-Samples.slnf
</code></pre>
<h3 id="run-individual-samples">Run Individual Samples</h3>
<pre><code class="lang-bash"># E-commerce sample
cd samples/maui/EcommerceMAUI
dotnet run

# Project management sample  
cd samples/maui/DefaultTemplateWithContent
dotnet run
</code></pre>
<h3 id="visual-testing">Visual Testing</h3>
<pre><code class="lang-bash"># Run visual regression tests
dotnet test samples/maui/EcommerceMAUI --logger &quot;console;verbosity=detailed&quot;
</code></pre>
<h2 id="learning-resources">Learning Resources</h2>
<h3 id="component-examples">Component Examples</h3>
<p>Study the sample components to learn:</p>
<ul>
<li><strong>Effective preview design patterns</strong></li>
<li><strong>State management for previews</strong></li>
<li><strong>Data binding in preview contexts</strong></li>
<li><strong>Platform-specific adaptations</strong></li>
</ul>
<h3 id="preview-strategies">Preview Strategies</h3>
<p>The samples showcase various preview strategies:</p>
<ul>
<li><strong>Minimal previews</strong> for simple components</li>
<li><strong>Comprehensive state coverage</strong> for complex components</li>
<li><strong>Data-driven previews</strong> for components with varying content</li>
<li><strong>Interactive previews</strong> with command support</li>
</ul>
<h3 id="testing-approaches">Testing Approaches</h3>
<p>Learn from the sample testing strategies:</p>
<ul>
<li><strong>Visual regression testing</strong> for UI consistency</li>
<li><strong>Unit testing</strong> for component logic</li>
<li><strong>Integration testing</strong> for component interactions</li>
<li><strong>Cross-platform testing</strong> for platform consistency</li>
</ul>

</article>

        <div class="contribution d-print-none">
          <a href="https://github.com/BretJohnson/hot-preview/blob/main/docs/docs/samples.md/#L1" class="edit-link">Edit this page</a>
        </div>

        <div class="next-article d-print-none border-top" id="nextArticle"></div>

      </div>

      <div class="affix">
        <nav id="affix"></nav>
      </div>
    </main>

    <div class="container-xxl search-results" id="search-results"></div>

    <footer class="border-top text-secondary">
      <div class="container-xxl">
        <div class="flex-fill">
          <span>Made with <a href="https://dotnet.github.io/docfx">docfx</a></span>
        </div>
      </div>
    </footer>
  </body>
</html>
