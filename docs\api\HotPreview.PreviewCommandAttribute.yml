### YamlMime:ManagedReference
items:
- uid: HotPreview.PreviewCommandAttribute
  commentId: T:HotPreview.PreviewCommandAttribute
  id: PreviewCommandAttribute
  parent: HotPreview
  children:
  - HotPreview.PreviewCommandAttribute.#ctor(System.String)
  - HotPreview.PreviewCommandAttribute.DisplayName
  langs:
  - csharp
  - vb
  name: PreviewCommandAttribute
  nameWithType: PreviewCommandAttribute
  fullName: HotPreview.PreviewCommandAttribute
  type: Class
  source:
    remote:
      path: src/HotPreview/PreviewCommandAttribute.cs
      branch: main
      repo: https://github.com/BretJohnson/hot-preview.git
    id: PreviewCommandAttribute
    path: ../src/HotPreview/PreviewCommandAttribute.cs
    startLine: 14
  assemblies:
  - HotPreview
  namespace: HotPreview
  summary: Designates this method as a command that can be executed from the DevTools UI or as an MCP tool command.
  remarks: >-
    <p>Commands typically update global state, changing the way that subsequent previews appear. For instance,

    commands could update the UI language for the app or switch the theme between light and dark. Commands normally

    don't update UI themselves (but they can - nothing prevents this).</p>

    <p>Commands for now should have no parameters and return void, though likely we'll add parameter and return

    support in the future once we've figured out the desired behavior.</p>
  example: []
  syntax:
    content: >-
      [AttributeUsage(AttributeTargets.Method)]

      public sealed class PreviewCommandAttribute : Attribute
    content.vb: >-
      <AttributeUsage(AttributeTargets.Method)>

      Public NotInheritable Class PreviewCommandAttribute Inherits Attribute
  inheritance:
  - System.Object
  - System.Attribute
  inheritedMembers:
  - System.Attribute.Equals(System.Object)
  - System.Attribute.GetCustomAttribute(System.Reflection.Assembly,System.Type)
  - System.Attribute.GetCustomAttribute(System.Reflection.Assembly,System.Type,System.Boolean)
  - System.Attribute.GetCustomAttribute(System.Reflection.MemberInfo,System.Type)
  - System.Attribute.GetCustomAttribute(System.Reflection.MemberInfo,System.Type,System.Boolean)
  - System.Attribute.GetCustomAttribute(System.Reflection.Module,System.Type)
  - System.Attribute.GetCustomAttribute(System.Reflection.Module,System.Type,System.Boolean)
  - System.Attribute.GetCustomAttribute(System.Reflection.ParameterInfo,System.Type)
  - System.Attribute.GetCustomAttribute(System.Reflection.ParameterInfo,System.Type,System.Boolean)
  - System.Attribute.GetCustomAttributes(System.Reflection.Assembly)
  - System.Attribute.GetCustomAttributes(System.Reflection.Assembly,System.Boolean)
  - System.Attribute.GetCustomAttributes(System.Reflection.Assembly,System.Type)
  - System.Attribute.GetCustomAttributes(System.Reflection.Assembly,System.Type,System.Boolean)
  - System.Attribute.GetCustomAttributes(System.Reflection.MemberInfo)
  - System.Attribute.GetCustomAttributes(System.Reflection.MemberInfo,System.Boolean)
  - System.Attribute.GetCustomAttributes(System.Reflection.MemberInfo,System.Type)
  - System.Attribute.GetCustomAttributes(System.Reflection.MemberInfo,System.Type,System.Boolean)
  - System.Attribute.GetCustomAttributes(System.Reflection.Module)
  - System.Attribute.GetCustomAttributes(System.Reflection.Module,System.Boolean)
  - System.Attribute.GetCustomAttributes(System.Reflection.Module,System.Type)
  - System.Attribute.GetCustomAttributes(System.Reflection.Module,System.Type,System.Boolean)
  - System.Attribute.GetCustomAttributes(System.Reflection.ParameterInfo)
  - System.Attribute.GetCustomAttributes(System.Reflection.ParameterInfo,System.Boolean)
  - System.Attribute.GetCustomAttributes(System.Reflection.ParameterInfo,System.Type)
  - System.Attribute.GetCustomAttributes(System.Reflection.ParameterInfo,System.Type,System.Boolean)
  - System.Attribute.GetHashCode
  - System.Attribute.IsDefaultAttribute
  - System.Attribute.IsDefined(System.Reflection.Assembly,System.Type)
  - System.Attribute.IsDefined(System.Reflection.Assembly,System.Type,System.Boolean)
  - System.Attribute.IsDefined(System.Reflection.MemberInfo,System.Type)
  - System.Attribute.IsDefined(System.Reflection.MemberInfo,System.Type,System.Boolean)
  - System.Attribute.IsDefined(System.Reflection.Module,System.Type)
  - System.Attribute.IsDefined(System.Reflection.Module,System.Type,System.Boolean)
  - System.Attribute.IsDefined(System.Reflection.ParameterInfo,System.Type)
  - System.Attribute.IsDefined(System.Reflection.ParameterInfo,System.Type,System.Boolean)
  - System.Attribute.Match(System.Object)
  - System.Attribute.TypeId
  - System.Object.Equals(System.Object,System.Object)
  - System.Object.GetType
  - System.Object.ReferenceEquals(System.Object,System.Object)
  - System.Object.ToString
  attributes:
  - type: System.AttributeUsageAttribute
    ctor: System.AttributeUsageAttribute.#ctor(System.AttributeTargets)
    arguments:
    - type: System.AttributeTargets
      value: 64
- uid: HotPreview.PreviewCommandAttribute.#ctor(System.String)
  commentId: M:HotPreview.PreviewCommandAttribute.#ctor(System.String)
  id: '#ctor(System.String)'
  parent: HotPreview.PreviewCommandAttribute
  langs:
  - csharp
  - vb
  name: PreviewCommandAttribute(string?)
  nameWithType: PreviewCommandAttribute.PreviewCommandAttribute(string?)
  fullName: HotPreview.PreviewCommandAttribute.PreviewCommandAttribute(string?)
  type: Constructor
  source:
    remote:
      path: src/HotPreview/PreviewCommandAttribute.cs
      branch: main
      repo: https://github.com/BretJohnson/hot-preview.git
    id: .ctor
    path: ../src/HotPreview/PreviewCommandAttribute.cs
    startLine: 14
  assemblies:
  - HotPreview
  namespace: HotPreview
  summary: Designates this method as a command that can be executed from the DevTools UI or as an MCP tool command.
  remarks: >-
    <p>Commands typically update global state, changing the way that subsequent previews appear. For instance,

    commands could update the UI language for the app or switch the theme between light and dark. Commands normally

    don't update UI themselves (but they can - nothing prevents this).</p>

    <p>Commands for now should have no parameters and return void, though likely we'll add parameter and return

    support in the future once we've figured out the desired behavior.</p>
  example: []
  syntax:
    content: public PreviewCommandAttribute(string? displayName = null)
    parameters:
    - id: displayName
      type: System.String
      description: >-
        Optional display name override for the command, determining how it appears in navigation
            UI. If not specified, the name of the method is used.
    content.vb: Public Sub New(displayName As String = Nothing)
  overload: HotPreview.PreviewCommandAttribute.#ctor*
  nameWithType.vb: PreviewCommandAttribute.New(String)
  fullName.vb: HotPreview.PreviewCommandAttribute.New(String)
  name.vb: New(String)
- uid: HotPreview.PreviewCommandAttribute.DisplayName
  commentId: P:HotPreview.PreviewCommandAttribute.DisplayName
  id: DisplayName
  parent: HotPreview.PreviewCommandAttribute
  langs:
  - csharp
  - vb
  name: DisplayName
  nameWithType: PreviewCommandAttribute.DisplayName
  fullName: HotPreview.PreviewCommandAttribute.DisplayName
  type: Property
  source:
    remote:
      path: src/HotPreview/PreviewCommandAttribute.cs
      branch: main
      repo: https://github.com/BretJohnson/hot-preview.git
    id: DisplayName
    path: ../src/HotPreview/PreviewCommandAttribute.cs
    startLine: 21
  assemblies:
  - HotPreview
  namespace: HotPreview
  summary: >-
    Optional display name override for the command, determining how it appears in navigation UI.

    If not specified, the name of the method is used.
  example: []
  syntax:
    content: public string? DisplayName { get; }
    parameters: []
    return:
      type: System.String
    content.vb: Public ReadOnly Property DisplayName As String
  overload: HotPreview.PreviewCommandAttribute.DisplayName*
references:
- uid: HotPreview
  commentId: N:HotPreview
  href: HotPreview.html
  name: HotPreview
  nameWithType: HotPreview
  fullName: HotPreview
- uid: System.Object
  commentId: T:System.Object
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object
  name: object
  nameWithType: object
  fullName: object
  nameWithType.vb: Object
  fullName.vb: Object
  name.vb: Object
- uid: System.Attribute
  commentId: T:System.Attribute
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.attribute
  name: Attribute
  nameWithType: Attribute
  fullName: System.Attribute
- uid: System.Attribute.Equals(System.Object)
  commentId: M:System.Attribute.Equals(System.Object)
  parent: System.Attribute
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.attribute.equals
  name: Equals(object)
  nameWithType: Attribute.Equals(object)
  fullName: System.Attribute.Equals(object)
  nameWithType.vb: Attribute.Equals(Object)
  fullName.vb: System.Attribute.Equals(Object)
  name.vb: Equals(Object)
  spec.csharp:
  - uid: System.Attribute.Equals(System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.attribute.equals
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Attribute.Equals(System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.attribute.equals
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Attribute.GetCustomAttribute(System.Reflection.Assembly,System.Type)
  commentId: M:System.Attribute.GetCustomAttribute(System.Reflection.Assembly,System.Type)
  parent: System.Attribute
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.attribute.getcustomattribute#system-attribute-getcustomattribute(system-reflection-assembly-system-type)
  name: GetCustomAttribute(Assembly, Type)
  nameWithType: Attribute.GetCustomAttribute(Assembly, Type)
  fullName: System.Attribute.GetCustomAttribute(System.Reflection.Assembly, System.Type)
  spec.csharp:
  - uid: System.Attribute.GetCustomAttribute(System.Reflection.Assembly,System.Type)
    name: GetCustomAttribute
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.attribute.getcustomattribute#system-attribute-getcustomattribute(system-reflection-assembly-system-type)
  - name: (
  - uid: System.Reflection.Assembly
    name: Assembly
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.reflection.assembly
  - name: ','
  - name: " "
  - uid: System.Type
    name: Type
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.type
  - name: )
  spec.vb:
  - uid: System.Attribute.GetCustomAttribute(System.Reflection.Assembly,System.Type)
    name: GetCustomAttribute
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.attribute.getcustomattribute#system-attribute-getcustomattribute(system-reflection-assembly-system-type)
  - name: (
  - uid: System.Reflection.Assembly
    name: Assembly
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.reflection.assembly
  - name: ','
  - name: " "
  - uid: System.Type
    name: Type
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.type
  - name: )
- uid: System.Attribute.GetCustomAttribute(System.Reflection.Assembly,System.Type,System.Boolean)
  commentId: M:System.Attribute.GetCustomAttribute(System.Reflection.Assembly,System.Type,System.Boolean)
  parent: System.Attribute
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.attribute.getcustomattribute#system-attribute-getcustomattribute(system-reflection-assembly-system-type-system-boolean)
  name: GetCustomAttribute(Assembly, Type, bool)
  nameWithType: Attribute.GetCustomAttribute(Assembly, Type, bool)
  fullName: System.Attribute.GetCustomAttribute(System.Reflection.Assembly, System.Type, bool)
  nameWithType.vb: Attribute.GetCustomAttribute(Assembly, Type, Boolean)
  fullName.vb: System.Attribute.GetCustomAttribute(System.Reflection.Assembly, System.Type, Boolean)
  name.vb: GetCustomAttribute(Assembly, Type, Boolean)
  spec.csharp:
  - uid: System.Attribute.GetCustomAttribute(System.Reflection.Assembly,System.Type,System.Boolean)
    name: GetCustomAttribute
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.attribute.getcustomattribute#system-attribute-getcustomattribute(system-reflection-assembly-system-type-system-boolean)
  - name: (
  - uid: System.Reflection.Assembly
    name: Assembly
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.reflection.assembly
  - name: ','
  - name: " "
  - uid: System.Type
    name: Type
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.type
  - name: ','
  - name: " "
  - uid: System.Boolean
    name: bool
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.boolean
  - name: )
  spec.vb:
  - uid: System.Attribute.GetCustomAttribute(System.Reflection.Assembly,System.Type,System.Boolean)
    name: GetCustomAttribute
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.attribute.getcustomattribute#system-attribute-getcustomattribute(system-reflection-assembly-system-type-system-boolean)
  - name: (
  - uid: System.Reflection.Assembly
    name: Assembly
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.reflection.assembly
  - name: ','
  - name: " "
  - uid: System.Type
    name: Type
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.type
  - name: ','
  - name: " "
  - uid: System.Boolean
    name: Boolean
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.boolean
  - name: )
- uid: System.Attribute.GetCustomAttribute(System.Reflection.MemberInfo,System.Type)
  commentId: M:System.Attribute.GetCustomAttribute(System.Reflection.MemberInfo,System.Type)
  parent: System.Attribute
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.attribute.getcustomattribute#system-attribute-getcustomattribute(system-reflection-memberinfo-system-type)
  name: GetCustomAttribute(MemberInfo, Type)
  nameWithType: Attribute.GetCustomAttribute(MemberInfo, Type)
  fullName: System.Attribute.GetCustomAttribute(System.Reflection.MemberInfo, System.Type)
  spec.csharp:
  - uid: System.Attribute.GetCustomAttribute(System.Reflection.MemberInfo,System.Type)
    name: GetCustomAttribute
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.attribute.getcustomattribute#system-attribute-getcustomattribute(system-reflection-memberinfo-system-type)
  - name: (
  - uid: System.Reflection.MemberInfo
    name: MemberInfo
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.reflection.memberinfo
  - name: ','
  - name: " "
  - uid: System.Type
    name: Type
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.type
  - name: )
  spec.vb:
  - uid: System.Attribute.GetCustomAttribute(System.Reflection.MemberInfo,System.Type)
    name: GetCustomAttribute
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.attribute.getcustomattribute#system-attribute-getcustomattribute(system-reflection-memberinfo-system-type)
  - name: (
  - uid: System.Reflection.MemberInfo
    name: MemberInfo
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.reflection.memberinfo
  - name: ','
  - name: " "
  - uid: System.Type
    name: Type
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.type
  - name: )
- uid: System.Attribute.GetCustomAttribute(System.Reflection.MemberInfo,System.Type,System.Boolean)
  commentId: M:System.Attribute.GetCustomAttribute(System.Reflection.MemberInfo,System.Type,System.Boolean)
  parent: System.Attribute
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.attribute.getcustomattribute#system-attribute-getcustomattribute(system-reflection-memberinfo-system-type-system-boolean)
  name: GetCustomAttribute(MemberInfo, Type, bool)
  nameWithType: Attribute.GetCustomAttribute(MemberInfo, Type, bool)
  fullName: System.Attribute.GetCustomAttribute(System.Reflection.MemberInfo, System.Type, bool)
  nameWithType.vb: Attribute.GetCustomAttribute(MemberInfo, Type, Boolean)
  fullName.vb: System.Attribute.GetCustomAttribute(System.Reflection.MemberInfo, System.Type, Boolean)
  name.vb: GetCustomAttribute(MemberInfo, Type, Boolean)
  spec.csharp:
  - uid: System.Attribute.GetCustomAttribute(System.Reflection.MemberInfo,System.Type,System.Boolean)
    name: GetCustomAttribute
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.attribute.getcustomattribute#system-attribute-getcustomattribute(system-reflection-memberinfo-system-type-system-boolean)
  - name: (
  - uid: System.Reflection.MemberInfo
    name: MemberInfo
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.reflection.memberinfo
  - name: ','
  - name: " "
  - uid: System.Type
    name: Type
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.type
  - name: ','
  - name: " "
  - uid: System.Boolean
    name: bool
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.boolean
  - name: )
  spec.vb:
  - uid: System.Attribute.GetCustomAttribute(System.Reflection.MemberInfo,System.Type,System.Boolean)
    name: GetCustomAttribute
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.attribute.getcustomattribute#system-attribute-getcustomattribute(system-reflection-memberinfo-system-type-system-boolean)
  - name: (
  - uid: System.Reflection.MemberInfo
    name: MemberInfo
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.reflection.memberinfo
  - name: ','
  - name: " "
  - uid: System.Type
    name: Type
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.type
  - name: ','
  - name: " "
  - uid: System.Boolean
    name: Boolean
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.boolean
  - name: )
- uid: System.Attribute.GetCustomAttribute(System.Reflection.Module,System.Type)
  commentId: M:System.Attribute.GetCustomAttribute(System.Reflection.Module,System.Type)
  parent: System.Attribute
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.attribute.getcustomattribute#system-attribute-getcustomattribute(system-reflection-module-system-type)
  name: GetCustomAttribute(Module, Type)
  nameWithType: Attribute.GetCustomAttribute(Module, Type)
  fullName: System.Attribute.GetCustomAttribute(System.Reflection.Module, System.Type)
  spec.csharp:
  - uid: System.Attribute.GetCustomAttribute(System.Reflection.Module,System.Type)
    name: GetCustomAttribute
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.attribute.getcustomattribute#system-attribute-getcustomattribute(system-reflection-module-system-type)
  - name: (
  - uid: System.Reflection.Module
    name: Module
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.reflection.module
  - name: ','
  - name: " "
  - uid: System.Type
    name: Type
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.type
  - name: )
  spec.vb:
  - uid: System.Attribute.GetCustomAttribute(System.Reflection.Module,System.Type)
    name: GetCustomAttribute
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.attribute.getcustomattribute#system-attribute-getcustomattribute(system-reflection-module-system-type)
  - name: (
  - uid: System.Reflection.Module
    name: Module
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.reflection.module
  - name: ','
  - name: " "
  - uid: System.Type
    name: Type
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.type
  - name: )
- uid: System.Attribute.GetCustomAttribute(System.Reflection.Module,System.Type,System.Boolean)
  commentId: M:System.Attribute.GetCustomAttribute(System.Reflection.Module,System.Type,System.Boolean)
  parent: System.Attribute
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.attribute.getcustomattribute#system-attribute-getcustomattribute(system-reflection-module-system-type-system-boolean)
  name: GetCustomAttribute(Module, Type, bool)
  nameWithType: Attribute.GetCustomAttribute(Module, Type, bool)
  fullName: System.Attribute.GetCustomAttribute(System.Reflection.Module, System.Type, bool)
  nameWithType.vb: Attribute.GetCustomAttribute(Module, Type, Boolean)
  fullName.vb: System.Attribute.GetCustomAttribute(System.Reflection.Module, System.Type, Boolean)
  name.vb: GetCustomAttribute(Module, Type, Boolean)
  spec.csharp:
  - uid: System.Attribute.GetCustomAttribute(System.Reflection.Module,System.Type,System.Boolean)
    name: GetCustomAttribute
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.attribute.getcustomattribute#system-attribute-getcustomattribute(system-reflection-module-system-type-system-boolean)
  - name: (
  - uid: System.Reflection.Module
    name: Module
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.reflection.module
  - name: ','
  - name: " "
  - uid: System.Type
    name: Type
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.type
  - name: ','
  - name: " "
  - uid: System.Boolean
    name: bool
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.boolean
  - name: )
  spec.vb:
  - uid: System.Attribute.GetCustomAttribute(System.Reflection.Module,System.Type,System.Boolean)
    name: GetCustomAttribute
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.attribute.getcustomattribute#system-attribute-getcustomattribute(system-reflection-module-system-type-system-boolean)
  - name: (
  - uid: System.Reflection.Module
    name: Module
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.reflection.module
  - name: ','
  - name: " "
  - uid: System.Type
    name: Type
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.type
  - name: ','
  - name: " "
  - uid: System.Boolean
    name: Boolean
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.boolean
  - name: )
- uid: System.Attribute.GetCustomAttribute(System.Reflection.ParameterInfo,System.Type)
  commentId: M:System.Attribute.GetCustomAttribute(System.Reflection.ParameterInfo,System.Type)
  parent: System.Attribute
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.attribute.getcustomattribute#system-attribute-getcustomattribute(system-reflection-parameterinfo-system-type)
  name: GetCustomAttribute(ParameterInfo, Type)
  nameWithType: Attribute.GetCustomAttribute(ParameterInfo, Type)
  fullName: System.Attribute.GetCustomAttribute(System.Reflection.ParameterInfo, System.Type)
  spec.csharp:
  - uid: System.Attribute.GetCustomAttribute(System.Reflection.ParameterInfo,System.Type)
    name: GetCustomAttribute
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.attribute.getcustomattribute#system-attribute-getcustomattribute(system-reflection-parameterinfo-system-type)
  - name: (
  - uid: System.Reflection.ParameterInfo
    name: ParameterInfo
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.reflection.parameterinfo
  - name: ','
  - name: " "
  - uid: System.Type
    name: Type
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.type
  - name: )
  spec.vb:
  - uid: System.Attribute.GetCustomAttribute(System.Reflection.ParameterInfo,System.Type)
    name: GetCustomAttribute
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.attribute.getcustomattribute#system-attribute-getcustomattribute(system-reflection-parameterinfo-system-type)
  - name: (
  - uid: System.Reflection.ParameterInfo
    name: ParameterInfo
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.reflection.parameterinfo
  - name: ','
  - name: " "
  - uid: System.Type
    name: Type
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.type
  - name: )
- uid: System.Attribute.GetCustomAttribute(System.Reflection.ParameterInfo,System.Type,System.Boolean)
  commentId: M:System.Attribute.GetCustomAttribute(System.Reflection.ParameterInfo,System.Type,System.Boolean)
  parent: System.Attribute
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.attribute.getcustomattribute#system-attribute-getcustomattribute(system-reflection-parameterinfo-system-type-system-boolean)
  name: GetCustomAttribute(ParameterInfo, Type, bool)
  nameWithType: Attribute.GetCustomAttribute(ParameterInfo, Type, bool)
  fullName: System.Attribute.GetCustomAttribute(System.Reflection.ParameterInfo, System.Type, bool)
  nameWithType.vb: Attribute.GetCustomAttribute(ParameterInfo, Type, Boolean)
  fullName.vb: System.Attribute.GetCustomAttribute(System.Reflection.ParameterInfo, System.Type, Boolean)
  name.vb: GetCustomAttribute(ParameterInfo, Type, Boolean)
  spec.csharp:
  - uid: System.Attribute.GetCustomAttribute(System.Reflection.ParameterInfo,System.Type,System.Boolean)
    name: GetCustomAttribute
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.attribute.getcustomattribute#system-attribute-getcustomattribute(system-reflection-parameterinfo-system-type-system-boolean)
  - name: (
  - uid: System.Reflection.ParameterInfo
    name: ParameterInfo
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.reflection.parameterinfo
  - name: ','
  - name: " "
  - uid: System.Type
    name: Type
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.type
  - name: ','
  - name: " "
  - uid: System.Boolean
    name: bool
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.boolean
  - name: )
  spec.vb:
  - uid: System.Attribute.GetCustomAttribute(System.Reflection.ParameterInfo,System.Type,System.Boolean)
    name: GetCustomAttribute
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.attribute.getcustomattribute#system-attribute-getcustomattribute(system-reflection-parameterinfo-system-type-system-boolean)
  - name: (
  - uid: System.Reflection.ParameterInfo
    name: ParameterInfo
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.reflection.parameterinfo
  - name: ','
  - name: " "
  - uid: System.Type
    name: Type
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.type
  - name: ','
  - name: " "
  - uid: System.Boolean
    name: Boolean
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.boolean
  - name: )
- uid: System.Attribute.GetCustomAttributes(System.Reflection.Assembly)
  commentId: M:System.Attribute.GetCustomAttributes(System.Reflection.Assembly)
  parent: System.Attribute
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.attribute.getcustomattributes#system-attribute-getcustomattributes(system-reflection-assembly)
  name: GetCustomAttributes(Assembly)
  nameWithType: Attribute.GetCustomAttributes(Assembly)
  fullName: System.Attribute.GetCustomAttributes(System.Reflection.Assembly)
  spec.csharp:
  - uid: System.Attribute.GetCustomAttributes(System.Reflection.Assembly)
    name: GetCustomAttributes
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.attribute.getcustomattributes#system-attribute-getcustomattributes(system-reflection-assembly)
  - name: (
  - uid: System.Reflection.Assembly
    name: Assembly
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.reflection.assembly
  - name: )
  spec.vb:
  - uid: System.Attribute.GetCustomAttributes(System.Reflection.Assembly)
    name: GetCustomAttributes
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.attribute.getcustomattributes#system-attribute-getcustomattributes(system-reflection-assembly)
  - name: (
  - uid: System.Reflection.Assembly
    name: Assembly
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.reflection.assembly
  - name: )
- uid: System.Attribute.GetCustomAttributes(System.Reflection.Assembly,System.Boolean)
  commentId: M:System.Attribute.GetCustomAttributes(System.Reflection.Assembly,System.Boolean)
  parent: System.Attribute
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.attribute.getcustomattributes#system-attribute-getcustomattributes(system-reflection-assembly-system-boolean)
  name: GetCustomAttributes(Assembly, bool)
  nameWithType: Attribute.GetCustomAttributes(Assembly, bool)
  fullName: System.Attribute.GetCustomAttributes(System.Reflection.Assembly, bool)
  nameWithType.vb: Attribute.GetCustomAttributes(Assembly, Boolean)
  fullName.vb: System.Attribute.GetCustomAttributes(System.Reflection.Assembly, Boolean)
  name.vb: GetCustomAttributes(Assembly, Boolean)
  spec.csharp:
  - uid: System.Attribute.GetCustomAttributes(System.Reflection.Assembly,System.Boolean)
    name: GetCustomAttributes
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.attribute.getcustomattributes#system-attribute-getcustomattributes(system-reflection-assembly-system-boolean)
  - name: (
  - uid: System.Reflection.Assembly
    name: Assembly
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.reflection.assembly
  - name: ','
  - name: " "
  - uid: System.Boolean
    name: bool
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.boolean
  - name: )
  spec.vb:
  - uid: System.Attribute.GetCustomAttributes(System.Reflection.Assembly,System.Boolean)
    name: GetCustomAttributes
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.attribute.getcustomattributes#system-attribute-getcustomattributes(system-reflection-assembly-system-boolean)
  - name: (
  - uid: System.Reflection.Assembly
    name: Assembly
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.reflection.assembly
  - name: ','
  - name: " "
  - uid: System.Boolean
    name: Boolean
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.boolean
  - name: )
- uid: System.Attribute.GetCustomAttributes(System.Reflection.Assembly,System.Type)
  commentId: M:System.Attribute.GetCustomAttributes(System.Reflection.Assembly,System.Type)
  parent: System.Attribute
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.attribute.getcustomattributes#system-attribute-getcustomattributes(system-reflection-assembly-system-type)
  name: GetCustomAttributes(Assembly, Type)
  nameWithType: Attribute.GetCustomAttributes(Assembly, Type)
  fullName: System.Attribute.GetCustomAttributes(System.Reflection.Assembly, System.Type)
  spec.csharp:
  - uid: System.Attribute.GetCustomAttributes(System.Reflection.Assembly,System.Type)
    name: GetCustomAttributes
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.attribute.getcustomattributes#system-attribute-getcustomattributes(system-reflection-assembly-system-type)
  - name: (
  - uid: System.Reflection.Assembly
    name: Assembly
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.reflection.assembly
  - name: ','
  - name: " "
  - uid: System.Type
    name: Type
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.type
  - name: )
  spec.vb:
  - uid: System.Attribute.GetCustomAttributes(System.Reflection.Assembly,System.Type)
    name: GetCustomAttributes
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.attribute.getcustomattributes#system-attribute-getcustomattributes(system-reflection-assembly-system-type)
  - name: (
  - uid: System.Reflection.Assembly
    name: Assembly
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.reflection.assembly
  - name: ','
  - name: " "
  - uid: System.Type
    name: Type
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.type
  - name: )
- uid: System.Attribute.GetCustomAttributes(System.Reflection.Assembly,System.Type,System.Boolean)
  commentId: M:System.Attribute.GetCustomAttributes(System.Reflection.Assembly,System.Type,System.Boolean)
  parent: System.Attribute
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.attribute.getcustomattributes#system-attribute-getcustomattributes(system-reflection-assembly-system-type-system-boolean)
  name: GetCustomAttributes(Assembly, Type, bool)
  nameWithType: Attribute.GetCustomAttributes(Assembly, Type, bool)
  fullName: System.Attribute.GetCustomAttributes(System.Reflection.Assembly, System.Type, bool)
  nameWithType.vb: Attribute.GetCustomAttributes(Assembly, Type, Boolean)
  fullName.vb: System.Attribute.GetCustomAttributes(System.Reflection.Assembly, System.Type, Boolean)
  name.vb: GetCustomAttributes(Assembly, Type, Boolean)
  spec.csharp:
  - uid: System.Attribute.GetCustomAttributes(System.Reflection.Assembly,System.Type,System.Boolean)
    name: GetCustomAttributes
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.attribute.getcustomattributes#system-attribute-getcustomattributes(system-reflection-assembly-system-type-system-boolean)
  - name: (
  - uid: System.Reflection.Assembly
    name: Assembly
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.reflection.assembly
  - name: ','
  - name: " "
  - uid: System.Type
    name: Type
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.type
  - name: ','
  - name: " "
  - uid: System.Boolean
    name: bool
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.boolean
  - name: )
  spec.vb:
  - uid: System.Attribute.GetCustomAttributes(System.Reflection.Assembly,System.Type,System.Boolean)
    name: GetCustomAttributes
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.attribute.getcustomattributes#system-attribute-getcustomattributes(system-reflection-assembly-system-type-system-boolean)
  - name: (
  - uid: System.Reflection.Assembly
    name: Assembly
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.reflection.assembly
  - name: ','
  - name: " "
  - uid: System.Type
    name: Type
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.type
  - name: ','
  - name: " "
  - uid: System.Boolean
    name: Boolean
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.boolean
  - name: )
- uid: System.Attribute.GetCustomAttributes(System.Reflection.MemberInfo)
  commentId: M:System.Attribute.GetCustomAttributes(System.Reflection.MemberInfo)
  parent: System.Attribute
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.attribute.getcustomattributes#system-attribute-getcustomattributes(system-reflection-memberinfo)
  name: GetCustomAttributes(MemberInfo)
  nameWithType: Attribute.GetCustomAttributes(MemberInfo)
  fullName: System.Attribute.GetCustomAttributes(System.Reflection.MemberInfo)
  spec.csharp:
  - uid: System.Attribute.GetCustomAttributes(System.Reflection.MemberInfo)
    name: GetCustomAttributes
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.attribute.getcustomattributes#system-attribute-getcustomattributes(system-reflection-memberinfo)
  - name: (
  - uid: System.Reflection.MemberInfo
    name: MemberInfo
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.reflection.memberinfo
  - name: )
  spec.vb:
  - uid: System.Attribute.GetCustomAttributes(System.Reflection.MemberInfo)
    name: GetCustomAttributes
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.attribute.getcustomattributes#system-attribute-getcustomattributes(system-reflection-memberinfo)
  - name: (
  - uid: System.Reflection.MemberInfo
    name: MemberInfo
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.reflection.memberinfo
  - name: )
- uid: System.Attribute.GetCustomAttributes(System.Reflection.MemberInfo,System.Boolean)
  commentId: M:System.Attribute.GetCustomAttributes(System.Reflection.MemberInfo,System.Boolean)
  parent: System.Attribute
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.attribute.getcustomattributes#system-attribute-getcustomattributes(system-reflection-memberinfo-system-boolean)
  name: GetCustomAttributes(MemberInfo, bool)
  nameWithType: Attribute.GetCustomAttributes(MemberInfo, bool)
  fullName: System.Attribute.GetCustomAttributes(System.Reflection.MemberInfo, bool)
  nameWithType.vb: Attribute.GetCustomAttributes(MemberInfo, Boolean)
  fullName.vb: System.Attribute.GetCustomAttributes(System.Reflection.MemberInfo, Boolean)
  name.vb: GetCustomAttributes(MemberInfo, Boolean)
  spec.csharp:
  - uid: System.Attribute.GetCustomAttributes(System.Reflection.MemberInfo,System.Boolean)
    name: GetCustomAttributes
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.attribute.getcustomattributes#system-attribute-getcustomattributes(system-reflection-memberinfo-system-boolean)
  - name: (
  - uid: System.Reflection.MemberInfo
    name: MemberInfo
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.reflection.memberinfo
  - name: ','
  - name: " "
  - uid: System.Boolean
    name: bool
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.boolean
  - name: )
  spec.vb:
  - uid: System.Attribute.GetCustomAttributes(System.Reflection.MemberInfo,System.Boolean)
    name: GetCustomAttributes
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.attribute.getcustomattributes#system-attribute-getcustomattributes(system-reflection-memberinfo-system-boolean)
  - name: (
  - uid: System.Reflection.MemberInfo
    name: MemberInfo
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.reflection.memberinfo
  - name: ','
  - name: " "
  - uid: System.Boolean
    name: Boolean
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.boolean
  - name: )
- uid: System.Attribute.GetCustomAttributes(System.Reflection.MemberInfo,System.Type)
  commentId: M:System.Attribute.GetCustomAttributes(System.Reflection.MemberInfo,System.Type)
  parent: System.Attribute
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.attribute.getcustomattributes#system-attribute-getcustomattributes(system-reflection-memberinfo-system-type)
  name: GetCustomAttributes(MemberInfo, Type)
  nameWithType: Attribute.GetCustomAttributes(MemberInfo, Type)
  fullName: System.Attribute.GetCustomAttributes(System.Reflection.MemberInfo, System.Type)
  spec.csharp:
  - uid: System.Attribute.GetCustomAttributes(System.Reflection.MemberInfo,System.Type)
    name: GetCustomAttributes
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.attribute.getcustomattributes#system-attribute-getcustomattributes(system-reflection-memberinfo-system-type)
  - name: (
  - uid: System.Reflection.MemberInfo
    name: MemberInfo
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.reflection.memberinfo
  - name: ','
  - name: " "
  - uid: System.Type
    name: Type
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.type
  - name: )
  spec.vb:
  - uid: System.Attribute.GetCustomAttributes(System.Reflection.MemberInfo,System.Type)
    name: GetCustomAttributes
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.attribute.getcustomattributes#system-attribute-getcustomattributes(system-reflection-memberinfo-system-type)
  - name: (
  - uid: System.Reflection.MemberInfo
    name: MemberInfo
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.reflection.memberinfo
  - name: ','
  - name: " "
  - uid: System.Type
    name: Type
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.type
  - name: )
- uid: System.Attribute.GetCustomAttributes(System.Reflection.MemberInfo,System.Type,System.Boolean)
  commentId: M:System.Attribute.GetCustomAttributes(System.Reflection.MemberInfo,System.Type,System.Boolean)
  parent: System.Attribute
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.attribute.getcustomattributes#system-attribute-getcustomattributes(system-reflection-memberinfo-system-type-system-boolean)
  name: GetCustomAttributes(MemberInfo, Type, bool)
  nameWithType: Attribute.GetCustomAttributes(MemberInfo, Type, bool)
  fullName: System.Attribute.GetCustomAttributes(System.Reflection.MemberInfo, System.Type, bool)
  nameWithType.vb: Attribute.GetCustomAttributes(MemberInfo, Type, Boolean)
  fullName.vb: System.Attribute.GetCustomAttributes(System.Reflection.MemberInfo, System.Type, Boolean)
  name.vb: GetCustomAttributes(MemberInfo, Type, Boolean)
  spec.csharp:
  - uid: System.Attribute.GetCustomAttributes(System.Reflection.MemberInfo,System.Type,System.Boolean)
    name: GetCustomAttributes
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.attribute.getcustomattributes#system-attribute-getcustomattributes(system-reflection-memberinfo-system-type-system-boolean)
  - name: (
  - uid: System.Reflection.MemberInfo
    name: MemberInfo
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.reflection.memberinfo
  - name: ','
  - name: " "
  - uid: System.Type
    name: Type
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.type
  - name: ','
  - name: " "
  - uid: System.Boolean
    name: bool
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.boolean
  - name: )
  spec.vb:
  - uid: System.Attribute.GetCustomAttributes(System.Reflection.MemberInfo,System.Type,System.Boolean)
    name: GetCustomAttributes
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.attribute.getcustomattributes#system-attribute-getcustomattributes(system-reflection-memberinfo-system-type-system-boolean)
  - name: (
  - uid: System.Reflection.MemberInfo
    name: MemberInfo
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.reflection.memberinfo
  - name: ','
  - name: " "
  - uid: System.Type
    name: Type
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.type
  - name: ','
  - name: " "
  - uid: System.Boolean
    name: Boolean
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.boolean
  - name: )
- uid: System.Attribute.GetCustomAttributes(System.Reflection.Module)
  commentId: M:System.Attribute.GetCustomAttributes(System.Reflection.Module)
  parent: System.Attribute
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.attribute.getcustomattributes#system-attribute-getcustomattributes(system-reflection-module)
  name: GetCustomAttributes(Module)
  nameWithType: Attribute.GetCustomAttributes(Module)
  fullName: System.Attribute.GetCustomAttributes(System.Reflection.Module)
  spec.csharp:
  - uid: System.Attribute.GetCustomAttributes(System.Reflection.Module)
    name: GetCustomAttributes
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.attribute.getcustomattributes#system-attribute-getcustomattributes(system-reflection-module)
  - name: (
  - uid: System.Reflection.Module
    name: Module
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.reflection.module
  - name: )
  spec.vb:
  - uid: System.Attribute.GetCustomAttributes(System.Reflection.Module)
    name: GetCustomAttributes
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.attribute.getcustomattributes#system-attribute-getcustomattributes(system-reflection-module)
  - name: (
  - uid: System.Reflection.Module
    name: Module
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.reflection.module
  - name: )
- uid: System.Attribute.GetCustomAttributes(System.Reflection.Module,System.Boolean)
  commentId: M:System.Attribute.GetCustomAttributes(System.Reflection.Module,System.Boolean)
  parent: System.Attribute
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.attribute.getcustomattributes#system-attribute-getcustomattributes(system-reflection-module-system-boolean)
  name: GetCustomAttributes(Module, bool)
  nameWithType: Attribute.GetCustomAttributes(Module, bool)
  fullName: System.Attribute.GetCustomAttributes(System.Reflection.Module, bool)
  nameWithType.vb: Attribute.GetCustomAttributes(Module, Boolean)
  fullName.vb: System.Attribute.GetCustomAttributes(System.Reflection.Module, Boolean)
  name.vb: GetCustomAttributes(Module, Boolean)
  spec.csharp:
  - uid: System.Attribute.GetCustomAttributes(System.Reflection.Module,System.Boolean)
    name: GetCustomAttributes
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.attribute.getcustomattributes#system-attribute-getcustomattributes(system-reflection-module-system-boolean)
  - name: (
  - uid: System.Reflection.Module
    name: Module
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.reflection.module
  - name: ','
  - name: " "
  - uid: System.Boolean
    name: bool
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.boolean
  - name: )
  spec.vb:
  - uid: System.Attribute.GetCustomAttributes(System.Reflection.Module,System.Boolean)
    name: GetCustomAttributes
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.attribute.getcustomattributes#system-attribute-getcustomattributes(system-reflection-module-system-boolean)
  - name: (
  - uid: System.Reflection.Module
    name: Module
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.reflection.module
  - name: ','
  - name: " "
  - uid: System.Boolean
    name: Boolean
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.boolean
  - name: )
- uid: System.Attribute.GetCustomAttributes(System.Reflection.Module,System.Type)
  commentId: M:System.Attribute.GetCustomAttributes(System.Reflection.Module,System.Type)
  parent: System.Attribute
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.attribute.getcustomattributes#system-attribute-getcustomattributes(system-reflection-module-system-type)
  name: GetCustomAttributes(Module, Type)
  nameWithType: Attribute.GetCustomAttributes(Module, Type)
  fullName: System.Attribute.GetCustomAttributes(System.Reflection.Module, System.Type)
  spec.csharp:
  - uid: System.Attribute.GetCustomAttributes(System.Reflection.Module,System.Type)
    name: GetCustomAttributes
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.attribute.getcustomattributes#system-attribute-getcustomattributes(system-reflection-module-system-type)
  - name: (
  - uid: System.Reflection.Module
    name: Module
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.reflection.module
  - name: ','
  - name: " "
  - uid: System.Type
    name: Type
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.type
  - name: )
  spec.vb:
  - uid: System.Attribute.GetCustomAttributes(System.Reflection.Module,System.Type)
    name: GetCustomAttributes
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.attribute.getcustomattributes#system-attribute-getcustomattributes(system-reflection-module-system-type)
  - name: (
  - uid: System.Reflection.Module
    name: Module
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.reflection.module
  - name: ','
  - name: " "
  - uid: System.Type
    name: Type
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.type
  - name: )
- uid: System.Attribute.GetCustomAttributes(System.Reflection.Module,System.Type,System.Boolean)
  commentId: M:System.Attribute.GetCustomAttributes(System.Reflection.Module,System.Type,System.Boolean)
  parent: System.Attribute
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.attribute.getcustomattributes#system-attribute-getcustomattributes(system-reflection-module-system-type-system-boolean)
  name: GetCustomAttributes(Module, Type, bool)
  nameWithType: Attribute.GetCustomAttributes(Module, Type, bool)
  fullName: System.Attribute.GetCustomAttributes(System.Reflection.Module, System.Type, bool)
  nameWithType.vb: Attribute.GetCustomAttributes(Module, Type, Boolean)
  fullName.vb: System.Attribute.GetCustomAttributes(System.Reflection.Module, System.Type, Boolean)
  name.vb: GetCustomAttributes(Module, Type, Boolean)
  spec.csharp:
  - uid: System.Attribute.GetCustomAttributes(System.Reflection.Module,System.Type,System.Boolean)
    name: GetCustomAttributes
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.attribute.getcustomattributes#system-attribute-getcustomattributes(system-reflection-module-system-type-system-boolean)
  - name: (
  - uid: System.Reflection.Module
    name: Module
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.reflection.module
  - name: ','
  - name: " "
  - uid: System.Type
    name: Type
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.type
  - name: ','
  - name: " "
  - uid: System.Boolean
    name: bool
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.boolean
  - name: )
  spec.vb:
  - uid: System.Attribute.GetCustomAttributes(System.Reflection.Module,System.Type,System.Boolean)
    name: GetCustomAttributes
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.attribute.getcustomattributes#system-attribute-getcustomattributes(system-reflection-module-system-type-system-boolean)
  - name: (
  - uid: System.Reflection.Module
    name: Module
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.reflection.module
  - name: ','
  - name: " "
  - uid: System.Type
    name: Type
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.type
  - name: ','
  - name: " "
  - uid: System.Boolean
    name: Boolean
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.boolean
  - name: )
- uid: System.Attribute.GetCustomAttributes(System.Reflection.ParameterInfo)
  commentId: M:System.Attribute.GetCustomAttributes(System.Reflection.ParameterInfo)
  parent: System.Attribute
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.attribute.getcustomattributes#system-attribute-getcustomattributes(system-reflection-parameterinfo)
  name: GetCustomAttributes(ParameterInfo)
  nameWithType: Attribute.GetCustomAttributes(ParameterInfo)
  fullName: System.Attribute.GetCustomAttributes(System.Reflection.ParameterInfo)
  spec.csharp:
  - uid: System.Attribute.GetCustomAttributes(System.Reflection.ParameterInfo)
    name: GetCustomAttributes
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.attribute.getcustomattributes#system-attribute-getcustomattributes(system-reflection-parameterinfo)
  - name: (
  - uid: System.Reflection.ParameterInfo
    name: ParameterInfo
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.reflection.parameterinfo
  - name: )
  spec.vb:
  - uid: System.Attribute.GetCustomAttributes(System.Reflection.ParameterInfo)
    name: GetCustomAttributes
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.attribute.getcustomattributes#system-attribute-getcustomattributes(system-reflection-parameterinfo)
  - name: (
  - uid: System.Reflection.ParameterInfo
    name: ParameterInfo
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.reflection.parameterinfo
  - name: )
- uid: System.Attribute.GetCustomAttributes(System.Reflection.ParameterInfo,System.Boolean)
  commentId: M:System.Attribute.GetCustomAttributes(System.Reflection.ParameterInfo,System.Boolean)
  parent: System.Attribute
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.attribute.getcustomattributes#system-attribute-getcustomattributes(system-reflection-parameterinfo-system-boolean)
  name: GetCustomAttributes(ParameterInfo, bool)
  nameWithType: Attribute.GetCustomAttributes(ParameterInfo, bool)
  fullName: System.Attribute.GetCustomAttributes(System.Reflection.ParameterInfo, bool)
  nameWithType.vb: Attribute.GetCustomAttributes(ParameterInfo, Boolean)
  fullName.vb: System.Attribute.GetCustomAttributes(System.Reflection.ParameterInfo, Boolean)
  name.vb: GetCustomAttributes(ParameterInfo, Boolean)
  spec.csharp:
  - uid: System.Attribute.GetCustomAttributes(System.Reflection.ParameterInfo,System.Boolean)
    name: GetCustomAttributes
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.attribute.getcustomattributes#system-attribute-getcustomattributes(system-reflection-parameterinfo-system-boolean)
  - name: (
  - uid: System.Reflection.ParameterInfo
    name: ParameterInfo
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.reflection.parameterinfo
  - name: ','
  - name: " "
  - uid: System.Boolean
    name: bool
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.boolean
  - name: )
  spec.vb:
  - uid: System.Attribute.GetCustomAttributes(System.Reflection.ParameterInfo,System.Boolean)
    name: GetCustomAttributes
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.attribute.getcustomattributes#system-attribute-getcustomattributes(system-reflection-parameterinfo-system-boolean)
  - name: (
  - uid: System.Reflection.ParameterInfo
    name: ParameterInfo
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.reflection.parameterinfo
  - name: ','
  - name: " "
  - uid: System.Boolean
    name: Boolean
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.boolean
  - name: )
- uid: System.Attribute.GetCustomAttributes(System.Reflection.ParameterInfo,System.Type)
  commentId: M:System.Attribute.GetCustomAttributes(System.Reflection.ParameterInfo,System.Type)
  parent: System.Attribute
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.attribute.getcustomattributes#system-attribute-getcustomattributes(system-reflection-parameterinfo-system-type)
  name: GetCustomAttributes(ParameterInfo, Type)
  nameWithType: Attribute.GetCustomAttributes(ParameterInfo, Type)
  fullName: System.Attribute.GetCustomAttributes(System.Reflection.ParameterInfo, System.Type)
  spec.csharp:
  - uid: System.Attribute.GetCustomAttributes(System.Reflection.ParameterInfo,System.Type)
    name: GetCustomAttributes
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.attribute.getcustomattributes#system-attribute-getcustomattributes(system-reflection-parameterinfo-system-type)
  - name: (
  - uid: System.Reflection.ParameterInfo
    name: ParameterInfo
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.reflection.parameterinfo
  - name: ','
  - name: " "
  - uid: System.Type
    name: Type
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.type
  - name: )
  spec.vb:
  - uid: System.Attribute.GetCustomAttributes(System.Reflection.ParameterInfo,System.Type)
    name: GetCustomAttributes
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.attribute.getcustomattributes#system-attribute-getcustomattributes(system-reflection-parameterinfo-system-type)
  - name: (
  - uid: System.Reflection.ParameterInfo
    name: ParameterInfo
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.reflection.parameterinfo
  - name: ','
  - name: " "
  - uid: System.Type
    name: Type
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.type
  - name: )
- uid: System.Attribute.GetCustomAttributes(System.Reflection.ParameterInfo,System.Type,System.Boolean)
  commentId: M:System.Attribute.GetCustomAttributes(System.Reflection.ParameterInfo,System.Type,System.Boolean)
  parent: System.Attribute
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.attribute.getcustomattributes#system-attribute-getcustomattributes(system-reflection-parameterinfo-system-type-system-boolean)
  name: GetCustomAttributes(ParameterInfo, Type, bool)
  nameWithType: Attribute.GetCustomAttributes(ParameterInfo, Type, bool)
  fullName: System.Attribute.GetCustomAttributes(System.Reflection.ParameterInfo, System.Type, bool)
  nameWithType.vb: Attribute.GetCustomAttributes(ParameterInfo, Type, Boolean)
  fullName.vb: System.Attribute.GetCustomAttributes(System.Reflection.ParameterInfo, System.Type, Boolean)
  name.vb: GetCustomAttributes(ParameterInfo, Type, Boolean)
  spec.csharp:
  - uid: System.Attribute.GetCustomAttributes(System.Reflection.ParameterInfo,System.Type,System.Boolean)
    name: GetCustomAttributes
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.attribute.getcustomattributes#system-attribute-getcustomattributes(system-reflection-parameterinfo-system-type-system-boolean)
  - name: (
  - uid: System.Reflection.ParameterInfo
    name: ParameterInfo
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.reflection.parameterinfo
  - name: ','
  - name: " "
  - uid: System.Type
    name: Type
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.type
  - name: ','
  - name: " "
  - uid: System.Boolean
    name: bool
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.boolean
  - name: )
  spec.vb:
  - uid: System.Attribute.GetCustomAttributes(System.Reflection.ParameterInfo,System.Type,System.Boolean)
    name: GetCustomAttributes
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.attribute.getcustomattributes#system-attribute-getcustomattributes(system-reflection-parameterinfo-system-type-system-boolean)
  - name: (
  - uid: System.Reflection.ParameterInfo
    name: ParameterInfo
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.reflection.parameterinfo
  - name: ','
  - name: " "
  - uid: System.Type
    name: Type
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.type
  - name: ','
  - name: " "
  - uid: System.Boolean
    name: Boolean
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.boolean
  - name: )
- uid: System.Attribute.GetHashCode
  commentId: M:System.Attribute.GetHashCode
  parent: System.Attribute
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.attribute.gethashcode
  name: GetHashCode()
  nameWithType: Attribute.GetHashCode()
  fullName: System.Attribute.GetHashCode()
  spec.csharp:
  - uid: System.Attribute.GetHashCode
    name: GetHashCode
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.attribute.gethashcode
  - name: (
  - name: )
  spec.vb:
  - uid: System.Attribute.GetHashCode
    name: GetHashCode
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.attribute.gethashcode
  - name: (
  - name: )
- uid: System.Attribute.IsDefaultAttribute
  commentId: M:System.Attribute.IsDefaultAttribute
  parent: System.Attribute
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.attribute.isdefaultattribute
  name: IsDefaultAttribute()
  nameWithType: Attribute.IsDefaultAttribute()
  fullName: System.Attribute.IsDefaultAttribute()
  spec.csharp:
  - uid: System.Attribute.IsDefaultAttribute
    name: IsDefaultAttribute
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.attribute.isdefaultattribute
  - name: (
  - name: )
  spec.vb:
  - uid: System.Attribute.IsDefaultAttribute
    name: IsDefaultAttribute
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.attribute.isdefaultattribute
  - name: (
  - name: )
- uid: System.Attribute.IsDefined(System.Reflection.Assembly,System.Type)
  commentId: M:System.Attribute.IsDefined(System.Reflection.Assembly,System.Type)
  parent: System.Attribute
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.attribute.isdefined#system-attribute-isdefined(system-reflection-assembly-system-type)
  name: IsDefined(Assembly, Type)
  nameWithType: Attribute.IsDefined(Assembly, Type)
  fullName: System.Attribute.IsDefined(System.Reflection.Assembly, System.Type)
  spec.csharp:
  - uid: System.Attribute.IsDefined(System.Reflection.Assembly,System.Type)
    name: IsDefined
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.attribute.isdefined#system-attribute-isdefined(system-reflection-assembly-system-type)
  - name: (
  - uid: System.Reflection.Assembly
    name: Assembly
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.reflection.assembly
  - name: ','
  - name: " "
  - uid: System.Type
    name: Type
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.type
  - name: )
  spec.vb:
  - uid: System.Attribute.IsDefined(System.Reflection.Assembly,System.Type)
    name: IsDefined
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.attribute.isdefined#system-attribute-isdefined(system-reflection-assembly-system-type)
  - name: (
  - uid: System.Reflection.Assembly
    name: Assembly
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.reflection.assembly
  - name: ','
  - name: " "
  - uid: System.Type
    name: Type
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.type
  - name: )
- uid: System.Attribute.IsDefined(System.Reflection.Assembly,System.Type,System.Boolean)
  commentId: M:System.Attribute.IsDefined(System.Reflection.Assembly,System.Type,System.Boolean)
  parent: System.Attribute
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.attribute.isdefined#system-attribute-isdefined(system-reflection-assembly-system-type-system-boolean)
  name: IsDefined(Assembly, Type, bool)
  nameWithType: Attribute.IsDefined(Assembly, Type, bool)
  fullName: System.Attribute.IsDefined(System.Reflection.Assembly, System.Type, bool)
  nameWithType.vb: Attribute.IsDefined(Assembly, Type, Boolean)
  fullName.vb: System.Attribute.IsDefined(System.Reflection.Assembly, System.Type, Boolean)
  name.vb: IsDefined(Assembly, Type, Boolean)
  spec.csharp:
  - uid: System.Attribute.IsDefined(System.Reflection.Assembly,System.Type,System.Boolean)
    name: IsDefined
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.attribute.isdefined#system-attribute-isdefined(system-reflection-assembly-system-type-system-boolean)
  - name: (
  - uid: System.Reflection.Assembly
    name: Assembly
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.reflection.assembly
  - name: ','
  - name: " "
  - uid: System.Type
    name: Type
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.type
  - name: ','
  - name: " "
  - uid: System.Boolean
    name: bool
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.boolean
  - name: )
  spec.vb:
  - uid: System.Attribute.IsDefined(System.Reflection.Assembly,System.Type,System.Boolean)
    name: IsDefined
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.attribute.isdefined#system-attribute-isdefined(system-reflection-assembly-system-type-system-boolean)
  - name: (
  - uid: System.Reflection.Assembly
    name: Assembly
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.reflection.assembly
  - name: ','
  - name: " "
  - uid: System.Type
    name: Type
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.type
  - name: ','
  - name: " "
  - uid: System.Boolean
    name: Boolean
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.boolean
  - name: )
- uid: System.Attribute.IsDefined(System.Reflection.MemberInfo,System.Type)
  commentId: M:System.Attribute.IsDefined(System.Reflection.MemberInfo,System.Type)
  parent: System.Attribute
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.attribute.isdefined#system-attribute-isdefined(system-reflection-memberinfo-system-type)
  name: IsDefined(MemberInfo, Type)
  nameWithType: Attribute.IsDefined(MemberInfo, Type)
  fullName: System.Attribute.IsDefined(System.Reflection.MemberInfo, System.Type)
  spec.csharp:
  - uid: System.Attribute.IsDefined(System.Reflection.MemberInfo,System.Type)
    name: IsDefined
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.attribute.isdefined#system-attribute-isdefined(system-reflection-memberinfo-system-type)
  - name: (
  - uid: System.Reflection.MemberInfo
    name: MemberInfo
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.reflection.memberinfo
  - name: ','
  - name: " "
  - uid: System.Type
    name: Type
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.type
  - name: )
  spec.vb:
  - uid: System.Attribute.IsDefined(System.Reflection.MemberInfo,System.Type)
    name: IsDefined
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.attribute.isdefined#system-attribute-isdefined(system-reflection-memberinfo-system-type)
  - name: (
  - uid: System.Reflection.MemberInfo
    name: MemberInfo
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.reflection.memberinfo
  - name: ','
  - name: " "
  - uid: System.Type
    name: Type
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.type
  - name: )
- uid: System.Attribute.IsDefined(System.Reflection.MemberInfo,System.Type,System.Boolean)
  commentId: M:System.Attribute.IsDefined(System.Reflection.MemberInfo,System.Type,System.Boolean)
  parent: System.Attribute
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.attribute.isdefined#system-attribute-isdefined(system-reflection-memberinfo-system-type-system-boolean)
  name: IsDefined(MemberInfo, Type, bool)
  nameWithType: Attribute.IsDefined(MemberInfo, Type, bool)
  fullName: System.Attribute.IsDefined(System.Reflection.MemberInfo, System.Type, bool)
  nameWithType.vb: Attribute.IsDefined(MemberInfo, Type, Boolean)
  fullName.vb: System.Attribute.IsDefined(System.Reflection.MemberInfo, System.Type, Boolean)
  name.vb: IsDefined(MemberInfo, Type, Boolean)
  spec.csharp:
  - uid: System.Attribute.IsDefined(System.Reflection.MemberInfo,System.Type,System.Boolean)
    name: IsDefined
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.attribute.isdefined#system-attribute-isdefined(system-reflection-memberinfo-system-type-system-boolean)
  - name: (
  - uid: System.Reflection.MemberInfo
    name: MemberInfo
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.reflection.memberinfo
  - name: ','
  - name: " "
  - uid: System.Type
    name: Type
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.type
  - name: ','
  - name: " "
  - uid: System.Boolean
    name: bool
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.boolean
  - name: )
  spec.vb:
  - uid: System.Attribute.IsDefined(System.Reflection.MemberInfo,System.Type,System.Boolean)
    name: IsDefined
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.attribute.isdefined#system-attribute-isdefined(system-reflection-memberinfo-system-type-system-boolean)
  - name: (
  - uid: System.Reflection.MemberInfo
    name: MemberInfo
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.reflection.memberinfo
  - name: ','
  - name: " "
  - uid: System.Type
    name: Type
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.type
  - name: ','
  - name: " "
  - uid: System.Boolean
    name: Boolean
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.boolean
  - name: )
- uid: System.Attribute.IsDefined(System.Reflection.Module,System.Type)
  commentId: M:System.Attribute.IsDefined(System.Reflection.Module,System.Type)
  parent: System.Attribute
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.attribute.isdefined#system-attribute-isdefined(system-reflection-module-system-type)
  name: IsDefined(Module, Type)
  nameWithType: Attribute.IsDefined(Module, Type)
  fullName: System.Attribute.IsDefined(System.Reflection.Module, System.Type)
  spec.csharp:
  - uid: System.Attribute.IsDefined(System.Reflection.Module,System.Type)
    name: IsDefined
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.attribute.isdefined#system-attribute-isdefined(system-reflection-module-system-type)
  - name: (
  - uid: System.Reflection.Module
    name: Module
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.reflection.module
  - name: ','
  - name: " "
  - uid: System.Type
    name: Type
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.type
  - name: )
  spec.vb:
  - uid: System.Attribute.IsDefined(System.Reflection.Module,System.Type)
    name: IsDefined
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.attribute.isdefined#system-attribute-isdefined(system-reflection-module-system-type)
  - name: (
  - uid: System.Reflection.Module
    name: Module
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.reflection.module
  - name: ','
  - name: " "
  - uid: System.Type
    name: Type
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.type
  - name: )
- uid: System.Attribute.IsDefined(System.Reflection.Module,System.Type,System.Boolean)
  commentId: M:System.Attribute.IsDefined(System.Reflection.Module,System.Type,System.Boolean)
  parent: System.Attribute
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.attribute.isdefined#system-attribute-isdefined(system-reflection-module-system-type-system-boolean)
  name: IsDefined(Module, Type, bool)
  nameWithType: Attribute.IsDefined(Module, Type, bool)
  fullName: System.Attribute.IsDefined(System.Reflection.Module, System.Type, bool)
  nameWithType.vb: Attribute.IsDefined(Module, Type, Boolean)
  fullName.vb: System.Attribute.IsDefined(System.Reflection.Module, System.Type, Boolean)
  name.vb: IsDefined(Module, Type, Boolean)
  spec.csharp:
  - uid: System.Attribute.IsDefined(System.Reflection.Module,System.Type,System.Boolean)
    name: IsDefined
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.attribute.isdefined#system-attribute-isdefined(system-reflection-module-system-type-system-boolean)
  - name: (
  - uid: System.Reflection.Module
    name: Module
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.reflection.module
  - name: ','
  - name: " "
  - uid: System.Type
    name: Type
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.type
  - name: ','
  - name: " "
  - uid: System.Boolean
    name: bool
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.boolean
  - name: )
  spec.vb:
  - uid: System.Attribute.IsDefined(System.Reflection.Module,System.Type,System.Boolean)
    name: IsDefined
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.attribute.isdefined#system-attribute-isdefined(system-reflection-module-system-type-system-boolean)
  - name: (
  - uid: System.Reflection.Module
    name: Module
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.reflection.module
  - name: ','
  - name: " "
  - uid: System.Type
    name: Type
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.type
  - name: ','
  - name: " "
  - uid: System.Boolean
    name: Boolean
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.boolean
  - name: )
- uid: System.Attribute.IsDefined(System.Reflection.ParameterInfo,System.Type)
  commentId: M:System.Attribute.IsDefined(System.Reflection.ParameterInfo,System.Type)
  parent: System.Attribute
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.attribute.isdefined#system-attribute-isdefined(system-reflection-parameterinfo-system-type)
  name: IsDefined(ParameterInfo, Type)
  nameWithType: Attribute.IsDefined(ParameterInfo, Type)
  fullName: System.Attribute.IsDefined(System.Reflection.ParameterInfo, System.Type)
  spec.csharp:
  - uid: System.Attribute.IsDefined(System.Reflection.ParameterInfo,System.Type)
    name: IsDefined
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.attribute.isdefined#system-attribute-isdefined(system-reflection-parameterinfo-system-type)
  - name: (
  - uid: System.Reflection.ParameterInfo
    name: ParameterInfo
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.reflection.parameterinfo
  - name: ','
  - name: " "
  - uid: System.Type
    name: Type
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.type
  - name: )
  spec.vb:
  - uid: System.Attribute.IsDefined(System.Reflection.ParameterInfo,System.Type)
    name: IsDefined
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.attribute.isdefined#system-attribute-isdefined(system-reflection-parameterinfo-system-type)
  - name: (
  - uid: System.Reflection.ParameterInfo
    name: ParameterInfo
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.reflection.parameterinfo
  - name: ','
  - name: " "
  - uid: System.Type
    name: Type
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.type
  - name: )
- uid: System.Attribute.IsDefined(System.Reflection.ParameterInfo,System.Type,System.Boolean)
  commentId: M:System.Attribute.IsDefined(System.Reflection.ParameterInfo,System.Type,System.Boolean)
  parent: System.Attribute
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.attribute.isdefined#system-attribute-isdefined(system-reflection-parameterinfo-system-type-system-boolean)
  name: IsDefined(ParameterInfo, Type, bool)
  nameWithType: Attribute.IsDefined(ParameterInfo, Type, bool)
  fullName: System.Attribute.IsDefined(System.Reflection.ParameterInfo, System.Type, bool)
  nameWithType.vb: Attribute.IsDefined(ParameterInfo, Type, Boolean)
  fullName.vb: System.Attribute.IsDefined(System.Reflection.ParameterInfo, System.Type, Boolean)
  name.vb: IsDefined(ParameterInfo, Type, Boolean)
  spec.csharp:
  - uid: System.Attribute.IsDefined(System.Reflection.ParameterInfo,System.Type,System.Boolean)
    name: IsDefined
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.attribute.isdefined#system-attribute-isdefined(system-reflection-parameterinfo-system-type-system-boolean)
  - name: (
  - uid: System.Reflection.ParameterInfo
    name: ParameterInfo
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.reflection.parameterinfo
  - name: ','
  - name: " "
  - uid: System.Type
    name: Type
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.type
  - name: ','
  - name: " "
  - uid: System.Boolean
    name: bool
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.boolean
  - name: )
  spec.vb:
  - uid: System.Attribute.IsDefined(System.Reflection.ParameterInfo,System.Type,System.Boolean)
    name: IsDefined
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.attribute.isdefined#system-attribute-isdefined(system-reflection-parameterinfo-system-type-system-boolean)
  - name: (
  - uid: System.Reflection.ParameterInfo
    name: ParameterInfo
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.reflection.parameterinfo
  - name: ','
  - name: " "
  - uid: System.Type
    name: Type
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.type
  - name: ','
  - name: " "
  - uid: System.Boolean
    name: Boolean
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.boolean
  - name: )
- uid: System.Attribute.Match(System.Object)
  commentId: M:System.Attribute.Match(System.Object)
  parent: System.Attribute
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.attribute.match
  name: Match(object)
  nameWithType: Attribute.Match(object)
  fullName: System.Attribute.Match(object)
  nameWithType.vb: Attribute.Match(Object)
  fullName.vb: System.Attribute.Match(Object)
  name.vb: Match(Object)
  spec.csharp:
  - uid: System.Attribute.Match(System.Object)
    name: Match
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.attribute.match
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Attribute.Match(System.Object)
    name: Match
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.attribute.match
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Attribute.TypeId
  commentId: P:System.Attribute.TypeId
  parent: System.Attribute
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.attribute.typeid
  name: TypeId
  nameWithType: Attribute.TypeId
  fullName: System.Attribute.TypeId
- uid: System.Object.Equals(System.Object,System.Object)
  commentId: M:System.Object.Equals(System.Object,System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  name: Equals(object, object)
  nameWithType: object.Equals(object, object)
  fullName: object.Equals(object, object)
  nameWithType.vb: Object.Equals(Object, Object)
  fullName.vb: Object.Equals(Object, Object)
  name.vb: Equals(Object, Object)
  spec.csharp:
  - uid: System.Object.Equals(System.Object,System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.Equals(System.Object,System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.GetType
  commentId: M:System.Object.GetType
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  name: GetType()
  nameWithType: object.GetType()
  fullName: object.GetType()
  nameWithType.vb: Object.GetType()
  fullName.vb: Object.GetType()
  spec.csharp:
  - uid: System.Object.GetType
    name: GetType
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.GetType
    name: GetType
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  - name: (
  - name: )
- uid: System.Object.ReferenceEquals(System.Object,System.Object)
  commentId: M:System.Object.ReferenceEquals(System.Object,System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  name: ReferenceEquals(object, object)
  nameWithType: object.ReferenceEquals(object, object)
  fullName: object.ReferenceEquals(object, object)
  nameWithType.vb: Object.ReferenceEquals(Object, Object)
  fullName.vb: Object.ReferenceEquals(Object, Object)
  name.vb: ReferenceEquals(Object, Object)
  spec.csharp:
  - uid: System.Object.ReferenceEquals(System.Object,System.Object)
    name: ReferenceEquals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.ReferenceEquals(System.Object,System.Object)
    name: ReferenceEquals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.ToString
  commentId: M:System.Object.ToString
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.tostring
  name: ToString()
  nameWithType: object.ToString()
  fullName: object.ToString()
  nameWithType.vb: Object.ToString()
  fullName.vb: Object.ToString()
  spec.csharp:
  - uid: System.Object.ToString
    name: ToString
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.tostring
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.ToString
    name: ToString
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.tostring
  - name: (
  - name: )
- uid: System
  commentId: N:System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system
  name: System
  nameWithType: System
  fullName: System
- uid: HotPreview.PreviewCommandAttribute.#ctor*
  commentId: Overload:HotPreview.PreviewCommandAttribute.#ctor
  href: HotPreview.PreviewCommandAttribute.html#HotPreview_PreviewCommandAttribute__ctor_System_String_
  name: PreviewCommandAttribute
  nameWithType: PreviewCommandAttribute.PreviewCommandAttribute
  fullName: HotPreview.PreviewCommandAttribute.PreviewCommandAttribute
  nameWithType.vb: PreviewCommandAttribute.New
  fullName.vb: HotPreview.PreviewCommandAttribute.New
  name.vb: New
- uid: System.String
  commentId: T:System.String
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.string
  name: string
  nameWithType: string
  fullName: string
  nameWithType.vb: String
  fullName.vb: String
  name.vb: String
- uid: HotPreview.PreviewCommandAttribute.DisplayName*
  commentId: Overload:HotPreview.PreviewCommandAttribute.DisplayName
  href: HotPreview.PreviewCommandAttribute.html#HotPreview_PreviewCommandAttribute_DisplayName
  name: DisplayName
  nameWithType: PreviewCommandAttribute.DisplayName
  fullName: HotPreview.PreviewCommandAttribute.DisplayName
